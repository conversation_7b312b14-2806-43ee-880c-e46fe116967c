{"main": {"id": "997c0a544050f798", "type": "split", "children": [{"id": "3e36c316198e60c8", "type": "tabs", "children": [{"id": "d88657fe851d67ac", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}]}], "direction": "vertical"}, "left": {"id": "42ef2c96b14468d5", "type": "split", "children": [{"id": "a942b1584a833606", "type": "tabs", "children": [{"id": "c69f6450765d35e0", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "c747cd1ec44b6a61", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "beb31de800d32435", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 363.5}, "right": {"id": "86aeabaf0c8eefe2", "type": "split", "children": [{"id": "7ccf15ba6a82b0f9", "type": "tabs", "children": [{"id": "ef6e009b855d0e6e", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks"}}, {"id": "aa9a7699a5ca2141", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links"}}, {"id": "31c7567f08ad2a23", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "3a6ad3b00a4107bc", "type": "leaf", "state": {"type": "outline", "state": {"followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "bases:Create new base": false}}, "active": "d88657fe851d67ac", "lastOpenFiles": ["tests/Feature/SupplierPhoneSeparationTest.php", "tests/Feature/SupplierPhoneNormalizationTest.php", "tests/Feature/SupplierImportTest.php", "tests/Feature/SupplierEnumPhoneTest.php", "tests/Feature/SupplierCountryQueriesTest.php", "tests/Feature/SimpleProductTemplateTest.php", "tests/Feature/ProjectConsolidatedViewTest.php", "tests/Feature/ProjectConsolidatedViewSimpleTest.php", "tests/Feature/ProductVariantTwoColumnAttributesTest.php", "tests/Feature/ProductVariantSingleColumnTest.php", "tests/Feature/ProductVariantReorderedFormTest.php", "plan_augment.md", "mejoras_augment_a_claude.md", "implementation_plan.md", "evaluacion_supernova.md", "especificacion_claude_simple_final.md", "especificacion_claude_simple_corregida.md", "especificacion_claude_simple.md", "especificacion_claude.md", "especificacion_cero_augment.md", "_docus/_clean/plan_implementacion_especificacion_producto.md", "_docus/_clean/listado_taxonomia.md", "_docus/_clean/listado_dimensiones_especificacion.md", "_docus/_clean/detalle_especificacion.md", "_docus/_clean/prompts/Untitled/listado_taxonomia.md", "_docus/_clean/prompts/Untitled/detalle_especificacion.md", "_docus/_clean/prompts/Untitled/listado_dimensiones_especificacion.md", "_docus/_clean/espec_detallada/2_modelo_de_entidades.md", "_docus/_clean/analisis_arquitectura_claude.md", "_docus/_clean/listado.md", "_docus/_clean/arquitectura_final_test_plan.md", "_docus/_clean/taxonomia.md", "_docus/_clean/plantillas_de_producto.md", "_docus/_clean/paso_a_paso.md", "_docus/_clean/nueva_interfaz.md", "_docus/_clean/Guia_Implementacion_Taxonomia_Grupos_Subgrupos.md"]}