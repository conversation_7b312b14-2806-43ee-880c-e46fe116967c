<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Foundation\Auth\User as AuthUser;
use App\Models\ProductSubcategory;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProductSubcategoryPolicy
{
    use HandlesAuthorization;
    
    public function viewAny(AuthUser $authUser): bool
    {
        return $authUser->can('ViewAny:ProductSubcategory');
    }

    public function view(AuthUser $authUser, ProductSubcategory $productSubcategory): bool
    {
        return $authUser->can('View:ProductSubcategory');
    }

    public function create(AuthUser $authUser): bool
    {
        return $authUser->can('Create:ProductSubcategory');
    }

    public function update(AuthUser $authUser, ProductSubcategory $productSubcategory): bool
    {
        return $authUser->can('Update:ProductSubcategory');
    }

    public function delete(AuthUser $authUser, ProductSubcategory $productSubcategory): bool
    {
        return $authUser->can('Delete:ProductSubcategory');
    }

    public function restore(AuthUser $authUser, ProductSubcategory $productSubcategory): bool
    {
        return $authUser->can('Restore:ProductSubcategory');
    }

    public function forceDelete(AuthUser $authUser, ProductSubcategory $productSubcategory): bool
    {
        return $authUser->can('ForceDelete:ProductSubcategory');
    }

    public function forceDeleteAny(AuthUser $authUser): bool
    {
        return $authUser->can('ForceDeleteAny:ProductSubcategory');
    }

    public function restoreAny(AuthUser $authUser): bool
    {
        return $authUser->can('RestoreAny:ProductSubcategory');
    }

    public function replicate(AuthUser $authUser, ProductSubcategory $productSubcategory): bool
    {
        return $authUser->can('Replicate:ProductSubcategory');
    }

    public function reorder(AuthUser $authUser): bool
    {
        return $authUser->can('Reorder:ProductSubcategory');
    }

}