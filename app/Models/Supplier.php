<?php

namespace App\Models;

use App\Enums\CommonStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supplier extends Model
{
    /** @use HasFactory<\Database\Factories\SupplierFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'tax_id',
        'contact_info',
        'status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    public function casts(): array
    {
        return [
            'contact_info' => 'array',
            'status' => CommonStatus::class,
        ];
    }

    /**
     * Get the product projects for the supplier.
     */
    public function productProjects(): HasMany
    {
        return $this->hasMany(ProductProject::class);
    }

    /**
     * Get the product variants that suggest this supplier.
     */
    public function suggestedProductVariants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'suggested_supplier_id');
    }

    /**
     * Get the procurement lots for the supplier.
     */
    public function procurementLots(): HasMany
    {
        return $this->hasMany(ProcurementLot::class);
    }

    /**
     * Get the supplier quotes for the supplier.
     */
    public function supplierQuotes(): HasMany
    {
        return $this->hasMany(SupplierQuote::class);
    }
}
