<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AttributeGroup extends Model
{
    /** @use HasFactory<\Database\Factories\AttributeGroupFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'name_es',
        'name_en',
        'description_es',
        'description_en',
        'icon',
        'position',
        'active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    public function casts(): array
    {
        return [
            'active' => 'boolean',
        ];
    }

    /**
     * Get the template attributes for this group.
     */
    public function productTemplateAttributes(): HasMany
    {
        return $this->hasMany(ProductTemplateAttribute::class);
    }
}
