<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AttributeGroup extends Model
{
    /** @use HasFactory<\Database\Factories\AttributeGroupFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'dimension_id',
        'name',
        'key',
        'description',
        'sort_order',
        'is_required',
        'status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    public function casts(): array
    {
        return [
            'is_required' => 'boolean',
        ];
    }

    /**
     * Get the dimension that owns the attribute group.
     */
    public function dimension(): BelongsTo
    {
        return $this->belongsTo(Dimension::class);
    }

    /**
     * Get the attributes for this group.
     */
    public function attributes(): HasMany
    {
        return $this->hasMany(Attribute::class)->orderBy('sort_order');
    }

    /**
     * Get the active attributes for this group.
     */
    public function activeAttributes(): HasMany
    {
        return $this->hasMany(Attribute::class)
            ->where('status', 'active')
            ->orderBy('sort_order');
    }

    /**
     * Get the product template that owns the attribute group.
     */
    public function productTemplate(): BelongsTo
    {
        return $this->belongsTo(ProductTemplate::class);
    }

    /**
     * Get validation rules for creating/updating attribute groups.
     *
     * @return array<string, string>
     */
    public static function rules(): array
    {
        return [
            'key' => 'required|string|max:100|unique:attribute_groups,key',
            'dimension_id' => 'required|exists:dimensions,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'integer|min:0',
            'is_required' => 'boolean',
            'status' => 'required|in:active,inactive',
        ];
    }

    /**
     * Get validation rules for updating (excluding unique constraint on key).
     *
     * @return array<string, string>
     */
    public static function updateRules(int $id): array
    {
        return [
            'key' => 'required|string|max:100|unique:attribute_groups,key,' . $id,
            'dimension_id' => 'required|exists:dimensions,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'integer|min:0',
            'is_required' => 'boolean',
            'status' => 'required|in:active,inactive',
        ];
    }
}
