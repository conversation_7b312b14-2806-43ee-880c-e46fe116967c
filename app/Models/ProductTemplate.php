<?php

namespace App\Models;

use App\Enums\ProductTemplateStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductTemplate extends Model
{
    /** @use HasFactory<\Database\Factories\ProductTemplateFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'product_subcategory_id',
        'status',
        'description',
        'metadata',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    public function casts(): array
    {
        return [
            'status' => ProductTemplateStatus::class,
            'description' => 'array',
            'metadata' => 'array',
        ];
    }

    /**
     * Get the subcategory that owns the template.
     */
    public function productSubcategory(): BelongsTo
    {
        return $this->belongsTo(ProductSubcategory::class);
    }

    /**
     * Get the product projects for the template.
     */
    public function productProjects(): HasMany
    {
        return $this->hasMany(ProductProject::class);
    }

    /**
     * Get the template attributes.
     */
    public function productTemplateAttributes(): HasMany
    {
        return $this->hasMany(ProductTemplateAttribute::class);
    }

    /**
     * Get the template versions.
     */
    public function templateVersions(): HasMany
    {
        return $this->hasMany(TemplateVersion::class);
    }
}
