<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Attribute extends Model
{
    /** @use HasFactory<\Database\Factories\AttributeFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'type',
        'options_json',
        'label_es',
        'label_en',
        'active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    public function casts(): array
    {
        return [
            'options_json' => 'array',
            'active' => 'boolean',
        ];
    }

    /**
     * Get the template attributes for this attribute.
     */
    public function productTemplateAttributes(): HasMany
    {
        return $this->hasMany(ProductTemplateAttribute::class);
    }
}
