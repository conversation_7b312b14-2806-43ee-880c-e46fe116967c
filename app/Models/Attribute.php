<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Attribute extends Model
{
    /** @use HasFactory<\Database\Factories\AttributeFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'attribute_group_id',
        'name',
        'key',
        'description',
        'type',
        'unit',
        'validation_rules',
        'options',
        'default_value',
        'default_placeholder',
        'is_required',
        'sort_order',
        'status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    public function casts(): array
    {
        return [
            'validation_rules' => 'array',
            'options' => 'array',
            'default_placeholder' => 'string',
            'is_required' => 'boolean',
        ];
    }

    /**
     * Get the attribute group that owns the attribute.
     */
    public function attributeGroup(): BelongsTo
    {
        return $this->belongsTo(AttributeGroup::class);
    }

    /**
     * Get the product variant attribute values for this attribute.
     */
    public function productVariantAttributeValues(): HasMany
    {
        return $this->hasMany(ProductVariantAttributeValue::class);
    }

    /**
     * Get the display name with description for dropdowns.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->description) {
            return "{$this->name} ({$this->description})";
        }

        return $this->name;
    }

    /**
     * Get validation rules for creating/updating attributes.
     *
     * @return array<string, string>
     */
    public static function rules(): array
    {
        return [
            'key' => 'required|string|max:100|unique:attributes,key',
            'attribute_group_id' => 'required|exists:attribute_groups,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:string,number,boolean,array,json,select,multiselect,date',
            'unit' => 'nullable|string|max:32',
            'validation_rules' => 'nullable|array',
            'options' => 'nullable|array',
            'default_value' => 'nullable|string',
            'is_required' => 'boolean',
            'sort_order' => 'integer|min:0',
            'status' => 'required|in:active,inactive',
        ];
    }

    /**
     * Get validation rules for updating (excluding unique constraint on key).
     *
     * @return array<string, string>
     */
    public static function updateRules(int $id): array
    {
        return [
            'key' => 'required|string|max:100|unique:attributes,key,' . $id,
            'attribute_group_id' => 'required|exists:attribute_groups,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:string,number,boolean,array,json,select,multiselect,date',
            'unit' => 'nullable|string|max:32',
            'validation_rules' => 'nullable|array',
            'options' => 'nullable|array',
            'default_value' => 'nullable|string',
            'is_required' => 'boolean',
            'sort_order' => 'integer|min:0',
            'status' => 'required|in:active,inactive',
        ];
    }

    /**
     * Check if the attribute is of JSON type.
     */
    public function isJsonType(): bool
    {
        return $this->type === 'json';
    }

    /**
     * Get validation rules formatted for display.
     */
    public function getValidationRulesForDisplay(): ?string
    {
        if (!$this->isJsonType() || empty($this->validation_rules)) {
            return null;
        }
        return json_encode($this->validation_rules, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    }
}
