<?php

namespace App\Models;

use App\Enums\ProductVariantStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ProductVariant extends Model
{
    /** @use HasFactory<\Database\Factories\ProductVariantFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_project_id',
        'name',
        'quantity',
        'configuration',
        'generated_sku',
        'estimated_unit_price',
        'suggested_supplier_id',
        'delivery_window_start',
        'delivery_window_end',
        'variant_status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    public function casts(): array
    {
        return [
            'quantity' => 'integer',
            'configuration' => 'array',
            'estimated_unit_price' => 'decimal:2',
            'delivery_window_start' => 'date',
            'delivery_window_end' => 'date',
            'variant_status' => ProductVariantStatus::class,
        ];
    }

    /**
     * Get the product project that owns the variant.
     */
    public function productProject(): BelongsTo
    {
        return $this->belongsTo(ProductProject::class);
    }

    /**
     * Get the suggested supplier.
     */
    public function suggestedSupplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'suggested_supplier_id');
    }

    /**
     * Get the procurement lot for the variant.
     */
    public function procurementLot(): HasOne
    {
        return $this->hasOne(ProcurementLot::class);
    }
}
