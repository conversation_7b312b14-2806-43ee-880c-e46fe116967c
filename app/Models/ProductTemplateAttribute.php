<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductTemplateAttribute extends Model
{
    /** @use HasFactory<\Database\Factories\ProductTemplateAttributeFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_template_id',
        'attribute_id',
        'attribute_group_id',
        'is_required',
        'visible',
        'position',
        'ui_component',
        'ui_props',
        'default_value',
        'validation_rules',
        'placeholder',
        'column_span',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    public function casts(): array
    {
        return [
            'is_required' => 'boolean',
            'visible' => 'boolean',
            'ui_props' => 'array',
            'default_value' => 'json',
            'validation_rules' => 'array',
        ];
    }

    /**
     * Get the template that owns this attribute.
     */
    public function productTemplate(): BelongsTo
    {
        return $this->belongsTo(ProductTemplate::class);
    }

    /**
     * Get the attribute.
     */
    public function attribute(): BelongsTo
    {
        return $this->belongsTo(Attribute::class);
    }

    /**
     * Get the attribute group.
     */
    public function attributeGroup(): BelongsTo
    {
        return $this->belongsTo(AttributeGroup::class);
    }
}
