<?php

namespace App\Filament\Resources\ProductSubcategories\Pages;

use App\Filament\Resources\ProductSubcategories\ProductSubcategoryResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListProductSubcategories extends ListRecords
{
    protected static string $resource = ProductSubcategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
