<?php

namespace App\Filament\Resources\ProductSubcategories\Pages;

use App\Filament\Resources\ProductSubcategories\ProductSubcategoryResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditProductSubcategory extends EditRecord
{
    protected static string $resource = ProductSubcategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
