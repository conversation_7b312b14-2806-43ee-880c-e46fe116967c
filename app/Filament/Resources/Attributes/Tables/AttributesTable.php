<?php

namespace App\Filament\Resources\Attributes\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class AttributesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('attributeGroup.name')
                    ->label('Grupo')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->label('Nombre')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('description')
                    ->label('Descripción')
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        return strlen($state) > 50 ? $state : null;
                    }),
                TextColumn::make('type')
                    ->label('Tipo')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'string' => 'gray',
                        'number' => 'blue',
                        'boolean' => 'green',
                        'select' => 'purple',
                        'multiselect' => 'indigo',
                        'date' => 'orange',
                        default => 'gray',
                    }),
                TextColumn::make('is_required')
                    ->label('Requerido')
                    ->badge()
                    ->color(fn (bool $state): string => match ($state) {
                        true => 'success',
                        false => 'gray',
                    })
                    ->formatStateUsing(fn (bool $state): string => $state ? 'Sí' : 'No'),
                TextColumn::make('sort_order')
                    ->label('Orden')
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Estado')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'gray',
                    }),
            ])
            ->filters([
                SelectFilter::make('attribute_group_id')
                    ->label('Grupo')
                    ->relationship('attributeGroup', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('type')
                    ->label('Tipo')
                    ->options([
                        'string' => 'Texto',
                        'number' => 'Número',
                        'boolean' => 'Sí/No',
                        'select' => 'Selección única',
                        'multiselect' => 'Selección múltiple',
                        'date' => 'Fecha',
                    ]),
                SelectFilter::make('status')
                    ->label('Estado')
                    ->options([
                        'active' => 'Activo',
                        'inactive' => 'Inactivo',
                    ]),
            ])
            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
