<?php

namespace App\Filament\Resources\Attributes\Schemas;

use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;

class AttributeForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('attribute_group_id')
                    ->label('Grupo de Atributos')
                    ->relationship('attributeGroup', 'name')
                    ->required()
                    ->searchable()
                    ->preload(),
                TextInput::make('name')
                    ->label('Nombre del Atributo')
                    ->required()
                    ->maxLength(255),
                TextInput::make('description')
                    ->label('Descripción')
                    ->helperText('Breve descripción que aparecerá en los dropdowns (ej: "algodón, metal, etc")')
                    ->maxLength(500),
                Select::make('type')
                    ->label('Tipo de Dato')
                    ->options([
                        'string' => 'Texto',
                        'number' => 'Número',
                        'boolean' => 'Sí/No',
                        'select' => 'Selección única',
                        'multiselect' => 'Selección múltiple',
                        'date' => 'Fecha',
                    ])
                    ->required()
                    ->live(),
                KeyValue::make('options')
                    ->label('Opciones (para select/multiselect)')
                    ->keyLabel('Clave')
                    ->valueLabel('Valor')
                    ->keyPlaceholder('Ej: red, blue, green')
                    ->valuePlaceholder('Ej: Rojo, Azul, Verde')
                    ->addActionLabel('Agregar opción')
                    ->visible(fn (Get $get) => in_array($get('type'), ['select', 'multiselect'])),
                KeyValue::make('validation_rules')
                    ->label('Reglas de Validación')
                    ->keyLabel('Regla')
                    ->valueLabel('Valor')
                    ->keyPlaceholder('Ej: min, max, pattern')
                    ->valuePlaceholder('Ej: 1, 100, ^[A-Z]+$')
                    ->addActionLabel('Agregar regla'),
                TextInput::make('default_value')
                    ->label('Valor por Defecto'),
                TextInput::make('sort_order')
                    ->label('Orden')
                    ->numeric()
                    ->default(0),
                Toggle::make('is_required')
                    ->label('Atributo Requerido')
                    ->default(false),
                Select::make('status')
                    ->label('Estado')
                    ->options([
                        'active' => 'Activo',
                        'inactive' => 'Inactivo',
                    ])
                    ->default('active')
                    ->required(),
            ]);
    }
}
