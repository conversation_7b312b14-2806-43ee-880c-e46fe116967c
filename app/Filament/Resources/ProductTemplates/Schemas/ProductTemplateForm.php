<?php

namespace App\Filament\Resources\ProductTemplates\Schemas;

use App\Enums\ProductTemplateStatus;
use App\Models\Attribute;
use App\Models\Dimension;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Placeholder;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ProductTemplateForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                self::buildBasicInfoSection(),
                ...self::buildAttributeSections(),
            ]);
    }

    private static function buildBasicInfoSection(): Section
    {
        return Section::make('Información Básica')
            ->schema([
                TextInput::make('name')
                    ->label('Nombre de la Plantilla')
                    ->required(),
                Select::make('product_type_id')
                    ->label('Tipo de Producto')
                    ->relationship('productType', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                Select::make('status')
                    ->label('Estado')
                    ->options(ProductTemplateStatus::class)
                    ->default('draft')
                    ->required(),
                Textarea::make('description')
                    ->label('Descripción')
                    ->rows(3)
                    ->columnSpanFull(),
            ])
            ->columns(1)
            ->columnSpanFull();
    }

    /**
     * @return array<int, Section>
     */
    private static function buildAttributeSections(): array
    {
        $sections = [];
        $dimensions = Dimension::with(['attributeGroups.attributes'])->orderBy('sort_order')->get();

        foreach ($dimensions as $dimension) {
            $groupSections = [];
            foreach ($dimension->attributeGroups as $group) {
                $groupSections[] = Section::make($group->name)
                    ->description($group->description)
                    ->schema([
                        Repeater::make("attributes_for_group_{$group->id}")
                            ->label("Atributos de \"{$group->name}\"")
                            ->addActionLabel('Añadir Atributo al Grupo')
                            ->schema([
                                Select::make('attribute_id')
                                    ->label('Atributo')
                                    ->options(function () use ($group) {
                                        return $group->attributes->mapWithKeys(function ($attribute) {
                                            $label = $attribute->name;
                                            $details = array_filter([$attribute->type, $attribute->unit]);
                                            if (! empty($details)) {
                                                $label .= ' (' . implode(', ', $details) . ')';
                                            }
                                            return [$attribute->id => $label];
                                        });
                                    })
                                    ->live() // Make the field reactive
                                    ->searchable()
                                    ->preload()
                                    ->required()
                                    ->distinct()
                                    ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $attribute = Attribute::find($state);
                                            $set('placeholder', $attribute?->default_placeholder);
                                        }
                                    }),

                                Grid::make(3)->schema([
                                    Toggle::make('is_required')->label('Requerido')->default(false),
                                    Toggle::make('visible')->label('Visible')->default(true),
                                    TextInput::make('placeholder')->label('Placeholder'),
                                ]),

                                Placeholder::make('json_schema_hint')
                                    ->label('Ejemplo de Estructura JSON')
                                    ->visible(function (callable $get): bool {
                                        $attributeId = $get('attribute_id');
                                        if (! $attributeId) return false;
                                        $attribute = Attribute::find($attributeId);
                                        return $attribute?->type === 'json';
                                    })
                                    ->content(function (callable $get): ?string {
                                        $attributeId = $get('attribute_id');
                                        if (! $attributeId) return null;
                                        $attribute = Attribute::find($attributeId);
                                        if ($attribute?->type !== 'json' || empty($attribute->validation_rules)) {
                                            return null;
                                        }
                                        return '<pre><code>' . json_encode($attribute->validation_rules, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . '</code></pre>';
                                    })
                                    ->html() // Render as HTML to preserve <pre> tags
                                    ->columnSpanFull(),
                            ])
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed();
            }

            $sections[] = Section::make($dimension->name)
                ->schema($groupSections)
                ->collapsible()
                ->collapsed()
                ->columnSpanFull();
        }

        return $sections;
    }
}