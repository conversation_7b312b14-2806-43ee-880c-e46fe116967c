<?php

namespace App\Filament\Resources\ProductCategories\Schemas;

use App\Enums\CommonStatus;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class ProductCategoryForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label('Nombre')
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true)
                    ->placeholder('Ej: Textiles, Electrónicos, etc.'),
                Textarea::make('description')
                    ->label('Descripción')
                    ->columnSpanFull()
                    ->maxLength(1000)
                    ->placeholder('Descripción detallada de la categoría'),
                Select::make('status')
                    ->label('Estado')
                    ->options(CommonStatus::class)
                    ->default(CommonStatus::ACTIVE)
                    ->required()
                    ->native(false),
            ]);
    }
}
