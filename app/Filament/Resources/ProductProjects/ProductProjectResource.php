<?php

namespace App\Filament\Resources\ProductProjects;

use App\Filament\Resources\ProductProjects\Pages\CreateProductProject;
use App\Filament\Resources\ProductProjects\Pages\EditProductProject;
use App\Filament\Resources\ProductProjects\Pages\ListProductProjects;
use App\Filament\Resources\ProductProjects\Schemas\ProductProjectForm;
use App\Filament\Resources\ProductProjects\Tables\ProductProjectsTable;
use App\Models\ProductProject;
use BackedEnum;
use UnitEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;

class ProductProjectResource extends Resource
{
    protected static ?string $model = ProductProject::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-cube';

    protected static string|UnitEnum|null $navigationGroup = 'comercial';

    protected static ?int $navigationSort = 12;

    protected static ?string $navigationLabel = 'Productos por Proyecto';

    protected static ?string $modelLabel = 'Producto por Proyecto';

    protected static ?string $pluralModelLabel = 'Productos por Proyecto';

    public static function form(Schema $schema): Schema
    {
        return ProductProjectForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ProductProjectsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProductProjects::route('/'),
            'create' => CreateProductProject::route('/create'),
            'edit' => EditProductProject::route('/{record}/edit'),
        ];
    }
}
