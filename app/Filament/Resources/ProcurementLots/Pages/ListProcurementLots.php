<?php

namespace App\Filament\Resources\ProcurementLots\Pages;

use App\Filament\Resources\ProcurementLots\ProcurementLotResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListProcurementLots extends ListRecords
{
    protected static string $resource = ProcurementLotResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
