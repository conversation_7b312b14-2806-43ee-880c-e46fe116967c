<?php

namespace App\Filament\Resources\ProcurementLots\Pages;

use App\Filament\Resources\ProcurementLots\ProcurementLotResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditProcurementLot extends EditRecord
{
    protected static string $resource = ProcurementLotResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
