<?php

namespace App\Filament\Resources\ProcurementLots\Schemas;

use App\Enums\ProcurementLotStatus;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class ProcurementLotForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('product_project_id')
                    ->relationship('productProject', 'id')
                    ->required(),
                Select::make('product_variant_id')
                    ->relationship('productVariant', 'name')
                    ->required(),
                Select::make('supplier_id')
                    ->relationship('supplier', 'name'),
                Select::make('awarded_quote_id')
                    ->relationship('awardedQuote', 'id'),
                DateTimePicker::make('awarded_at'),
                TextInput::make('quantity')
                    ->required()
                    ->numeric(),
                TextInput::make('agreed_unit_price')
                    ->numeric(),
                TextInput::make('incoterm'),
                TextInput::make('lead_time_days')
                    ->numeric(),
                TextInput::make('currency'),
                Textarea::make('terms')
                    ->columnSpanFull(),
                Select::make('status')
                    ->options(ProcurementLotStatus::class)
                    ->default('draft')
                    ->required(),
            ]);
    }
}
