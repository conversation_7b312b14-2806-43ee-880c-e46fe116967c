<?php

namespace App\Filament\Resources\ProcurementLots;

use App\Filament\Resources\ProcurementLots\Pages\CreateProcurementLot;
use App\Filament\Resources\ProcurementLots\Pages\EditProcurementLot;
use App\Filament\Resources\ProcurementLots\Pages\ListProcurementLots;
use App\Filament\Resources\ProcurementLots\Schemas\ProcurementLotForm;
use App\Filament\Resources\ProcurementLots\Tables\ProcurementLotsTable;
use App\Models\ProcurementLot;
use BackedEnum;
use UnitEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Tables\Table;

class ProcurementLotResource extends Resource
{
    protected static ?string $model = ProcurementLot::class;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-archive-box';

    protected static string|UnitEnum|null $navigationGroup = 'proveedores';

    protected static ?int $navigationSort = 21;

    protected static ?string $navigationLabel = 'Lotes de Abastecimiento';

    protected static ?string $modelLabel = 'Lote de Abastecimiento';

    protected static ?string $pluralModelLabel = 'Lotes de Abastecimiento';

    public static function form(Schema $schema): Schema
    {
        return ProcurementLotForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ProcurementLotsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProcurementLots::route('/'),
            'create' => CreateProcurementLot::route('/create'),
            'edit' => EditProcurementLot::route('/{record}/edit'),
        ];
    }
}
