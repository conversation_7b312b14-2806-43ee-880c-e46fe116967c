<?php

namespace App\Filament\Resources\Customers\Schemas;

use App\Enums\CommonStatus;
use App\Filament\Components\PhoneNumberFields;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class CustomerForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label('Nombre de la Empresa')
                    ->required()
                    ->maxLength(255),

                TextInput::make('contact_person')
                    ->label('Persona de Contacto')
                    ->maxLength(255),

                ...PhoneNumberFields::make(),

                TextInput::make('email')
                    ->label('Email')
                    ->email()
                    ->maxLength(255),

                TextInput::make('tax_id')
                    ->label('Tax ID')
                    ->maxLength(255),

                TextInput::make('contact_info')
                    ->label('Información de Contacto Adicional')
                    ->helperText('Información adicional en formato JSON'),

                Select::make('status')
                    ->label('Estado')
                    ->options(CommonStatus::class)
                    ->default('active')
                    ->required(),
            ]);
    }
}
