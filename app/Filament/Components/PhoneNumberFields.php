<?php

namespace App\Filament\Components;

use App\Rules\CountryCodeEnum;
use App\Rules\PhoneNumberLocal;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;

class PhoneNumberFields
{
    /**
     * Get phone number form fields.
     */
    public static function make(): array
    {
        return [
            Select::make('country_code')
                ->label('País')
                ->options(\App\Enums\CountryCode::options())
                ->searchable()
                ->placeholder('Selecciona un país')
                ->rule(new CountryCodeEnum)
                ->reactive(),

            TextInput::make('phone_number')
                ->label('Número de Teléfono')
                ->tel()
                ->placeholder('17767097675')
                ->helperText('Número local sin código de país')
                ->rule(new PhoneNumberLocal)
                ->maxLength(15),
        ];
    }
}
