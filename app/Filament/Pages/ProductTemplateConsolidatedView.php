<?php

namespace App\Filament\Pages;

use App\Enums\ProductTemplateStatus;
use App\Models\ProductTemplate;
use App\Models\ProductTemplateAttribute;
use BackedEnum;
use Filament\Actions\Action as TableAction;
use Filament\Actions\BulkAction;
use Filament\Forms\Components\Select;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\TextSize;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use UnitEnum;

class ProductTemplateConsolidatedView extends Page implements HasInfolists, HasTable
{
    use InteractsWithInfolists, InteractsWithTable;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-document-text';

    protected string $view = 'filament.pages.product-template-consolidated-view';

    protected static string|UnitEnum|null $navigationGroup = 'especificación de productos';

    protected static ?int $navigationSort = 5;

    protected static ?string $navigationLabel = 'Vista Consolidada de Plantillas';

    protected static ?string $title = 'Vista Consolidada de Plantillas de Producto';

    public ?ProductTemplate $selectedTemplate = null;

    public function mount(): void
    {
        // Seleccionar la primera plantilla por defecto si existe
        $this->selectedTemplate = ProductTemplate::with([
            'productSubcategory.productCategory',
            'productTemplateAttributes.attribute',
            'productTemplateAttributes.attributeGroup',
            'productProjects.project.customer',
        ])->first();
    }

    public function selectTemplate(ProductTemplate $template): void
    {
        $this->selectedTemplate = $template->load([
            'productSubcategory.productCategory',
            'productTemplateAttributes.attribute',
            'productTemplateAttributes.attributeGroup',
            'productProjects.project.customer',
        ]);
    }

    public function templateInfolist(Schema $schema): Schema
    {
        if (! $this->selectedTemplate) {
            return $schema;
        }

        return $schema
            ->record($this->selectedTemplate)
            ->components([
                Section::make('Información de la Plantilla')
                    ->columnSpanFull()
                    ->schema([
                        Grid::make(3)
                            ->columnSpanFull()
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nombre de la Plantilla')
                                    ->size(TextSize::Large)
                                    ->weight('bold'),

                                TextEntry::make('productSubcategory.name')
                                    ->label('Subcategoría')
                                    ->icon('heroicon-o-tag'),

                                TextEntry::make('status')
                                    ->label('Estado')
                                    ->badge()
                                    ->color(fn (ProductTemplateStatus $state): string => match ($state) {
                                        ProductTemplateStatus::DRAFT => 'warning',
                                        ProductTemplateStatus::ACTIVE => 'success',
                                        ProductTemplateStatus::OBSOLETE => 'danger',
                                        default => 'gray',
                                    }),
                            ]),

                        Grid::make(2)
                            ->columnSpanFull()
                            ->schema([
                                TextEntry::make('productSubcategory.productCategory.name')
                                    ->label('Categoría')
                                    ->icon('heroicon-o-folder'),

                                TextEntry::make('created_at')
                                    ->label('Fecha de Creación')
                                    ->dateTime()
                                    ->icon('heroicon-o-calendar'),
                            ]),

                        TextEntry::make('description')
                            ->label('Descripción')
                            ->columnSpanFull()
                            ->formatStateUsing(function ($state) {
                                if (is_array($state)) {
                                    return implode("\n", $state);
                                }

                                return $state;
                            })
                            ->markdown(),
                    ]),

                Section::make('Resumen de Uso')
                    ->columnSpanFull()
                    ->schema([
                        Grid::make(4)
                            ->columnSpanFull()
                            ->schema([
                                TextEntry::make('product_projects_count')
                                    ->label('Proyectos Asociados')
                                    ->getStateUsing(fn () => $this->selectedTemplate->productProjects->count())
                                    ->icon('heroicon-o-folder-open')
                                    ->size(TextSize::Large),

                                TextEntry::make('variants_count')
                                    ->label('Total Variantes')
                                    ->getStateUsing(fn () => $this->selectedTemplate->productProjects->sum(fn ($pp) => $pp->productVariants->count()))
                                    ->icon('heroicon-o-cube-transparent')
                                    ->size(TextSize::Large),

                                TextEntry::make('total_quantity_used')
                                    ->label('Cantidad Total Usada')
                                    ->getStateUsing(fn () => $this->selectedTemplate->productProjects->sum('total_quantity'))
                                    ->icon('heroicon-o-calculator')
                                    ->size(TextSize::Large),

                                TextEntry::make('avg_unit_price')
                                    ->label('Precio Promedio')
                                    ->getStateUsing(function () {
                                        $avgPrice = $this->selectedTemplate->productProjects->avg('avg_unit_price');

                                        return $avgPrice ? '$'.number_format($avgPrice, 2) : 'Sin datos';
                                    })
                                    ->icon('heroicon-o-currency-dollar')
                                    ->size(TextSize::Large),
                            ]),
                    ]),

                Section::make('Configuración de Atributos')
                    ->columnSpanFull()
                    ->schema([
                        Grid::make(3)
                            ->columnSpanFull()
                            ->schema([
                                TextEntry::make('total_attributes')
                                    ->label('Total Atributos')
                                    ->getStateUsing(fn () => $this->selectedTemplate->productTemplateAttributes->count())
                                    ->icon('heroicon-o-cog-6-tooth')
                                    ->size(TextSize::Large),

                                TextEntry::make('required_attributes')
                                    ->label('Atributos Requeridos')
                                    ->getStateUsing(fn () => $this->selectedTemplate->productTemplateAttributes->where('is_required', true)->count())
                                    ->icon('heroicon-o-exclamation-triangle')
                                    ->size(TextSize::Large),

                                TextEntry::make('visible_attributes')
                                    ->label('Atributos Visibles')
                                    ->getStateUsing(fn () => $this->selectedTemplate->productTemplateAttributes->where('visible', true)->count())
                                    ->icon('heroicon-o-eye')
                                    ->size(TextSize::Large),
                            ]),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                ProductTemplateAttribute::query()
                    ->whereHas('productTemplate', function (Builder $query) {
                        if ($this->selectedTemplate) {
                            $query->where('id', $this->selectedTemplate->id);
                        }
                    })
                    ->with([
                        'attribute',
                        'attributeGroup',
                        'productTemplate',
                    ])
            )
            ->columns([
                TextColumn::make('attributeGroup.name')
                    ->label('Grupo')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->placeholder('Sin grupo'),

                TextColumn::make('attribute.name')
                    ->label('Atributo')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('attribute.key')
                    ->label('Clave')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->copyMessage('Clave copiada')
                    ->copyMessageDuration(1500)
                    ->fontFamily('mono'),

                TextColumn::make('attribute.type')
                    ->label('Tipo')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'string' => 'gray',
                        'integer' => 'blue',
                        'decimal' => 'blue',
                        'boolean' => 'yellow',
                        'enum' => 'green',
                        'text' => 'purple',
                        'date' => 'indigo',
                        'email' => 'pink',
                        'url' => 'cyan',
                        default => 'gray',
                    }),

                IconColumn::make('is_required')
                    ->label('Requerido')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('gray'),

                IconColumn::make('visible')
                    ->label('Visible')
                    ->boolean()
                    ->trueIcon('heroicon-o-eye')
                    ->falseIcon('heroicon-o-eye-slash')
                    ->trueColor('success')
                    ->falseColor('gray'),

                TextColumn::make('position')
                    ->label('Posición')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('ui_component')
                    ->label('Componente UI')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Por defecto'),

                TextColumn::make('default_value')
                    ->label('Valor por Defecto')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state)) {
                            return json_encode($state, JSON_UNESCAPED_UNICODE);
                        }

                        return $state;
                    })
                    ->limit(50)
                    ->placeholder('Sin valor por defecto'),

                TextColumn::make('placeholder')
                    ->label('Placeholder')
                    ->limit(30)
                    ->placeholder('Sin placeholder'),
            ])
            ->filters([
                SelectFilter::make('attribute_group_id')
                    ->label('Grupo de Atributos')
                    ->relationship('attributeGroup', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('attribute_type')
                    ->label('Tipo de Atributo')
                    ->options([
                        'string' => 'Texto',
                        'integer' => 'Entero',
                        'decimal' => 'Decimal',
                        'boolean' => 'Booleano',
                        'enum' => 'Enumeración',
                        'text' => 'Área de Texto',
                        'date' => 'Fecha',
                        'email' => 'Email',
                        'url' => 'URL',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn (Builder $query, $value): Builder => $query->whereHas('attribute', fn (Builder $query) => $query->where('type', $value))
                        );
                    }),

                SelectFilter::make('is_required')
                    ->label('Requerido')
                    ->options([
                        true => 'Sí',
                        false => 'No',
                    ]),

                SelectFilter::make('visible')
                    ->label('Visible')
                    ->options([
                        true => 'Sí',
                        false => 'No',
                    ]),
            ])
            ->actions([
                TableAction::make('edit')
                    ->label('Editar')
                    ->icon('heroicon-o-pencil')
                    ->url(fn (ProductTemplateAttribute $record): string => route('filament.admin.resources.product-template-attributes.edit', $record))
                    ->openUrlInNewTab(),

                TableAction::make('view_attribute')
                    ->label('Ver Atributo')
                    ->icon('heroicon-o-eye')
                    ->infolist([
                        Section::make('Detalles del Atributo')
                            ->columnSpanFull()
                            ->schema([
                                Grid::make(2)
                                    ->columnSpanFull()
                                    ->schema([
                                        TextEntry::make('attribute.name')
                                            ->label('Etiqueta'),

                                        TextEntry::make('attribute.key')
                                            ->label('Clave'),

                                        TextEntry::make('attribute.type')
                                            ->label('Tipo'),

                                        TextEntry::make('attributeGroup.name')
                                            ->label('Grupo'),

                                        TextEntry::make('is_required')
                                            ->label('Requerido')
                                            ->badge()
                                            ->color(fn (bool $state): string => $state ? 'success' : 'gray'),

                                        TextEntry::make('visible')
                                            ->label('Visible')
                                            ->badge()
                                            ->color(fn (bool $state): string => $state ? 'success' : 'gray'),

                                        TextEntry::make('position')
                                            ->label('Posición'),

                                        TextEntry::make('ui_component')
                                            ->label('Componente UI'),
                                    ]),

                                TextEntry::make('default_value')
                                    ->label('Valor por Defecto')
                                    ->formatStateUsing(fn ($state) => is_array($state) || is_object($state) ? json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : $state)
                                    ->columnSpanFull(),

                                TextEntry::make('validation_rules')
                                    ->label('Reglas de Validación')
                                    ->formatStateUsing(fn ($state) => is_array($state) || is_object($state) ? json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : $state)
                                    ->columnSpanFull(),

                                TextEntry::make('ui_props')
                                    ->label('Propiedades UI')
                                    ->formatStateUsing(fn ($state) => is_array($state) || is_object($state) ? json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : $state)
                                    ->columnSpanFull(),
                            ]),
                    ]),
            ])
            ->bulkActions([
                BulkAction::make('export')
                    ->label('Exportar Seleccionados')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function (Collection $records) {
                        // Implementar exportación
                        Notification::make()
                            ->title('Exportación iniciada')
                            ->body('Exportación iniciada para '.$records->count().' atributos')
                            ->success()
                            ->send();
                    }),

                BulkAction::make('toggle_required')
                    ->label('Cambiar Requerido')
                    ->icon('heroicon-o-exclamation-triangle')
                    ->form([
                        Select::make('is_required')
                            ->label('Requerido')
                            ->options([
                                true => 'Sí',
                                false => 'No',
                            ])
                            ->required(),
                    ])
                    ->action(function (Collection $records, array $data) {
                        $records->each->update(['is_required' => $data['is_required']]);
                        Notification::make()
                            ->title('Estado actualizado')
                            ->body('Estado de requerido actualizado para '.$records->count().' atributos')
                            ->success()
                            ->send();
                    }),

                BulkAction::make('toggle_visible')
                    ->label('Cambiar Visibilidad')
                    ->icon('heroicon-o-eye')
                    ->form([
                        Select::make('visible')
                            ->label('Visible')
                            ->options([
                                true => 'Sí',
                                false => 'No',
                            ])
                            ->required(),
                    ])
                    ->action(function (Collection $records, array $data) {
                        $records->each->update(['visible' => $data['visible']]);
                        Notification::make()
                            ->title('Visibilidad actualizada')
                            ->body('Visibilidad actualizada para '.$records->count().' atributos')
                            ->success()
                            ->send();
                    }),
            ])
            ->defaultSort('position')
            ->paginated([10, 25, 50, 100])
            ->poll('30s'); // Actualizar cada 30 segundos
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('select_template')
                ->label('Seleccionar Plantilla')
                ->icon('heroicon-o-document-text')
                ->form([
                    Select::make('template_id')
                        ->label('Plantilla')
                        ->options(ProductTemplate::with('productSubcategory.productCategory')->get()->mapWithKeys(function ($template) {
                            return [$template->id => $template->name.' - '.$template->productSubcategory->name];
                        }))
                        ->searchable()
                        ->preload()
                        ->required(),
                ])
                ->action(function (array $data) {
                    $template = ProductTemplate::with([
                        'productSubcategory.productCategory',
                        'productTemplateAttributes.attribute',
                        'productTemplateAttributes.attributeGroup',
                        'productProjects.project.customer',
                    ])->find($data['template_id']);
                    $this->selectTemplate($template);
                    Notification::make()
                        ->title('Plantilla seleccionada')
                        ->body('Plantilla seleccionada: '.$template->name)
                        ->success()
                        ->send();
                }),
        ];
    }
}
