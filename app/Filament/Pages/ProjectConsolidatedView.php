<?php

namespace App\Filament\Pages;

use App\Enums\ProjectStatus;
use App\Models\ProductVariant;
use App\Models\Project;
use BackedEnum;
use Filament\Actions\Action as TableAction;
use Filament\Actions\BulkAction;
use Filament\Forms\Components\Select;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\TextSize;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use UnitEnum;

class ProjectConsolidatedView extends Page implements HasInfolists, HasTable
{
    use InteractsWithInfolists, InteractsWithTable;

    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected string $view = 'filament.pages.project-consolidated-view';

    protected static string|UnitEnum|null $navigationGroup = 'comercial';

    protected static ?int $navigationSort = 15;

    protected static ?string $navigationLabel = 'Vista Consolidada de Proyectos';

    protected static ?string $title = 'Vista Consolidada de Proyectos';

    public ?Project $selectedProject = null;

    public function mount(): void
    {
        // Seleccionar el primer proyecto por defecto si existe
        $this->selectedProject = Project::with(['customer', 'productProjects.productTemplate'])
            ->first();
    }

    public function selectProject(Project $project): void
    {
        $this->selectedProject = $project->load(['customer', 'productProjects.productTemplate']);
    }

    public function projectInfolist(Schema $schema): Schema
    {
        if (! $this->selectedProject) {
            return $schema;
        }

        return $schema
            ->record($this->selectedProject)
            ->components([
                Section::make('Información del Proyecto')
                    ->columnSpanFull()
                    ->schema([
                        Grid::make(3)
                            ->columnSpanFull()
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nombre del Proyecto')
                                    ->size(TextSize::Large)
                                    ->weight('bold'),

                                TextEntry::make('customer.name')
                                    ->label('Cliente')
                                    ->icon('heroicon-o-user'),

                                TextEntry::make('status')
                                    ->label('Estado')
                                    ->badge()
                                    ->color(fn (ProjectStatus $state): string => match ($state) {
                                        ProjectStatus::DRAFT => 'warning',
                                        ProjectStatus::ACTIVE => 'success',
                                        ProjectStatus::CLOSED => 'info',
                                        default => 'gray',
                                    }),
                            ]),

                        Grid::make(2)
                            ->columnSpanFull()
                            ->schema([
                                TextEntry::make('start_date')
                                    ->label('Fecha de Inicio')
                                    ->date()
                                    ->icon('heroicon-o-calendar'),

                                TextEntry::make('end_date')
                                    ->label('Fecha de Fin')
                                    ->date()
                                    ->icon('heroicon-o-calendar'),
                            ]),

                        TextEntry::make('description')
                            ->label('Descripción')
                            ->columnSpanFull()
                            ->markdown(),
                    ]),

                Section::make('Resumen de Productos')
                    ->columnSpanFull()
                    ->schema([
                        Grid::make(4)
                            ->columnSpanFull()
                            ->schema([
                                TextEntry::make('product_projects_count')
                                    ->label('Total Productos')
                                    ->getStateUsing(fn () => $this->selectedProject->productProjects->count())
                                    ->icon('heroicon-o-cube')
                                    ->size(TextSize::Large),

                                TextEntry::make('variants_count')
                                    ->label('Total Variantes')
                                    ->getStateUsing(fn () => $this->selectedProject->productProjects->sum(fn ($pp) => $pp->productVariants->count()))
                                    ->icon('heroicon-o-cube-transparent')
                                    ->size(TextSize::Large),

                                TextEntry::make('total_quantity')
                                    ->label('Cantidad Total')
                                    ->getStateUsing(fn () => $this->selectedProject->productProjects->sum(fn ($pp) => $pp->productVariants->sum('quantity')))
                                    ->icon('heroicon-o-calculator')
                                    ->size(TextSize::Large),

                                TextEntry::make('estimated_total_cost')
                                    ->label('Costo Estimado Total')
                                    ->getStateUsing(function () {
                                        $total = $this->selectedProject->productProjects->sum(function ($pp) {
                                            return $pp->productVariants->sum(function ($variant) {
                                                return $variant->quantity * $variant->estimated_unit_price;
                                            });
                                        });

                                        return '$'.number_format($total, 2);
                                    })
                                    ->icon('heroicon-o-currency-dollar')
                                    ->size(TextSize::Large),
                            ]),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                ProductVariant::query()
                    ->whereHas('productProject', function (Builder $query) {
                        if ($this->selectedProject) {
                            $query->where('project_id', $this->selectedProject->id);
                        }
                    })
                    ->with([
                        'productProject.productTemplate',
                        'productProject.project.customer',
                        'suggestedSupplier',
                    ])
            )
            ->columns([
                TextColumn::make('productProject.productTemplate.name')
                    ->label('Producto')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('name')
                    ->label('Variante')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('generated_sku')
                    ->label('SKU')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->copyMessage('SKU copiado')
                    ->copyMessageDuration(1500),

                TextColumn::make('quantity')
                    ->label('Cantidad')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('estimated_unit_price')
                    ->label('Precio Unitario')
                    ->money('USD')
                    ->sortable()
                    ->alignEnd(),

                TextColumn::make('total_estimated_cost')
                    ->label('Costo Total')
                    ->getStateUsing(fn (ProductVariant $record): string => '$'.number_format($record->quantity * $record->estimated_unit_price, 2)
                    )
                    ->money('USD')
                    ->sortable()
                    ->alignEnd()
                    ->weight('bold'),

                BadgeColumn::make('variant_status')
                    ->label('Estado')
                    ->colors([
                        'warning' => 'draft',
                        'info' => 'configured',
                        'success' => 'locked',
                    ])
                    ->icons([
                        'heroicon-o-pencil' => 'draft',
                        'heroicon-o-cog-6-tooth' => 'configured',
                        'heroicon-o-lock-closed' => 'locked',
                    ]),

                TextColumn::make('suggestedSupplier.name')
                    ->label('Proveedor Sugerido')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Sin proveedor'),

                TextColumn::make('delivery_window_start')
                    ->label('Entrega Desde')
                    ->date()
                    ->sortable(),

                TextColumn::make('delivery_window_end')
                    ->label('Entrega Hasta')
                    ->date()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('variant_status')
                    ->label('Estado de Variante')
                    ->options([
                        'draft' => 'Borrador',
                        'configured' => 'Configurado',
                        'locked' => 'Bloqueado',
                    ]),

                SelectFilter::make('suggested_supplier_id')
                    ->label('Proveedor Sugerido')
                    ->relationship('suggestedSupplier', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                TableAction::make('edit')
                    ->label('Editar')
                    ->icon('heroicon-o-pencil')
                    ->url(fn (ProductVariant $record): string => route('filament.admin.resources.product-variants.edit', $record)
                    )
                    ->openUrlInNewTab(),

                TableAction::make('view_details')
                    ->label('Ver Detalles')
                    ->icon('heroicon-o-eye')
                    ->infolist([
                        Section::make('Detalles de la Variante')
                            ->columnSpanFull()
                            ->schema([
                                Grid::make(2)
                                    ->columnSpanFull()
                                    ->schema([
                                        TextEntry::make('name')
                                            ->label('Nombre'),

                                        TextEntry::make('generated_sku')
                                            ->label('SKU'),

                                        TextEntry::make('quantity')
                                            ->label('Cantidad'),

                                        TextEntry::make('estimated_unit_price')
                                            ->label('Precio Unitario')
                                            ->money('USD'),

                                        TextEntry::make('variant_status')
                                            ->label('Estado')
                                            ->badge(),

                                        TextEntry::make('suggestedSupplier.name')
                                            ->label('Proveedor Sugerido'),
                                    ]),

                                TextEntry::make('configuration')
                                    ->label('Configuración')
                                    ->formatStateUsing(fn ($state) => is_array($state) || is_object($state) ? json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : $state)
                                    ->columnSpanFull(),
                            ]),
                    ]),
            ])
            ->bulkActions([
                BulkAction::make('export')
                    ->label('Exportar Seleccionados')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function (Collection $records) {
                        // Implementar exportación
                        Notification::make()
                            ->title('Exportación iniciada')
                            ->body('Exportación iniciada para '.$records->count().' variantes')
                            ->success()
                            ->send();
                    }),

                BulkAction::make('update_status')
                    ->label('Actualizar Estado')
                    ->icon('heroicon-o-pencil')
                    ->form([
                        Select::make('status')
                            ->label('Nuevo Estado')
                            ->options([
                                'draft' => 'Borrador',
                                'configured' => 'Configurado',
                                'locked' => 'Bloqueado',
                            ])
                            ->required(),
                    ])
                    ->action(function (Collection $records, array $data) {
                        $records->each->update(['variant_status' => $data['status']]);
                        Notification::make()
                            ->title('Estado actualizado')
                            ->body('Estado actualizado para '.$records->count().' variantes')
                            ->success()
                            ->send();
                    }),
            ])
            ->defaultSort('productProject.productTemplate.name')
            ->paginated([10, 25, 50, 100])
            ->poll('30s'); // Actualizar cada 30 segundos
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('select_project')
                ->label('Seleccionar Proyecto')
                ->icon('heroicon-o-folder-open')
                ->form([
                    Select::make('project_id')
                        ->label('Proyecto')
                        ->options(Project::with('customer')->get()->mapWithKeys(function ($project) {
                            return [$project->id => $project->name.' - '.$project->customer->name];
                        }))
                        ->searchable()
                        ->preload()
                        ->required(),
                ])
                ->action(function (array $data) {
                    $project = Project::with(['customer', 'productProjects.productTemplate'])->find($data['project_id']);
                    $this->selectProject($project);
                    Notification::make()
                        ->title('Proyecto seleccionado')
                        ->body('Proyecto seleccionado: '.$project->name)
                        ->success()
                        ->send();
                }),
        ];
    }
}
