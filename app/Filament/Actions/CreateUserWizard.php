<?php

namespace App\Filament\Actions;

use App\Models\User;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Wizard\Step;

class CreateUserWizard extends CreateAction
{
    public static function getDefaultName(): string
    {
        return 'createUserWizard';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->steps([
            Step::make('Información Personal')
                ->description('Datos básicos del usuario')
                ->columnSpanFull()
                ->schema([
                    TextInput::make('name')
                        ->label('Nombre completo')
                        ->required(),
                    TextInput::make('email')
                        ->label('Correo electrónico')
                        ->email()
                        ->required()
                        ->unique(User::class, 'email'),
                ])
                ->columns(2),

            Step::make('Seguridad')
                ->description('Configuración de acceso')
                ->columnSpanFull()
                ->schema([
                    TextInput::make('password')
                        ->label('Contraseña')
                        ->password()
                        ->required()
                        ->minLength(8),
                    TextInput::make('password_confirmation')
                        ->label('Confirmar contraseña')
                        ->password()
                        ->required()
                        ->same('password'),
                ])
                ->columns(2),

            Step::make('Roles y Permisos')
                ->description('Asignar roles al usuario')
                ->columnSpanFull()
                ->schema([
                    Select::make('roles')
                        ->label('Roles')
                        ->multiple()
                        ->options(\Spatie\Permission\Models\Role::all()->pluck('name', 'id'))
                        ->preload()
                        ->searchable()
                        ->required()
                        ->default(function () {
                            $panelUserRole = \Spatie\Permission\Models\Role::where('name', 'panel_user')->first();

                            return $panelUserRole ? [$panelUserRole->id] : [];
                        }),
                ])
                ->columns(2),
        ])
            ->skippableSteps()
            ->action(function (array $data): User {
                $user = User::create([
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'password' => bcrypt($data['password']),
                ]);

                if (isset($data['roles'])) {
                    $user->roles()->sync($data['roles']);
                }

                return $user;
            });
    }
}
