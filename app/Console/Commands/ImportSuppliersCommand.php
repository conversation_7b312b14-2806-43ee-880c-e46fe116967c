<?php

namespace App\Console\Commands;

use App\Enums\CountryCode;
use App\Models\Supplier;
use Illuminate\Console\Command;

class ImportSuppliersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'suppliers:import {file?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Importa proveedores desde un archivo CSV';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $file = $this->argument('file') ?? 'proveedores.csv';
        $csvPath = base_path("_docus/datos_reales/{$file}");

        if (! file_exists($csvPath)) {
            $this->error("Archivo {$file} no encontrado en _docus/datos_reales/");

            return 1;
        }

        $this->info("Importando proveedores desde {$file}...");

        $handle = fopen($csvPath, 'r');
        $header = fgetcsv($handle, 0, ';', '"', '\\');
        $imported = 0;
        $updated = 0;

        while (($row = fgetcsv($handle, 0, ';', '"', '\\')) !== false) {
            if (empty($row[0])) {
                continue;
            }

            $contactPerson = trim($row[0], '"');
            $companyName = trim($row[1], '"');
            $phone = trim($row[2], '"');
            $email = trim($row[3], '"');

            $supplierName = $companyName ?: $contactPerson;

            // Extraer código de país y número del teléfono
            $phoneData = $this->parsePhoneNumber($phone);

            $supplier = Supplier::updateOrCreate(
                ['name' => $supplierName],
                [
                    'contact_person' => $contactPerson,
                    'country_code' => $phoneData['country_code'],
                    'phone_number' => $phoneData['phone_number'],
                    'email' => $email,
                    'status' => 'active',
                ]
            );

            if ($supplier->wasRecentlyCreated) {
                $imported++;
                $this->line("✓ Importado: {$supplierName}");
            } else {
                $updated++;
                $this->line("↻ Actualizado: {$supplierName}");
            }
        }

        fclose($handle);

        $this->info('Importación completada:');
        $this->info("- {$imported} proveedores nuevos importados");
        $this->info("- {$updated} proveedores actualizados");

        return 0;
    }

    /**
     * Parse phone number to extract country code and local number.
     */
    private function parsePhoneNumber(string $phone): array
    {
        // Limpiar el número
        $cleaned = preg_replace('/[^\d\+]/', '', $phone);

        // Si empieza con +, removerlo
        if (str_starts_with($cleaned, '+')) {
            $cleaned = substr($cleaned, 1);
        }

        // Para números chinos (86), extraer código y número
        if (str_starts_with($cleaned, '86') && strlen($cleaned) >= 11) {
            return [
                'country_code' => CountryCode::CHINA->value,
                'phone_number' => substr($cleaned, 2),
            ];
        }

        // Para otros países, asumir que los primeros 1-3 dígitos son el código
        // Por simplicidad, asumir código de 2 dígitos para números largos
        if (strlen($cleaned) >= 10) {
            $countryCode = substr($cleaned, 0, 2);
            $phoneNumber = substr($cleaned, 2);

            return [
                'country_code' => $countryCode,
                'phone_number' => $phoneNumber,
            ];
        }

        // Fallback: tratar todo como número local
        return [
            'country_code' => CountryCode::CHINA->value, // Default a China
            'phone_number' => $cleaned,
        ];
    }
}
