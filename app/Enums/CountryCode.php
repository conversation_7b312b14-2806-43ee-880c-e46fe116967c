<?php

namespace App\Enums;

enum CountryCode: string
{
    case CHINA = '86';
    case UNITED_STATES = '1';
    case SPAIN = '34';
    case UNITED_KINGDOM = '44';
    case GERMANY = '49';
    case FRANCE = '33';
    case ITALY = '39';
    case JAPAN = '81';
    case SOUTH_KOREA = '82';
    case AUSTRALIA = '61';
    case MEXICO = '52';
    case BRAZIL = '55';
    case ARGENTINA = '54';
    case CHILE = '56';
    case COLOMBIA = '57';
    case PERU = '51';
    case INDIA = '91';
    case RUSSIA = '7';
    case SOUTH_AFRICA = '27';

    /**
     * Get the country name for this code.
     */
    public function countryName(): string
    {
        return match ($this) {
            self::CHINA => 'China',
            self::UNITED_STATES => 'Estados Unidos',
            self::SPAIN => 'España',
            self::UNITED_KINGDOM => 'Reino Unido',
            self::GERMANY => 'Alemania',
            self::FRANCE => 'Francia',
            self::ITALY => 'Italia',
            self::JAPAN => 'Jap<PERSON>',
            self::SOUTH_KOREA => 'Corea del Sur',
            self::AUSTRALIA => 'Australia',
            self::MEXICO => 'México',
            self::BRAZIL => 'Brasil',
            self::ARGENTINA => 'Argentina',
            self::CHILE => 'Chile',
            self::COLOMBIA => 'Colombia',
            self::PERU => 'Perú',
            self::INDIA => 'India',
            self::RUSSIA => 'Rusia',
            self::SOUTH_AFRICA => 'Sudáfrica',
        };
    }

    /**
     * Get the flag emoji for this country.
     */
    public function flag(): string
    {
        return match ($this) {
            self::CHINA => '🇨🇳',
            self::UNITED_STATES => '🇺🇸',
            self::SPAIN => '🇪🇸',
            self::UNITED_KINGDOM => '🇬🇧',
            self::GERMANY => '🇩🇪',
            self::FRANCE => '🇫🇷',
            self::ITALY => '🇮🇹',
            self::JAPAN => '🇯🇵',
            self::SOUTH_KOREA => '🇰🇷',
            self::AUSTRALIA => '🇦🇺',
            self::MEXICO => '🇲🇽',
            self::BRAZIL => '🇧🇷',
            self::ARGENTINA => '🇦🇷',
            self::CHILE => '🇨🇱',
            self::COLOMBIA => '🇨🇴',
            self::PERU => '🇵🇪',
            self::INDIA => '🇮🇳',
            self::RUSSIA => '🇷🇺',
            self::SOUTH_AFRICA => '🇿🇦',
        };
    }

    /**
     * Get all country codes as array for forms.
     */
    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn ($case) => [
                $case->value => "{$case->flag()} {$case->countryName()} (+{$case->value})",
            ])
            ->toArray();
    }

    /**
     * Find country code by value.
     */
    public static function fromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }
}
