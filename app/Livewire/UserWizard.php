<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;

class UserWizard extends Component
{
    use WithFileUploads;

    public $currentStep = 1;
    public $totalSteps = 3;

    // Datos del formulario
    public $name = '';
    public $email = '';
    public $password = '';
    public $password_confirmation = '';
    public $roles = [];
    public $is_active = true;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255|unique:users,email',
        'password' => 'required|string|min:8|confirmed',
        'roles' => 'array',
        'is_active' => 'boolean',
    ];

    public function nextStep()
    {
        if ($this->currentStep < $this->totalSteps) {
            $this->validateCurrentStep();
            $this->currentStep++;
        }
    }

    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }

    public function goToStep($step)
    {
        if ($step >= 1 && $step <= $this->totalSteps) {
            $this->currentStep = $step;
        }
    }

    protected function validateCurrentStep()
    {
        switch ($this->currentStep) {
            case 1:
                $this->validate([
                    'name' => 'required|string|max:255',
                    'email' => 'required|email|max:255|unique:users,email',
                ]);
                break;
            case 2:
                $this->validate([
                    'password' => 'required|string|min:8|confirmed',
                ]);
                break;
        }
    }

    public function save()
    {
        $this->validate();

        $user = User::create([
            'name' => $this->name,
            'email' => $this->email,
            'password' => bcrypt($this->password),
            'is_active' => $this->is_active,
        ]);

        if (!empty($this->roles)) {
            $user->roles()->sync($this->roles);
        }

        session()->flash('message', 'Usuario creado exitosamente.');

        $this->reset();
        $this->currentStep = 1;
    }

    public function render()
    {
        return view('livewire.user-wizard', [
            'availableRoles' => \Spatie\Permission\Models\Role::all(),
        ]);
    }
}
