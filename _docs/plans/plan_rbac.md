### Plan RBAC con Spatie Permission, Filament 4 y Filament Shield 4 (<PERSON><PERSON> 12, sin multitenancy)

- Objetivo: implementar RBAC (roles y permisos) con <PERSON><PERSON>, integrarlo al panel de Filament 4 y automatizar permisos con Filament Shield 4. Guard/guard_name: web.

1) Spatie Permission
- Explicación: instala y prepara Spatie Permission (RBAC) y sus migraciones.
```bash
composer require spatie/laravel-permission
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan migrate
```
- `config/permission.php`: setear `'default_guard' => 'web'`.
- Registrar aliases de middleware en `bootstrap/app.php`:
```php
->withMiddleware(function (Middleware $middleware) {
    $middleware->alias([
        'role' => \Spatie\Permission\Middlewares\RoleMiddleware::class,
        'permission' => \Spatie\Permission\Middlewares\PermissionMiddleware::class,
        'role_or_permission' => \Spatie\Permission\Middlewares\RoleOrPermissionMiddleware::class,
    ]);
})
```
- `app/Models/User.php`:
```php
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;
    // protected $guard_name = 'web'; // opcional
}
```

2) Filament 4
- Explicación: instala el panel de administración y fija el acceso usando el guard web.
```bash
composer require filament/filament:"^4.0"
php artisan make:filament-panel Admin
```
- Acceso al panel (ejemplo) implementando `FilamentUser`:
```php
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;

class User extends Authenticatable implements FilamentUser
{
    public function canAccessPanel(Panel $panel): bool
    {
        return $this->hasRole('admin') || $this->hasRole('super_admin');
    }
}
```

- Fijar explícitamente el guard en el panel (evita ambigüedad cuando existan otros guards):
```php
// app/Providers/Filament/AdminPanelProvider.php
public function panel(Panel $panel): Panel
{
    return $panel
        ->authGuard('web');
}
```

3) Filament Shield 4
- Explicación: integra Filament con Spatie para generar y aplicar permisos por recursos.
```bash
composer require bezhansalleh/filament-shield:"^4.0"
php artisan vendor:publish --tag=filament-shield-config
php artisan shield:install
```
- Registrar plugin en `app/Providers/Filament/AdminPanelProvider.php`:
```php
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;

public function panel(Panel $panel): Panel
{
    return $panel
        // ...
        ->plugins([
            FilamentShieldPlugin::make(),
        ]);
}
```

4) Seeds de roles base (sin permisos manuales)
- Explicación: define roles únicamente; los permisos se generarán con Shield.
```bash
php artisan make:seeder RolesAndPermissionsSeeder
```
```php
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        Role::firstOrCreate(['name' => 'super_admin']);
        Role::firstOrCreate(['name' => 'admin']);
        Role::firstOrCreate(['name' => 'editor']);
    }
}
```

5) Checklist de verificación rápida
- Explicación: recorrido mínimo para dejar RBAC (Role-Based Access Control) operativo end-to-end.

- Preparación (Spatie Permission)
```bash
composer require spatie/laravel-permission
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan migrate
```

- Filament 4
```bash
composer require filament/filament:"^4.0"
php artisan make:filament-panel Admin
```

- Filament Shield 4
```bash
composer require bezhansalleh/filament-shield:"^4.0"
php artisan vendor:publish --tag=filament-shield-config
php artisan shield:install
```

- Configuración clave
```text
1) En config/permission.php, fija: default_guard = "web".
2) En bootstrap/app.php, registra aliases de middleware:
   role, permission, role_or_permission.
3) En app/Models/User.php, añade HasRoles (y opcional guard_name = "web").
4) Implementa canAccessPanel en User (FilamentUser) para controlar acceso al panel.
5) En AdminPanelProvider, usa ->authGuard('web').
6) En AppServiceProvider, añade un Gate::before para super_admin.
```

- Gate global para `super_admin` (bypass de autorización):
```php
// app/Providers/AppServiceProvider.php
use Illuminate\Support\Facades\Gate;

public function boot(): void
{
    Gate::before(function ($user, string $ability) {
        return $user->hasRole('super_admin') ? true : null;
    });
}
```

- Seeds y permisos
```bash
php artisan db:seed --class=RolesAndPermissionsSeeder
# Crea al menos un Resource de Filament antes de generar permisos
# ej.: php artisan make:filament-resource User
php artisan shield:generate --all
php artisan permission:cache-reset
```

- Bootstrap de acceso (opcional)
```bash
php artisan tinker --execute='$u=\\App\\Models\\User::firstOrCreate(["email"=>"<EMAIL>"],["name"=>"Admin","password"=>bcrypt("secret123")]); $u->syncRoles(["super_admin"]);'
```

- Smoke test
```text
1) Inicia el servidor de desarrollo.
2) Accede al login y entra con el usuario de bootstrap.
3) Verifica acceso al panel de Filament.
```

- Mantenimiento
```bash
# Al agregar nuevos Resources/Pages/Widgets en Filament
php artisan shield:generate --all
php artisan permission:cache-reset
```