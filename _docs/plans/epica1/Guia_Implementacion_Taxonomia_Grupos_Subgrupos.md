# Guía de Implementación: Taxonomía y Especificaciones (Grupos/Subgrupos)

> Objetivo: implementar en Laravel/Filament 4 la taxonomía con Grupos (7 dimensiones), Subgrupos y Definiciones de Atributo, UI dinámica, validación, versionado y gobierno.

---

## 1) Prerrequisitos

- Laravel 10+ y PHP 8.2+.
- Filament 3/4 para panel de administración.
- MySQL 8+ o PostgreSQL 13+ (índices funcionales y JSON nativos).
- Roles: `admin` para administrar taxonomía.

---

## 2) Migraciones (esquema base)

- Tablas
  - `product_categories(id, name, slug:unique, status, timestamps)`
  - `product_subcategories(id, category_id:fk, name, slug:unique, status, timestamps)`
  - `attribute_groups(id, name, slug:unique, order:int, description:null, timestamps)`
  - `attribute_subgroups(id, group_id:fk, name, slug:unique, order:int, description:null, timestamps)`
  - `attribute_definitions(id, subcategory_id:fk, subgroup_id:fk, key:unique, label_es, label_en:null, type:enum, unit:null, required:bool, enum_values:json:null, min:int:null, max:int:null, order:int, help_text_es:null, help_text_en:null, timestamps)`
  - `product_items(id, project_id:fk, subcategory_id:fk, name, status:enum, quantity:int, uom:enum:null, notes:null, timestamps)`

- Índices recomendados
- `attribute_groups (order)`
- `attribute_subgroups (group_id, order)`
  - `attribute_definitions (subcategory_id, order)`
  - `product_items (subcategory_id, status)`

- Reglas y constraints
- `attribute_subgroups.group_id` → `attribute_groups.id` (FK `restrict`).
- `attribute_definitions.subgroup_id` → `attribute_subgroups.id` (FK `restrict`).
  - `attribute_definitions.subcategory_id` → `product_subcategories.id` (FK `cascade` delete opcional si se permite purga controlada).

Snippets (Laravel) – ejemplo de `attribute_groups` y `attribute_subgroups`
```php
Schema::create('attribute_groups', function (Blueprint $t) {
    $t->id();
    $t->string('name');
    $t->string('slug')->unique();
    $t->unsignedInteger('order')->default(0);
    $t->text('description')->nullable();
    $t->timestamps();
    $t->index('order');
});

Schema::create('attribute_subgroups', function (Blueprint $t) {
    $t->id();
    $t->foreignId('group_id')->constrained('attribute_groups');
    $t->string('name');
    $t->string('slug')->unique();
    $t->unsignedInteger('order')->default(0);
    $t->text('description')->nullable();
    $t->timestamps();
    $t->index(['group_id','order']);
});
```

---

## 3) Modelos (Eloquent)

- `AttributeGroup` ↔ `hasMany(AttributeSubgroup, 'group_id')`
- `AttributeSubgroup` ↔ `belongsTo(AttributeGroup, 'group_id')`, `hasMany(AttributeDefinition, 'subgroup_id')`
- `AttributeDefinition` ↔ `belongsTo(ProductSubcategory)`, `belongsTo(AttributeSubgroup, 'subgroup_id')`
- `ProductSubcategory` ↔ `hasMany(AttributeDefinition)`
- `ProductItem` ↔ `belongsTo(ProductSubcategory)`

Campos clave en casts
- `AttributeDefinition::$casts = ['enum_values' => 'array']`

---

## 4) Seed inicial (catálogo canónico)

Grupos (`attribute_groups.slug`)
- `informacion_basica`, `atributos_centrales`, `visual_marca`, `textiles`, `embalaje_logistica`, `uso_ciclo_vida`, `comercial_abastecimiento`

Subgrupos por Grupo (slugs sugeridos)
- `atributos_centrales`: `materiales`, `dimensiones`, `peso_carga`, `ensamblaje_estructura`
- `visual_marca`: `impresion`, `marca`, `color`, `etiquetado`, `arte_diseno`
- `textiles`: `textil_corte_calce`, `textil_tallas`, `cuidado`, `textil_gramaje`
- `embalaje_logistica`: `embalaje_unitario`, `embalaje_interior`, `embalaje_master`, `palletizacion`, `unidad_medida`, `despacho_estimado`
- `uso_ciclo_vida`: `uso_previsto`, `vida_util_caducidad`, `almacenamiento`
- `comercial_abastecimiento`: `costos_precios`, `moq`, `proveedor`, `aranceles_hs`, `regulatorio`, `documentacion_certificacion`, `requisitos_internos`
- `informacion_basica`: `identificacion_producto`, `mensajes_marketing`

Seeder (esqueleto)
```php
$groups = collect([
  ['slug' => 'informacion_basica', 'name' => 'Información Básica', 'order' => 10],
  ['slug' => 'atributos_centrales', 'name' => 'Atributos Centrales', 'order' => 20],
  ['slug' => 'visual_marca', 'name' => 'Visual y Marca', 'order' => 30],
  ['slug' => 'textiles', 'name' => 'Textiles', 'order' => 40],
  ['slug' => 'embalaje_logistica', 'name' => 'Embalaje y Logística', 'order' => 50],
  ['slug' => 'uso_ciclo_vida', 'name' => 'Uso y Ciclo de Vida', 'order' => 60],
  ['slug' => 'comercial_abastecimiento', 'name' => 'Comercial y Abastecimiento', 'order' => 70],
])->mapWithKeys(fn($r) => [
  $r['slug'] => AttributeGroup::firstOrCreate(['slug'=>$r['slug']], $r)->id
]);

$subgroupsMap = [
  'atributos_centrales' => ['materiales','dimensiones','peso_carga','ensamblaje_estructura'],
  'visual_marca' => ['impresion','marca','color','etiquetado','arte_diseno'],
  'textiles' => ['textil_corte_calce','textil_tallas','cuidado','textil_gramaje'],
  'embalaje_logistica' => ['embalaje_unitario','embalaje_interior','embalaje_master','palletizacion','unidad_medida','despacho_estimado'],
  'uso_ciclo_vida' => ['uso_previsto','vida_util_caducidad','almacenamiento'],
  'comercial_abastecimiento' => ['costos_precios','moq','proveedor','aranceles_hs','regulatorio','documentacion_certificacion','requisitos_internos'],
  'informacion_basica' => ['identificacion_producto','mensajes_marketing'],
];

foreach ($subgroupsMap as $groupSlug => $subs) {
  foreach ($subs as $i => $slug) {
    AttributeSubgroup::firstOrCreate(['slug'=>$slug],[
      'group_id' => $groups[$groupSlug],
      'name' => Str::headline($slug),
      'order' => ($i+1)*10,
    ]);
  }
}
```

---

## 5) UI de Administración (Filament)

Recursos necesarios
- `ProductCategoryResource`, `ProductSubcategoryResource`
- `AttributeGroupResource`, `AttributeSubgroupResource`
- `AttributeDefinitionResource`
- `ProductItemResource`

Form de `ProductItem`
- Tabs/Accordion por Grupo (`AttributeGroup`) ordenados por `order`.
- Dentro de cada Grupo, secciones por Subgrupo (`AttributeSubgroup`) en su `order`.
- Campos generados desde `AttributeDefinition` (tipo → componente):
  - `string` → `TextInput`
  - `integer` → `TextInput::numeric()->suffix($unit)`
  - `boolean` → `Toggle`
  - `enum` → `Select::options($enum_values)`
  - `array` → `TagsInput` o `Select::multiple()`
  - `object` → `KeyValue` o `Textarea` con JSON

Pseudocódigo de construcción
```php
$defs = Cache::remember("defs:subcat:{$id}", 300, fn() =>
  AttributeDefinition::with('subgroup.group')
    ->where('subcategory_id',$id)
    ->orderBy('subgroup.group.order')
    ->orderBy('subgroup.order')
    ->orderBy('order')
    ->get()
);

$byGroup = $defs->groupBy(fn($d) => $d->subgroup->group->slug);
// crear Tabs por Grupo y dentro paneles por Subgrupo, añadiendo campos mapeados por tipo
```

Estados y bloqueos
- Tras `CustomerPurchaseOrder`: deshabilitar campos de especificación.
- Mostrar banner si `spec_status = OUTDATED` con CTA de revalidación.

---

## 6) Validación y Acciones

Acciones
- `ValidateProductSpecsAction($subcategoryId, array $payload): ValidationResult`
- `PersistProductSpecsAction($productItem, array $payload): void`

Reglas
- Generadas desde `AttributeDefinition`: `required`, tipo, rangos, `in` para enums, estructura para `object`.
- Mensajes basados en `label_es` y `unit`.

---

## 7) Versionado y Gobierno

- `spec_schema_version` por subcategoría (`schema_major.minor`).
- Incrementar `major` ante cambios de requeridos/tipos/semántica o movimientos entre Grupos.
- Incrementar `minor` para campos opcionales o reordenamientos no disruptivos.
- Al publicar cambios: marcar productos como `OUTDATED`, exigir revalidación y auditar en `ActivityLog`.

---

## 8) API (opcional)

Endpoints sugeridos (`/admin/*`)
- `GET /taxonomy/categories|subcategories`
- `GET/POST/PATCH /taxonomy/groups`
- `GET/POST/PATCH /taxonomy/subgroups`
- `GET/POST/PATCH /taxonomy/definitions`
- `GET /taxonomy/definitions/{subcategory}` (composición efectiva)

Seguridad
- Scope `admin` para mutaciones; lectura pública opcional para generar UI externa.

---

## 9) Cache y Performance

- Cachear definiciones por subcategoría 5 minutos (`defs:subcat:{id}`).
- Invalidad cache al publicar cambios de definiciones/grupos/subgrupos.
- Mantener `spec_json` ≤ 16KB como guía.

---

## 10) Pruebas (mínimas)

- Unit: generación de reglas por tipo, unidades y enums.
- Feature: guardar `ProductItem` válido/ inválido según definiciones.
- Feature: UI muestra tabs Grupo y paneles Subgrupo en orden esperado.
- Governance: cambio `major` → marca productos `OUTDATED`.

---

## 11) Despliegue (checklist)

- Crear migraciones y ejecutar en stage/prod (`php artisan migrate`).
- Sembrar Grupos/Subgrupos canónicos (`php artisan db:seed --class=TaxonomySeeder`).
- Cargar/crear subcategorías iniciales y definiciones mínimas.
- Verificar UI de `ProductItem` con ejemplo `polera` y `botella_agua`.
- Configurar roles/permisos; restringir administración a `admin`.
- Activar cache e invalidación en eventos de publicación.

---

## 12) Operación

- Flujo de cambio: propuesta → revisión → aprobación → despliegue → remediación.
- Auditoría: log de publicaciones de definiciones, versiones y productos afectados.
- Mantenimiento: consolidar catálogo de Subgrupos y evitar duplicados semánticos.
