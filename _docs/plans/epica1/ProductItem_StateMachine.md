# 9. Especificación Detallada - Máquina de Estados del Producto (`ProductItem`)

> [!IMPORTANT]
> ### 📌 Decisiones de Negocio Pendientes que Afectan esta Máquina de Estados
> **Contexto:** La lógica de transiciones descrita en este documento es funcional, pero está sujeta a cambios basados en las siguientes decisiones de negocio aún no resueltas:
> 
> **1. Flujo de Aprobación para Excepciones:**
>    - **Pregunta:** ¿La creación de un **Registro de Cancelación de Cliente** (`CustomerCancellation`) o un **Incidente de Proveedor** (`SupplierIncident`) con impacto financiero requiere una segunda aprobación?
>    - **Impacto Potencial:** Si se requiere aprobación, podrían necesitarse estados intermedios adicionales (ej. `Cancelación Pendiente de Aprobación`) y lógica de transición más compleja.
> 
> **2. Manejo de Cancelaciones Parciales:**
>    - **Pregunta:** Si un cliente cancela solo una parte de la cantidad de un producto (`ProductItem`), ¿cuál es el procedimiento?
>    - **Impacto Potencial:** La solución (ej. dividir el `ProductItem`) podría impactar las reglas de negocio y los efectos secundarios de la transición a **Cancelado** (`Cancelled`).

> **Propósito:** Especificación técnica detallada de la máquina de estados del **Producto** (`ProductItem`) para validación completa por parte del negocio y desarrollo.

---

## Contenido

1. [Tabla de Transiciones Válidas](#tabla-de-transiciones-válidas)
2. [Matriz de Transiciones Prohibidas](#matriz-de-transiciones-prohibidas)
3. [Eventos de Negocio y Efectos Secundarios](#eventos-de-negocio-y-efectos-secundarios)
4. [Reglas de Negocio por Estado](#reglas-de-negocio-por-estado)
5. [Casos de Uso para Validación](#casos-de-uso-para-validación)
6. [Flujos de Excepción](#flujos-de-excepción)
7. [Validación de Consistencia](#validación-de-consistencia)

---

## Tabla de Transiciones Válidas

### Transiciones Principales del Flujo Normal

| Estado Origen | Estado Destino | Disparador | Precondiciones | Actor Responsable | Validaciones del Sistema |
|---|---|---|---|---|---|
| **Borrador** (`Draft`) | **Listo para Sourcing** (`ReadyForSourcing`) | Manual | • Especificaciones completas<br>• Cliente válido asignado | Analista de Ventas | • Validar campos obligatorios<br>• Verificar cliente existe |
| **Listo para Sourcing** (`ReadyForSourcing`) | **Sourcing en Progreso** (`SourcingInProgress`) | Manual | • Especificaciones bloqueadas<br>• Analista de Adquisiciones asignado | Analista de Ventas | • Asignar Analista de Adquisiciones |
| **Sourcing en Progreso** (`SourcingInProgress`) | **Pendiente de Revisión Interna** (`InternalReviewPending`) | Manual | • Al menos 1 **Cotización de Proveedor** (`SupplierQuotation`) | Analista de Adquisiciones | • Validar cotización completa |
| **Pendiente de Revisión Interna** (`InternalReviewPending`) | **Cotizado al Cliente** (`QuotedToCustomer`) | Manual | • Aprobación del Líder de Equipo<br>• Margen mínimo cumplido | Líder de Equipo | • Validar margen calculado |
| **Cotizado al Cliente** (`QuotedToCustomer`) | **Pendiente Aprobación Maqueta Virtual** (`PendingVmApproval`) | Manual | • Cliente acepta cotización (Orden de Compra del Cliente registrada)<br>• **HITO DE COMPROMISO** | Analista de Ventas | • Orden de Compra del Cliente registrada y válida<br>• Crear Línea Base del Proyecto (`ProjectBaseline`) |
| **Pendiente Aprobación Maqueta Virtual** (`PendingVmApproval`) | **Pendiente Aprobación Muestra Física** (`PendingPpsApproval`) | Manual | • Cliente aprueba maqueta virtual | Analista de Diseño | • Archivo de maqueta presente |
| **Pendiente Aprobación Muestra Física** (`PendingPpsApproval`) | **Pendiente Aprobación Factura Proforma** (`PendingPiApproval`) | Manual | • Cliente aprueba muestra física | Analista de Producción | • Muestra evaluada y aprobada |
| **Pendiente Aprobación Factura Proforma** (`PendingPiApproval`) | **Listo para Producción** (`ReadyForProduction`) | Manual | • Cliente aprueba Factura Proforma (PI) | Analista de Adquisiciones | • Factura Proforma (PI) aprobada y registrada |
| **Listo para Producción** (`ReadyForProduction`) | **En Producción** (`InProduction`) | Manual | • Orden de Compra a Proveedor (`PurchaseOrder`) enviada | Analista de Adquisiciones | • `PurchaseOrder` generada<br>• Transicionar `CostObligation` tipo `FOB` de **Provisionado** (`PROVISIONED`) a **Comprometido** (`COMMITTED`) |
| **En Producción** (`InProduction`) | **En Tránsito Internacional** (`InternationalTransit`) | Automático | • Asignado a un **Envío de Importación** (`ImportShipment`)<br>• Envío pasa a `InTransit` | Sistema | • Sincronización con `ImportShipment` |
| **En Tránsito Internacional** (`InternationalTransit`) | **En Despacho de Aduanas** (`CustomsClearance`) | Automático | • Envío (`ImportShipment`) pasa a `CustomsClearance` | Sistema | • Sincronización con `ImportShipment` |
| **En Despacho de Aduanas** (`CustomsClearance`) | **Listo para Entrega Doméstica** (`ReadyForDomesticDelivery`) | Automático | • Envío (`ImportShipment`) pasa a `Cleared` | Sistema | • Sincronización con `ImportShipment` |
| **Listo para Entrega Doméstica** (`ReadyForDomesticDelivery`) | **En Entrega Doméstica** (`InDomesticDelivery`) | Automático | • Envío (`ImportShipment`) pasa a `DomesticDelivery` | Sistema | • Sincronización con `ImportShipment` |
| **En Entrega Doméstica** (`InDomesticDelivery`) | **Entregado** (`Delivered`) | Automático | • Envío (`ImportShipment`) pasa a `Delivered` | Sistema | • Sincronización con `ImportShipment` |

### Transiciones de Excepción y Retroceso

| Estado Origen | Estado Destino | Disparador (Evento de Negocio) | Motivo | Actor Responsable | Validaciones del Sistema |
|---|---|---|---|---|---|
| **Pendiente de Revisión Interna** (`InternalReviewPending`) | **Sourcing en Progreso** (`SourcingInProgress`) | Manual | Cotización insuficiente | Líder de Equipo | Registrar motivo |
| **Cotizado al Cliente** (`QuotedToCustomer`) | **Listo para Sourcing** (`ReadyForSourcing`) | Manual | Cliente solicita cambios | Analista de Ventas | Registrar cambios |
| **Pendiente Aprobación Maqueta Virtual** (`PendingVmApproval`) | **Listo para Sourcing** (`ReadyForSourcing`) | Manual | Cliente rechaza maqueta | Analista de Ventas | Registrar feedback |
| **Pendiente Aprobación Muestra Física** (`PendingPpsApproval`) | **Listo para Sourcing** (`ReadyForSourcing`) | Manual | Cliente rechaza muestra | Analista de Ventas | Registrar razones |
| **Cualquier Estado Activo** | **Listo para Sourcing** (`ReadyForSourcing`) | **Incidente de Proveedor** | Fallo de proveedor | Analista de Adquisiciones | • Crear **Incidente de Proveedor** (`SupplierIncident`)<br>• Registrar costos de excepción |
| **Cualquier Estado Activo** | **Cancelado** (`Cancelled`) | **Cancelación de Cliente** | Cliente desiste | Analista de Ventas | • Crear **Cancelación de Cliente** (`CustomerCancellation`)<br>• Capitalizar costos hundidos |

---

## Matriz de Transiciones Prohibidas

| Desde → Hacia | Razón de Prohibición |
|---|---|
| **Entregado** (`Delivered`) → **cualquier estado** | Proceso ya completado. |
| **Cancelado** (`Cancelled`) → **cualquier estado** | Estado terminal definitivo. |
| **En Despacho de Aduanas** (`CustomsClearance`) → **En Producción** (`InProduction`) | Retroceso logísticamente imposible. |

---

## Eventos de Negocio y Efectos Secundarios

| Transición | Evento de Negocio | Efectos Secundarios Automáticos | Notificaciones |
|---|---|---|---|
| `QuotedToCustomer` → `PendingVmApproval` | **Hito de Compromiso** | • Crear Línea Base del Proyecto (`ProjectBaseline`)<br>• Bloquear especificaciones críticas | • Notificación en sistema a Equipo de Diseño, Finanzas y Líder de Equipo |
| `ReadyForProduction` → `InProduction` | **Inicio de Fabricación** | • Activar seguimiento de producción<br>• Transicionar `CostObligation` `FOB` a **COMMITTED** | • Notificación a Cliente y Analista de Producción/COMEX |
| `InProduction` → `InternationalTransit` | **Inicio de Transporte** | • Sincronizar con estado del envío | • Notificación a Cliente y Analista de COMEX |
| **Cualquier Estado** → `ReadyForSourcing` | **Incidente de Proveedor** | • Crear registro del incidente (`SupplierIncident`)<br>• Pausar cronograma y recalcular | • Notificación en sistema a Equipo de Adquisiciones y Líder de Equipo |
| **Cualquier Estado** → `Cancelled` | **Cancelación de Cliente** | • Crear registro de cancelación (`CustomerCancellation`)<br>• Congelar el producto<br>• Iniciar capitalización de pérdidas | • Notificación en sistema a Finanzas y Líder de Equipo |

---

## Reglas de Negocio por Estado

| Estado | Fase del Proceso | Acciones Permitidas | Reglas Especiales |
|---|---|---|---|
| **Borrador** (`Draft`) | `Costeo y Sourcing` | • Editar libremente<br>• Eliminar | Único estado donde se puede eliminar. |
| **Listo para Sourcing** (`ReadyForSourcing`) | `Costeo y Sourcing` | • Iniciar sourcing<br>• Retroceder a `Draft` | Cambios de especificaciones requieren aprobación. |
| **Pendiente de Revisión Interna** (`InternalReviewPending`) | `Costeo y Sourcing` | • Aprobar/Rechazar cotización<br>• Comparar especificaciones propuestas | • En este estado se decide si la `SupplierQuotation` es viable.<br>• Si el proveedor propone una especificación alternativa (`proposed_spec_json`), el equipo interno la revisa aquí.<br>• Una decisión de aceptar la propuesta del proveedor implica una acción de "Actualizar Spec" que modifica el `spec_json` del `ProductItem` y requiere generar una nueva `CustomerQuotation`. |
| **En Producción** (`InProduction`) | `Cumplimiento y Entrega` | • Actualizar progreso<br>• Registrar incidencias | - |
| **Entregado** (`Delivered`) | `Completado` | • Confirmar entrega<br>• Análisis final | Estado final del ciclo de vida operativo. |
| **En Riesgo** (`AtRisk`) | `Cualquiera` | • Añadir notas<br>• Resolver causa del riesgo | Es una **bandera (flag)**, no un estado. No bloquea acciones. |
| **Cancelado** (`Cancelled`) | `Terminal` | Ninguna | Estado final e inmutable. |

---

## Flujos de Excepción

| Situación | Estado Actual | Disparador | Estado Resultante | Efectos del Sistema |
|---|---|---|---|---|
| **Cancelación Formal** | Cualquier estado activo | El cliente desiste de la compra | **Cancelado** (`Cancelled`) | • Disparar creación de una **Cancelación de Cliente** (`CustomerCancellation`).<br>• Congelar estado y costos. |
| **Fallo de Proveedor** | Cualquier estado (post-sourcing) | Incapacidad de producción, calidad, etc. | **Listo para Sourcing** (`ReadyForSourcing`) | • Disparar creación de un **Incidente de Proveedor** (`SupplierIncident`).<br>• Activar sourcing de emergencia. |
| **Retraso en Envío** | **En Producción** (`InProduction`) | Proveedor no cumple fecha | **En Producción** (`InProduction`) con bandera `AtRisk` | • Actualizar cronograma.<br>• Notificar a cliente. |
| **Desfase de Cronograma** | Cualquier estado | Fecha estimada vs real > 7 días | Mismo estado con bandera `AtRisk` | • Escalar a Líder de Equipo y Finanzas. |

---

## Validación de Consistencia

### Checklist de Completitud

**✅ Estados Documentados (15 principales + 2 terminales/bandera):**
- Borrador (`Draft`), Listo para Sourcing (`ReadyForSourcing`), Sourcing en Progreso (`SourcingInProgress`), Pendiente de Revisión Interna (`InternalReviewPending`)
- Cotizado al Cliente (`QuotedToCustomer`), Pendiente Aprobación Maqueta Virtual (`PendingVmApproval`), Pendiente Aprobación Muestra Física (`PendingPpsApproval`), Pendiente Aprobación Factura Proforma (`PendingPiApproval`), Listo para Producción (`ReadyForProduction`)  
- En Producción (`InProduction`), En Tránsito Internacional (`InternationalTransit`), En Despacho de Aduanas (`CustomsClearance`), Listo para Entrega Doméstica (`ReadyForDomesticDelivery`), En Entrega Doméstica (`InDomesticDelivery`), Entregado (`Delivered`)
- **En Riesgo** (`AtRisk`) (Bandera), **Cancelado** (`Cancelled`) (Terminal)

**✅ Transiciones Válidas:** Todas las transiciones principales y de excepción están documentadas con sus disparadores y actores.

**✅ Casos de Excepción:** Rechazos, cancelaciones, problemas de proveedor y del sistema están cubiertos.

---

## Patrones de Implementación Técnica

La implementación de esta máquina de estados debe seguir los siguientes Patrones de Diseño y Decisiones Arquitectónicas (ADRs):

- **ADR-008 (Enums como Objetos de Valor):** El estado (`status`) del **Producto** (`ProductItem`) se implementará como un `Enum` de PHP.
- **ADR-010 (Gestión de Estados con Servicio Dedicado):** La lógica para validar transiciones se centralizará en un `StateTransitionService`.
