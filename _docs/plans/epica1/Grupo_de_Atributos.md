# Especificación Detallada: Grupo y Subgrupo de Atributos

> Propósito: modelar dos niveles de agrupación para atributos de producto: Grupo de Atributos (las 7 dimensiones) y Subgrupo de Atributos (agrupaciones específicas dentro de cada Grupo), optimizados para reutilización, validación y UI.

Documentos relacionados: ver `Especificacion_Enfoque_Hibrido_Grupos_Relacional_JSON.md`.

---

## 1. Entidades y Atributos Clave

- `AttributeGroup` (Grupo de Atributos)
  - `id` (PK)
  - `name` (Texto)
  - `slug` (Texto, único): Identificador canónico del grupo.
  - `order` (Entero): Orden de aparición (p. ej., tabs UI).
  - `description` (Texto, opcional)
  - Índices: único en `slug`; índice en `order`.

- `AttributeSubgroup` (Subgrupo de Atributos)
  - `id` (PK)
  - `name` (Texto)
  - `slug` (Texto, único): Identificador canónico estable.
  - `order` (Entero): Orden dentro del Grupo.
  - `description` (Texto, opcional)
  - `group_id` (FK a `AttributeGroup.id`, NOT NULL)
  - Índices: único en `slug`; índice compuesto en (`group_id`, `order`).

---

## 2. Relaciones

- `AttributeGroup hasMany AttributeSubgroup`
- `AttributeSubgroup hasMany AttributeDefinition`

---

## 3. Reglas y Consideraciones

- Dos niveles exactos: Grupo y Subgrupo. No se permiten niveles adicionales.
- Reutilización: los subgrupos se comparten entre subcategorías; la visibilidad y obligatoriedad se configuran por subcategoría.
- Orden: `order` en Grupo determina la navegación principal en UI; `order` en Subgrupo ordena paneles dentro del Grupo.
- Validación: las reglas de requerido/condicionalidad se definen a nivel Subgrupo, con overrides por subcategoría.
- Seguridad: edición reservada a `admin`.
- Convenciones de `slug`: snake_case, singular, sin acentos (p. ej., `embalaje_master`, `arte_diseno`).

---

## 4. Catálogo Canónico Grupo/Subgrupo (alineado con `99_wip/taxonomia/taxonomia.md`)

- `informacion_basica` (Información Básica del Producto)
  - `identificacion_producto`: `official_product_name`, `variant_name`, `seo_friendly_title`, `internal_sku`.
  - `mensajes_marketing`: `short_marketing_description`, `long_detailed_description`, `key_selling_points`, `target_audience`, `disclaimers`.

- `atributos_centrales` (Propiedades físicas/estructurales)
  - `materiales`: materiales principales/secundarios, certificaciones, grado/tipo, origen.
  - `dimensiones`: externas/internas, diámetro, capacidad/volumen, espesor, partes específicas, tolerancias, plegado.
  - `peso_carga`: `net_weight`, `maximum_load_capacity`, consideraciones de distribución de peso.
  - `ensamblaje_estructura`: `assembly_required`, `assembly_type`, `joining_mechanisms`, `structural_design`, `number_of_parts`.

- `visual_marca` (Especificaciones Visuales y de Marca)
  - `impresion`: métodos, colores por ubicación, área imprimible, tipos de tinta, especiales, bordado.
  - `marca`: guías/ubicaciones de marca, restricciones de placement.
  - `color`: colores base, `pantone_numbers`, `custom_color_capability`, tolerancias (`color_consistency_requirements`), `colorfastness_rating`.
  - `etiquetado`: `label_type`, `label_material`, `label_dimensions`, `label_information_content`, `label_attachment_method`.
  - `arte_diseno`: formatos aceptados, resolución, modo de color, vector requerido, sangrado, fuentes trazadas, plantillas.

- `textiles` (Características específicas de textiles)
  - `textil_corte_calce`: `fit_style`, `garment_construction`, `fabric_pattern`, `neckline_style`, `sleeve_style`, `hem_style`, `specific_features`.
  - `textil_tallas`: `available_sizes`, `sizing_chart`, `sizing_tolerance`, `international_size_conversions`.
  - `cuidado`: `washing_instructions`, `drying_instructions`, `ironing_instructions`, `bleaching_instructions`, `dry_cleaning_instructions`, `care_symbols`.
  - `textil_gramaje` (opcional): `gsm_value`, `gsm_tolerance`, `thread_count`.

- `embalaje_logistica` (Empaque, unitarización y despacho)
  - `embalaje_unitario`: tipo, material, dimensiones, branding, cierre.
  - `embalaje_interior`: unidades por pack, tipo, dimensiones, etiquetado.
  - `embalaje_master`: unidades por master, material, dimensiones, peso bruto/neto, marcado.
  - `palletizacion`: unidades por pallet, tipo/configuración de pallet.
  - `unidad_medida`: `unit_of_measure`, `pricing_ordering_base`.
  - `despacho_estimado`: `estimated_shipping_dimensions`, `estimated_shipping_weight`, `volumetric_weight`.

- `uso_ciclo_vida` (Uso previsto y vida útil)
  - `uso_previsto`: `primary_intended_use`, `intended_environment`, `system_compatibility`, `target_user_group`, `expected_lifespan`.
  - `vida_util_caducidad`: `applicable_shelf_life`, `expiry_date_requirements`, `batch_coding_requirements`.
  - `almacenamiento`: `storage_temperature`, `stacking_limitations`, `storage_sensitivities`.

- `comercial_abastecimiento` (Comercial y sourcing)
  - `costos_precios`: `exw_unit_cost`, `fob_unit_cost`, `cif_unit_cost`/`ddp`, `volume_price_tiers`, `setup_charges`.
  - `moq`: `moq_per_sku`, `moq_per_order`.
  - `proveedor`: `preferred_supplier`, `supplier_part_number`, `country_of_origin`.
  - `aranceles_hs`: `hs_code`, `country_specific_codes`.
  - `regulatorio` (opcional): requisitos país/industria (SEC/ICP, registros sanitarios, peligrosas).
  - `documentacion_certificacion` (opcional): reportes de ensayo, MSDS/SDS.
  - `requisitos_internos` (opcional): estándares de calidad y compliance internos.

Notas
- Subcategorías no textiles pueden omitir grupos bajo `textiles` y `cuidado` si no aplica.
- Para productos con componentes eléctricos/químicos, activar `regulatorio` y `documentacion_certificacion`.
- `color` puede integrarse dentro de `marca` si se prefiere menos granularidad.

---

## 5. Convenciones y Gobernanza

- Slugs estables, snake_case, singular y sin acentos.
- Reusar Subgrupos entre subcategorías para consistencia de UI y validación.
- Mantener Grupos fijos (las 7 dimensiones); añadir nuevos Subgrupos según necesidad.
- Versionar cambios de Subgrupos cuando impliquen cambios de validación/UI.

---

Impacto UI/validación
- UI: tabs/accordion por Grupo; paneles por Subgrupo con orden configurable.
- Validación: marcar requeridos por subcategoría a nivel Subgrupo; métricas de completitud por Grupo.
