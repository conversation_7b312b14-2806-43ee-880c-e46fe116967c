# Tipos de Productos y Especificaciones

> Propósito: describir la clasificación de productos y el enfoque para manejar especificaciones con dos niveles de agrupación (Grupos y Subgrupos de Atributos), alineado con la especificación técnica.

Documentos relacionados
- Especificación técnica: `@Especificacion_Enfoque_Hibrido_Grupos_Relacional_JSON.md`
- Glosario ES → `SI`: `@Glosario_ES_EN.md`

---

## 1. Visión General del Sistema de Clasificación

La taxonomía de productos permite una definición precisa y consistente de cada **Producto** (`ProductItem`). El enfoque es **híbrido y modular**, basado en los siguientes principios:

1.  **Catálogo Relacional:** La jerarquía (`ProductCategory`, `ProductSubcategory`) y el diccionario de atributos (`AttributeGroup`, `AttributeSubgroup`, `AttributeDefinition`) se definen en tablas relacionales para máxima integridad.

2.  **Almacenamiento Híbrido de Especificaciones:** Los valores de la especificación de un `ProductItem` se persisten de forma descompuesta por **Grupo de Atributos**:
    *   **Tablas Relacionales Dedicadas:** Para grupos de datos transaccionales y críticos para la operación (ej. `product_item_commercial`, `product_item_dimensions`).
    *   **Tabla JSON por Grupo:** Para grupos de datos más flexibles y descriptivos (ej. `product_item_group_specs`).

Este enfoque se detalla en el documento `@Especificacion_Enfoque_Hibrido_Grupos_Relacional_JSON.md`.

### A. Jerarquía de Productos
1.  **Categoría:** Agrupación de alto nivel (ej. `Textiles`, `Merchandising`).
2.  **Subcategoría:** Clasificación específica dentro de una categoría (ej. `Poleras`, `Botellas de Agua`).

### B. Agrupación y Composición de Especificaciones (Grupos/Subgrupos)

El sistema organiza las especificaciones en dos niveles y compone por subcategoría lo que aplica en cada caso:

1.  **Grupos** (`AttributeGroup`): 7 dimensiones que ordenan la UI y el modelo:
    - `informacion_basica`, `atributos_centrales`, `visual_marca`, `textiles`, `embalaje_logistica`, `uso_ciclo_vida`, `comercial_abastecimiento`.
2.  **Subgrupos** (`AttributeSubgroup`): subconjuntos específicos dentro de cada Grupo (ej. `materiales`, `dimensiones`, `impresion`, `etiquetado`, `costos_precios`).
3.  **Definiciones de Atributo** (`AttributeDefinition`): por subcategoría, establecen clave canónica (slug), etiqueta, tipo, unidad, obligatoriedad, opciones y ayudas.
4.  **Composición por Subcategoría** (`ProductSubcategory`): cada subcategoría selecciona los Subgrupos y definiciones que necesita.
5.  **Almacenamiento de valores**: los valores finales se guardan en `spec_json` dentro del **Producto** (`ProductItem`), como pares `clave → valor` según la clave canónica.

### Beneficios
- Reutilización y consistencia: Grupos estables y Subgrupos reutilizables por subcategoría.
- Escalabilidad: evolución sin migraciones por atributo (valores en `spec_json`).
- Claridad de UI: navegación por Grupos (tabs/accordion) y paneles por Subgrupos.
- Alineado con Filament 4: componentes generados a partir de definiciones.

---

## 2. Ejemplos de Composición por Grupo/Subgrupo

- Polera (Textiles)
  - Grupos: `informacion_basica`, `atributos_centrales`, `visual_marca`, `textiles`, `embalaje_logistica`, `uso_ciclo_vida`.
  - Subgrupos típicos: `identificacion_producto`, `materiales`, `dimensiones`, `impresion`, `color`, `etiquetado`, `arte_diseno`, `textil_corte_calce`, `textil_tallas`, `cuidado`, `embalaje_unitario`.
- Botella de agua (Merchandising)
  - Grupos: `informacion_basica`, `atributos_centrales`, `visual_marca`, `embalaje_logistica`, `uso_ciclo_vida`, `comercial_abastecimiento`.
  - Subgrupos típicos: `identificacion_producto`, `materiales`, `dimensiones`, `peso_carga`, `ensamblaje_estructura`, `impresion`, `color`, `etiquetado`, `arte_diseno`, `embalaje_unitario`, `embalaje_interior`, `embalaje_master`, `palletizacion`, `unidad_medida`, `despacho_estimado`, `costos_precios`, `moq`, `proveedor`, `aranceles_hs`.

---

## 3. Validación y Congelamiento (visión general)

- Validación: `spec_json` se valida contra las **Definiciones de Atributo** de su subcategoría (tipos, requeridos, rangos, enums). Los nombres de atributos usan la clave canónica (slug), no etiquetas.
- Congelamiento: al recibir la **Orden de Compra del Cliente** (`CustomerPurchaseOrder`), se congela la especificación (snapshot del `spec_json`) en la **Línea Base del Proyecto** (`ProjectBaseline`) y se restringe su edición, salvo excepciones auditadas.
- Transición a proveedor: antes de emitir la **Orden de Compra a Proveedor** (`PurchaseOrder`), todos los atributos requeridos deben estar completos y coherentes con la **Cotización al Cliente** (`CustomerQuotation`) y la **Cotización de Proveedor** (`SupplierQuotation`) seleccionada.

Nota: para detalles de campos, estados y reglas, ver la especificación técnica vinculada.

## 4. Productos con Variantes y Kits

### Productos con Variantes de Diseño
-   **Concepto:** Permite manejar un producto que es técnicamente idéntico pero tiene múltiples diseños (ej. 500 poleras con 5 logos diferentes).
-   **Gestión:** Se negocia el costo por la cantidad total, pero cada variante de diseño requiere su propia aprobación de maqueta virtual.

### Kits y Paquetes de Productos (Bundles)
-   **Concepto:** Permite agrupar diferentes **Productos** (`ProductItem`) que se venden como un solo paquete comercial.
-   **Gestión:** Se cotiza como una sola línea al cliente, pero cada componente del kit mantiene su propio ciclo de vida operativo y de costos.

---

## 5. Gestión de Consolidaciones de Producción (`ProductionConsolidation`)

Las consolidaciones son una optimización que permite aprovechar **economías de escala por proceso productivo**, agrupando múltiples productos del mismo proveedor.

- Compatibilidad: totalmente compatible con el modelo de especificaciones dinámicas.
- Optimización opcional: no afecta a productos gestionados de forma independiente.
- Trazabilidad: vínculo bidireccional consolidación ↔ productos y análisis de ahorros.

## 6. Consideraciones y Limitaciones (V1)

- Reporting por atributos específicos: fuera de alcance en V1 (no se filtra por `spec_json`). Si el negocio requiere analítica por atributo, se evaluará migración gradual a un modelo indexable (EAV tipado) reutilizando las mismas definiciones.
- UI dinámica: los formularios se generan a partir de definiciones y grupos; la experiencia de usuario se mantiene coherente entre subcategorías.
