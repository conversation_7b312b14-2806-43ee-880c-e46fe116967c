# Especificación Detallada: Producto (`ProductItem`)

> Propósito: Definir la entidad operativa central del sistema. Un `ProductItem` recorre el ciclo completo desde definición y cotización hasta producción y entrega, con especificaciones técnicas (`spec_json`) validadas por la taxonomía.

Documentos relacionados
- Glosario: `Glosario_ES_EN.md`
- Máquinas de Estado: `ProductItem_StateMachine.md`
- Taxonomía y Especificaciones (técnico): `Especificacion_Enfoque_Hibrido_Grupos_Relacional_JSON.md`

---

## 1. Atributos Clave

- `id` (PK)
- `project_id` (FK): Proyecto al que pertenece.
- `subcategory_id` (FK): Clasificación taxonómica (ver Subcategoría).
- `name` (Texto): Nombre visible del producto.
- `status` (Enum): Ver máquina de estado (`Draft` → … → `Delivered`/`Cancelled`).
- `quantity` (Entero): Cantidad acordada (para costeo y órdenes).
- `uom` (Enum, opcional): Unidad de medida (ej. `UNITS`, `SETS`).
- `notes` (Texto, opcional): Observaciones internas.

**Nota sobre Especificaciones:** Las especificaciones técnicas de un `ProductItem` no se almacenan en un único campo JSON. En su lugar, se utiliza un **modelo híbrido** donde los datos se distribuyen en tablas relacionales y tablas de especificaciones por grupo. Para más detalles, consultar el documento `@Especificacion_Enfoque_Hibrido_Grupos_Relacional_JSON.md`.

Índices recomendados
- (`project_id`, `status`), (`subcategory_id`, `status`).

---

## 2. Relaciones

- `belongsTo Project`
- `belongsTo ProductSubcategory`
- `belongsTo ProductionConsolidation` (opcional)
- `belongsTo ImportShipment` (opcional)
- `hasMany SupplierQuotation`
- `hasMany CostObligation`
- `hasMany ActivityLog`

---

## 3. Reglas de Negocio

1) Validación de Especificaciones
- `spec_json` se valida contra las `AttributeDefinition` de su `ProductSubcategory` (tipos, requeridos, rangos, enums).
- `spec_status = OUTDATED` cuando cambie el schema. Requiere revalidación para volver a `VALID`.

2) Gating para Órdenes
- Antes de emitir `PurchaseOrder`: todas las claves `required` deben estar presentes y válidas en `spec_json` y coherentes con `CustomerQuotation` y `SupplierQuotation` seleccionada (sin diferencias contractuales).

3) Baseline y Congelamiento
- Al recibir `CustomerPurchaseOrder`, congelar `spec_json` en `ProjectBaseline` y bloquear edición (salvo excepción auditada).

4) Costeo por Cotización de Proveedor
- Al seleccionar `SupplierQuotation`, crear/actualizar `CostObligation` `FOB` en `PROVISIONED`.
- Al emitir `PurchaseOrder`, transicionar `CostObligation` `FOB` a `COMMITTED`.

5) Consolidaciones
- Si el `ProductItem` pertenece a una `ProductionConsolidation`, heredar precio negociado y evitar edición manual de ese costo.

---

## 4. Estados (resumen)

- Venta/Cotización: `Draft` → `ReadyForSourcing` → `QuotedToCustomer`
- Preparación: `PendingVmApproval` → `PendingPpsApproval` → `ReadyForProduction`
- Ejecución: `InProduction` → `InternationalTransit` → `Delivered`
- Excepciones: `Cancelled`, `AtRisk`

Ver detalles en `Maquinas_de_Estado/ProductItem_StateMachine.md`.

---

## 5. Consideraciones de Implementación

- Usar DTOs y Actions para validar y persistir `spec_json` (ver especificación técnica).
- Cachear definiciones por subcategoría y agruparlas en UI por Grupos (tabs/accordion) y Subgrupos (paneles), respetando los `order`.
- Auditar cambios en especificaciones, cambios de estado y selección de cotización/proveedor.

---

## 6. Reglas de Aceptación (extracto)

- Dado un `ProductItem` con atributos `required` faltantes, cuando se intente emitir una `PurchaseOrder`, entonces la acción debe ser bloqueada con errores específicos por atributo.
- Dado que se recibe `CustomerPurchaseOrder`, cuando se reabra el `ProductItem`, entonces los campos de `spec_json` aparecen bloqueados y se registra el snapshot en `ProjectBaseline`.
- Dado que se selecciona una `SupplierQuotation`, entonces se crea/actualiza la `CostObligation` `FOB` en `PROVISIONED` y se registra en `ActivityLog`.

---

## 7. Ejemplos de `spec_json` (canónicos)

Subcategoría: `polera`
```json
{
  "fabric_gsm": 180,
  "material": "algodon",
  "color": "negro",
  "print_method": "serigrafia",
  "sizes_available": ["S", "M", "L", "XL"],
  "care_symbols": ["wash_30c", "no_bleach", "iron_low"]
}
```
Validación esperada: `fabric_gsm` entero 100-300 y requerido; `print_method` ∈ {serigrafia, dtf, bordado}; `sizes_available` lista no vacía.

Subcategoría: `botella_agua`
```json
{
  "capacity_ml": 600,
  "height_mm": 230,
  "diameter_mm": 65,
  "material_body": "tritan",
  "print_method": "tampografia",
  "print_area_mm": {"width": 80, "height": 40}
}
```
Validación esperada: `capacity_ml` entero 100-2000 requerido; `print_area_mm` JSON válido con claves `width` y `height` enteras positivas.
