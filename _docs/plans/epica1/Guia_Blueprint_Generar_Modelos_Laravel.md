# Guía práctica: Generar modelos con Laravel Blueprint

Esta guía te muestra, paso a paso, cómo instalar y usar Blueprint (de Laravel Shift) para definir tu dominio en YAML y generar modelos Eloquent, migraciones y piezas relacionadas en un proyecto Laravel.

## Requisitos

- PHP y Composer instalados.
- Proyecto Laravel funcional (Laravel 8+ recomendado).
- Git recomendado (para revisar difs generados).

## Instalación

1) Instala Blueprint como dependencia de desarrollo:

```bash
composer require -W --dev laravel-shift/blueprint
```

2) (Opcional) Instala aserciones de prueba sugeridas, ya que algunas pruebas generadas pueden usarlas:

```bash
composer require --dev jasonmccreary/laravel-test-assertions
```

3) (Opcional) Publica el archivo de configuración de Blueprint si necesitas personalizar rutas/convenciones:

```bash
php artisan vendor:publish --tag=blueprint-config
# o
php artisan blueprint:new --config
```

4) Añade al .gitignore los archivos de trabajo de Blueprint si vas a usar un borrador temporal en la raíz:

```bash
echo '/draft.yaml' >> .gitignore
echo '/.blueprint' >> .gitignore
```

## Archivo de borrador (draft.yaml)

Blueprint lee la definición de tu sistema desde un archivo YAML (por defecto `draft.yaml`). Allí describes modelos, relaciones y otras piezas. Ejemplo mínimo con varios modelos:

```yaml
models:
  Post:
    title: string:400
    content: longtext
    published_at: nullable timestamp

  Comment:
    content: longtext
    published_at: nullable timestamp
```

Al ejecutar el build, Blueprint generará migraciones y modelos Eloquent para cada entrada en `models`.

## Definir columnas: tipos y modificadores

- Tipos con atributos:
  - `string:40`, `decimal:8,2`, `enum:pending,successful,failed`.
- Modificadores comunes:
  - `nullable`, `index`, `unique`, `default:valor` (cuando aplique), `foreign` para claves foráneas.
- Ejemplos:

```yaml
payment_token: string:40
email: string:100 nullable index
status: enum:pending,successful,failed
```

### Claves foráneas, llaves e índices

- Claves foráneas (inferidas o explícitas):

```yaml
user_id: id foreign          # inferida a users.id
owner_id: id foreign:users   # tabla explícita
uid: id foreign:users.id     # tabla y columna explícitas
```

- Índices compuestos y únicos:

```yaml
indexes:
  - unique: owner_id, badge_number
```

### Shorthands (atajos)

Blueprint agrega por defecto algunos campos si usas shorthands implícitos/explictos.

- Implícitos: `id` y `timestamps` se asumen si no los defines cuando hay columnas.
- Explícitos: puedes declarar `id`, `timestamps`, `softDeletes`:

```yaml
models:
  Widget:
    softDeletes   # añade deleted_at y mantiene id, timestamps implícitos
```

Si prefieres “a mano” todo, puedes definirlo en longhand:

```yaml
models:
  Widget:
    id: id
    deleted_at: timestamp
    created_at: timestamp
    updated_at: timestamp
```

## Relaciones entre modelos

Declara relaciones estándar de Eloquent dentro de `relationships`:

```yaml
models:
  Post:
    title: string:400
    published_at: timestamp nullable
    relationships:
      hasMany: Comment                 # post()->comments()
      belongsToMany: Media, Site       # relación N:N
      belongsTo: \\Spatie\\LaravelPermission\\Models\\Role
```

- Alias de relación (renombra el método generado):

```yaml
hasMany: Comment:reply  # genera reply() en lugar de comments()
```

- belongsToMany con modelo intermedio:

```yaml
User:
  relationships:
    belongsToMany: Team:&Membership
```

## Seeders (opcional)

Puedes indicar qué modelos deben tener seeders generados:

```yaml
models:
  Post:
    title: string:400
    content: longtext
    published_at: nullable timestamp

  Comment:
    post_id: id
    content: longtext
    user_id: id

  User:
    name: string

seeders: Post, Comment
```

## Generar los artefactos

- Revisa las opciones del comando:

```bash
php artisan blueprint:build --help
```

- Ejecuta la generación:

```bash
php artisan blueprint:build
```

Esto traducirá tu `draft.yaml` en:
- Modelos Eloquent (por defecto en `app/Models` en Laravel modernos).
- Migraciones con columnas, claves foráneas e índices.
- Opcionalmente: factories, seeders, controladores, requests, vistas, resources, jobs, events, notificaciones, etc., dependiendo de lo que declares.

Ejemplo de migración generada para `Post`:

```php
Schema::create('posts', function (Blueprint $table) {
    $table->id();
    $table->string('title', 400);
    $table->longText('content');
    $table->timestamp('published_at')->nullable();
    $table->timestamps();
});
```

## Ejemplo completo enfocado en modelos

```yaml
models:
  Category:
    name: string:120 unique

  Author:
    name: string:120
    email: string:100 unique

  Post:
    title: string:200 index
    content: longtext
    published_at: nullable timestamp
    author_id: id foreign:authors
    relationships:
      belongsTo: Author
      belongsToMany: Category

seeders: Author, Category
```

Luego:

```bash
php artisan blueprint:build
php artisan migrate
```

- Obtendrás modelos `Author`, `Category`, `Post` con sus relaciones (`author()`, `categories()`), migraciones con sus llaves/índices, y seeders para `Author` y `Category`.

## Personalización

- Publica `config/blueprint.php` para ajustar namespaces, rutas de salida y convenciones si lo necesitas.
- Mantén tus definiciones en uno o varios archivos YAML (según tu flujo). Por defecto se usa `draft.yaml`.
- Integra `blueprint:build` a tu flujo de desarrollo (p. ej., ejecutarlo tras cambiar el borrador y antes de `migrate`).

## Buenas prácticas

- Versiona solo el código generado, no tu `draft.yaml` temporal si lo usas como scratch pad.
- Revisa los difs de Git después de cada build para asegurar que los cambios son los esperados.
- Empieza simple: define primero los modelos y sus relaciones; añade controladores/requests después.

## Solución de problemas

- “No se generan modelos”: confirma que la clave raíz es `models:` y ejecutaste `php artisan blueprint:build` sin errores.
- “Modelos en carpeta inesperada”: ajusta la configuración publicada de Blueprint para `models_namespace`/`app_path` según tu estructura.
- “Conflictos de nombres o tablas”: valida singular/plural y usa alias de relación cuando choque con otros métodos.

---

Con esto deberías poder definir tus modelos en YAML y generar de forma rápida y consistente los modelos Eloquent y migraciones en Laravel usando Blueprint.

## Blueprint: Taxonomía (Grupos/Subgrupos) – YAML listo

El siguiente `draft.yaml` define los modelos y relaciones para la taxonomía y el modelo híbrido de `ProductItem`.

```yaml
models:
  ProductCategory:
    name: string:120
    slug: string:120 unique
    status: enum:ACTIVE,INACTIVE default:ACTIVE
    description: text nullable
    relationships:
      hasMany: ProductSubcategory

  ProductSubcategory:
    category_id: id foreign:product_categories
    name: string:120
    slug: string:120 unique
    status: enum:ACTIVE,INACTIVE default:ACTIVE
    description: text nullable
    relationships:
      belongsTo: ProductCategory
      hasMany: AttributeDefinition, ProductItem

  AttributeGroup:
    name: string:120
    slug: string:120 unique
    order: integer index
    description: text nullable
    relationships:
      hasMany: AttributeSubgroup

  AttributeSubgroup:
    group_id: id foreign:attribute_groups
    name: string:120
    slug: string:120 unique
    order: integer index
    description: text nullable
    relationships:
      belongsTo: AttributeGroup
      hasMany: AttributeDefinition

  AttributeDefinition:
    subcategory_id: id foreign:product_subcategories
    subgroup_id: id foreign:attribute_subgroups
    key: string:120 unique
    label_es: string:200
    type: enum:string,integer,boolean,enum,array,object
    unit: string:50 nullable
    required: boolean
    enum_values: json nullable
    min: integer nullable
    max: integer nullable
    order: integer
    help_text_es: text nullable
    relationships:
      belongsTo: ProductSubcategory, AttributeSubgroup

  ProductItem:
    project_id: id nullable
    subcategory_id: id foreign:product_subcategories
    name: string:200
    status: enum:Draft,ReadyForSourcing,QuotedToCustomer,PendingVmApproval,PendingPpsApproval,ReadyForProduction,InProduction,InternationalTransit,Delivered,Cancelled,AtRisk default:Draft
    quantity: integer default:0
    uom: enum:UNITS,SETS nullable
    notes: text nullable
    relationships:
      belongsTo: ProductSubcategory
      hasOne: ProductItemCommercial
      hasMany: ProductItemGroupSpec, ProductItemPriceTier # ... y otros dominios relacionales

  ProductItemGroupSpec:
    product_item_id: id foreign
    group_id: id foreign:attribute_groups
    spec_json: json
    schema_version: integer
    spec_status: enum:VALID,OUTDATED
    valid: boolean
    completion_pct: decimal:5,2
    indexes:
      - unique: product_item_id, group_id

  ProductItemCommercial:
    product_item_id: id foreign
    currency: string:3
    incoterm: string:10
    selected_supplier_id: id foreign:suppliers nullable
    # ...otros campos comerciales

  ProductItemPriceTier:
    product_item_id: id foreign
    min_qty: integer
    unit_cost_minor: integer
    currency: string:3
```

Guarda el YAML como `draft.yaml` y genera:

```bash
php artisan blueprint:build
php artisan migrate
```

Nota: Si usas namespaces y paths personalizados, publica y ajusta `config/blueprint.php`.

## Controladores y Form Requests

Blueprint también puede generar controladores y Form Requests a partir del borrador.

### Shorthand de recursos e invokables

```yaml
controllers:
  Post:
    resource           # genera las 7 acciones CRUD y vistas por defecto

  Report:
    invokable          # genera controlador de una sola acción (__invoke)
```

- Selección de acciones específicas:

```yaml
controllers:
  Post:
    resource: index, show
  File:
    resource: api.store, api.update
```

### Acciones explícitas con statements

```yaml
controllers:
  Post:
    index:
      query: all
      render: post.index with:posts
    create:
      render: post.create
    store:
      validate: title, content      # o: validate: post (usa el modelo)
      save: post
      redirect: post.index
    show:
      render: post.show with:post
```

Ejemplo de método generado para `index`:

```php
public function index(Request $request): View
{
    $posts = Post::all();
    return view('post.index', compact('posts'));
}
```

### Form Requests desde el modelo

Al usar `validate: post` (o una lista: `validate: title, content, author_id`), Blueprint genera un Form Request con reglas derivadas del modelo y tipado del método del controlador.

Puntos clave:
- Archivos típicos: `app/Http/Requests/PostStoreRequest.php`, `PostUpdateRequest.php`.
- Las reglas consideran `nullable`, `string:len`, `decimal:precision,scale`, `enum:...`, `unique`, etc.
- Puedes personalizar las reglas tras la generación.

### Rutas

Blueprint no modifica tus archivos de rutas. Agrega las rutas según el tipo de controlador generado:

```php
// Controlador invokable
Route::get('/report', App\Http\Controllers\ReportController::class);

// Controlador de recurso web
Route::resource('posts', App\Http\Controllers\PostController::class);
```

## Factories y Tests

### Factories

Blueprint puede generar factories en base a los modelos definidos. Úsalos en seeders o pruebas. Si también generas `seeders`, éstos suelen usar las factories.

Ejemplo (seeder generado):

```php
public function run(): void
{
    \App\Post::factory(5)->create();
}
```

Notas:
- En proyectos modernos, las factories viven en `database/factories` y usan clases tipo `ModelFactory`.
- Ajusta valores por defecto y estados en las factories generadas.

### Tests

Cuando generas controladores (p. ej., `resource`), Blueprint puede crear pruebas base para las acciones comunes.

Recomendaciones:
- Instala `jasonmccreary/laravel-test-assertions` como dev para assertions extra.
- Ejecuta tu suite: `php artisan test` o `./vendor/bin/pest` si usas Pest.
- Revisa y amplia los tests generados según tus reglas de negocio.

## Ejemplo integral (modelos + controladores + requests + factories + tests)

Contenido de `draft.yaml` que genera todo el flujo CRUD de `Post` y `Comment`, más `Author` y `Category` con relaciones, seeders y validación vía Form Requests:

```yaml
models:
  Author:
    name: string:120
    email: string:100 unique
    relationships:
      hasMany: Post

  Category:
    name: string:120 unique
    relationships:
      belongsToMany: Post

  Post:
    title: string:200 index
    content: longtext
    published_at: nullable timestamp
    author_id: id foreign:authors
    relationships:
      belongsTo: Author
      belongsToMany: Category
      hasMany: Comment

  Comment:
    post_id: id foreign:posts
    author_id: id foreign:authors
    content: longtext
    relationships:
      belongsTo: Post, Author

controllers:
  Post:
    resource: all              # genera CRUD completo
    store:                     # sobreescribe "store" con validación
      validate: post           # genera Form Request según el modelo Post
      save: post
      redirect: post.index
    update:                    # sobreescribe "update"
      validate: post
      update: post
      redirect: post.show
    show:
      query: all:comments      # carga comentarios asociados
      render: post.show with:post,comments

  Comment:
    resource: store, destroy
    store:
      validate: content, post_id, author_id
      save: comment
      redirect: post.show

seeders: Author, Category
```

Qué obtendrás al ejecutar `php artisan blueprint:build`:
- Modelos Eloquent (con métodos de relación `author()`, `categories()`, `comments()`…).
- Migraciones para `authors`, `categories`, `posts`, `comments` con claves foráneas e índices.
- Controladores `PostController` y `CommentController` con acciones generadas.
- Form Requests de `Post` (store/update) y `Comment` (store) con reglas deducidas.
- Factories para cada modelo.
- Tests base para controladores (según configuración).

Luego ejecuta tus migraciones y pruebas:

```bash
php artisan migrate
php artisan test
```

Revisa las rutas en `routes/web.php` o `routes/api.php` según necesites, por ejemplo:

```php
Route::resource('posts', App\Http\Controllers\PostController::class);
Route::post('comments', [App\Http\Controllers\CommentController::class, 'store'])->name('comments.store');
Route::delete('comments/{comment}', [App\Http\Controllers\CommentController::class, 'destroy'])->name('comments.destroy');
```

## Script paso a paso (ejecución)

Incluí un script listo para usar que instala Blueprint, crea un `draft.yaml` de ejemplo, genera los artefactos y sugiere correr migraciones y tests.

- Script: `scripts/blueprint_e2e_demo.sh`
- Uso (desde un proyecto Laravel):

```bash
bash scripts/blueprint_e2e_demo.sh
```

Notas:
- Asegúrate de tener la conexión a base de datos configurada en `.env` antes de migrar.
- Revisa los difs de Git tras la generación.
