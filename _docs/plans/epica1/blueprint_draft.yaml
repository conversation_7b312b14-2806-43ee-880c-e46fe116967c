models:
  ProductCategory:
    name: string:120
    slug: string:120 unique
    status: enum:ACTIVE,INACTIVE default:ACTIVE
    description: text nullable
    relationships:
      hasMany: ProductSubcategory

  ProductSubcategory:
    category_id: id foreign:product_categories
    name: string:120
    slug: string:120 unique
    status: enum:ACTIVE,INACTIVE default:ACTIVE
    description: text nullable
    relationships:
      belongsTo: ProductCategory
      hasMany: AttributeDefinition, ProductItem
      

  AttributeGroup:
    name: string:120
    slug: string:120 unique
    order: integer index
    description: text nullable
    relationships:
      hasMany: AttributeSubgroup

  AttributeSubgroup:
    group_id: id foreign:attribute_groups
    name: string:120
    slug: string:120 unique
    order: integer index
    description: text nullable
    relationships:
      belongsTo: AttributeGroup
      hasMany: AttributeDefinition

  AttributeDefinition:
    subcategory_id: id foreign:product_subcategories
    subgroup_id: id foreign:attribute_subgroups
    key: string:120 unique
    label_es: string:200
    label_en: string:200 nullable
    type: enum:string,integer,boolean,enum,array,object
    unit: string:50 nullable
    required: boolean default:false
    enum_values: json nullable
    min: integer nullable
    max: integer nullable
    order: integer default:0
    help_text_es: text nullable
    help_text_en: text nullable
    indexes:
      - columns: [subcategory_id, order]
    relationships:
      belongsTo: ProductSubcategory, AttributeSubgroup

  ProductItem:
    project_id: id foreign nullable
    subcategory_id: id foreign:product_subcategories
    name: string:200
    status: enum:Draft,ReadyForSourcing,SourcingInProgress,InternalReviewPending,QuotedToCustomer,PendingVmApproval,PendingPpsApproval,PendingPiApproval,ReadyForProduction,InProduction,InternationalTransit,CustomsClearance,ReadyForDomesticDelivery,InDomesticDelivery,Delivered,Cancelled,AtRisk default:Draft
    quantity: integer default:0
    uom: enum:UNITS,SETS nullable
    notes: text nullable
    relationships:
      belongsTo: ProductSubcategory, Project
      hasOne: ProductItemCommercialSpec
      hasMany: ProductItemGroupSpec, ProductItemPriceTierSpec, ProductItemPackagingSpec, ProductItemDimensionSpec, ProductItemImprintSpec, ProductItemColorSpec, ProductItemSizeSpec, ProductItemLabelSpec

  ProductItemGroupSpec:
    product_item_id: id foreign
    group_id: id foreign:attribute_groups
    spec_json: json
    schema_version: integer
    spec_status: enum:VALID,OUTDATED default:VALID
    valid: boolean default:false
    completion_pct: decimal:5,2 default:0
    required_count: integer nullable
    filled_count: integer nullable
    indexes:
      - unique: product_item_id, group_id
    relationships:
      belongsTo: ProductItem, AttributeGroup

  ProductItemCommercialSpec:
    product_item_id: id foreign
    selected_supplier_id: id foreign:suppliers nullable
    supplier_quotation_id: id nullable
    currency: string:3 nullable
    incoterm: string:10 nullable
    country_of_origin: string:100 nullable
    hs_code: string:50 nullable
    relationships:
      belongsTo: ProductItem, Supplier:selectedSupplier
    schema_version: integer nullable
    spec_status: enum:VALID,OUTDATED default:VALID
    valid: boolean default:false
    completion_pct: decimal:5,2 default:0
    indexes:
      - columns: [selected_supplier_id]
      - columns: [hs_code]
      - columns: [currency, incoterm]

  ProductItemPriceTierSpec:
    product_item_id: id foreign
    incoterm: string:10 nullable
    min_qty: integer
    unit_cost_minor: integer
    currency: string:3
    relationships:
      belongsTo: ProductItem
    indexes:
      - unique: product_item_id, incoterm, min_qty

  ProductItemPackagingSpec:
    product_item_id: id foreign
    layer: enum:UNIT,INNER,MASTER
    type: string:100 nullable
    material: string:100 nullable
    length_mm: integer nullable
    width_mm: integer nullable
    height_mm: integer nullable
    net_weight_g: integer nullable
    gross_weight_g: integer nullable
    units_per_layer: integer nullable
    marking: json nullable
    relationships:
      belongsTo: ProductItem

  ProductItemDimensionSpec:
    product_item_id: id foreign
    length_mm: integer nullable
    width_mm: integer nullable
    height_mm: integer nullable
    diameter_mm: integer nullable
    capacity_ml: integer nullable
    thickness_mm: integer nullable
    tolerances: json nullable
    relationships:
      belongsTo: ProductItem

  ProductItemImprintSpec:
    product_item_id: id foreign
    location: string:100 nullable
    method: string:100 nullable
    colors: integer nullable
    area_width_mm: integer nullable
    area_height_mm: integer nullable
    ink_type: string:100 nullable
    notes: text nullable
    relationships:
      belongsTo: ProductItem

  ProductItemColorSpec:
    product_item_id: id foreign
    pantone_code: string:100 nullable
    role: enum:PRODUCT,IMPRINT
    relationships:
      belongsTo: ProductItem

  ProductItemSizeSpec:
    product_item_id: id foreign
    size_code: string:50
    relationships:
      belongsTo: ProductItem

  ProductItemLabelSpec:
    product_item_id: id foreign
    label_type: string:100 nullable
    material: string:100 nullable
    width_mm: integer nullable
    height_mm: integer nullable
    attachment: string:100 nullable
    content: json nullable
    relationships:
      belongsTo: ProductItem

  # Layout pivots modeled explicitly (replaces belongsToMany with extra columns)
  SubcategoryGroup:
    subcategory_id: id foreign:product_subcategories
    group_id: id foreign:attribute_groups
    order: integer default:0
    visible: boolean default:true
    required: boolean default:false
    indexes:
      - unique: subcategory_id, group_id
    relationships:
      belongsTo: ProductSubcategory, AttributeGroup

  SubcategorySubgroup:
    subcategory_id: id foreign:product_subcategories
    subgroup_id: id foreign:attribute_subgroups
    order: integer default:0
    visible: boolean default:true
    indexes:
      - unique: subcategory_id, subgroup_id
    relationships:
      belongsTo: ProductSubcategory, AttributeSubgroup

  # Dummy models needed for relationships
  Project:
    name: string
  Supplier:
    name: string