# Especificación Detallada: Enfoque Híbrido por Grupo (Relacional + JSON)

> Propósito: Definir un modelo operativo en el que el catálogo (Grupos/Subgrupos/Definiciones) vive en relacional, la especificación del producto se persiste por Grupo, y los Grupos estables/transactionales (p. ej., Comercial y Abastecimiento) se modelan relacionalmente; el resto continúa en JSON por Grupo.

Documentos relacionados
- Catálogo y taxonomía: `Grupo_de_Atributos.md`, `Definicion_de_Atributo.md`, `Subcategoria_de_Producto.md`
- Especificación técnica de referencia: Este documento es la especificación principal.
- Guías Blueprint y de implementación: `Guia_Blueprint_Generar_Modelos_Laravel.md`, `Guia_Implementacion_Taxonomia_Grupos_Subgrupos.md`

---

## 1. Objetivos
- Modularizar captura y validación por Grupo de Atributos (7 dimensiones).
- <PERSON><PERSON><PERSON> go<PERSON>, trazabilidad y gating por Grupo obligatorio (informacion_basica, atributos_centrales, comercial_abastecimiento).
- Tipar relacionalmente los Grupos estables/operacionales (comercial, embalaje, dimensiones, impresión, colores, tallas) y mantener JSON por Grupo para el resto.
- Facilitar reporting y performance en dominios críticos, manteniendo flexibilidad para el long tail.

---

## 2. Alcance
- Catálogo (Grupos/Subgrupos/Definiciones) en relacional (como ya está definido).
- Especificación por producto:
  - JSON por Grupo en `product_item_group_specs` para Grupos no relacionalizados.
  - Tablas relacionales por dominio para Grupos estables (p. ej., comercial_abastecimiento, packaging, dimensiones, imprints, colors, sizes).
- Layout/obligatoriedad por subcategoría en relacional: `subcategory_groups` y `subcategory_subgroups`.

---

## 3. Modelo de Datos (alto nivel)

```mermaid
erDiagram
  ProductCategory ||--o{ ProductSubcategory : has
  ProductSubcategory ||--o{ AttributeDefinition : defines
  AttributeGroup ||--o{ AttributeSubgroup : contains
  AttributeSubgroup ||--o{ AttributeDefinition : groups
  ProductSubcategory ||--o{ ProductItem : classifies
  ProductItem ||--o{ ProductItemGroupSpec : "has specs for"
  ProductItem ||--|| ProductItemCommercial : "has commercial data"
  ProductItemCommercial ||--o{ ProductItemPriceTier : "has price tiers"

  ProductCategory {
    bigint id PK
    string name
    string slug
  }

  ProductSubcategory {
    bigint id PK
    bigint category_id FK
    string name
    string slug
  }

  ProductItem {
    bigint id PK
    bigint project_id FK
    bigint subcategory_id FK
    string name
    enum status
  }

  AttributeGroup {
    bigint id PK
    string name
    string slug
    int order
  }

  AttributeSubgroup {
    bigint id PK
    bigint group_id FK
    string name
    string slug
    int order
  }

  AttributeDefinition {
    bigint id PK
    bigint subcategory_id FK
    bigint subgroup_id FK
    string key
    enum type
    string unit
    bool required
  }

  ProductItemGroupSpec {
    bigint id PK
    bigint product_item_id FK
    bigint group_id FK
    json spec_json
    int schema_version
    enum spec_status
    decimal completion_pct
    bool valid
  }

  ProductItemCommercial {
    bigint product_item_id PK,FK
    string currency
    string incoterm
    string country_of_origin
    string hs_code
    bigint selected_supplier_id FK
    bigint supplier_quotation_id FK
  }

  ProductItemPriceTier {
    bigint id PK
    bigint product_item_id FK
    string incoterm
    int min_qty
    int unit_cost_minor
    string currency
  }
```

Nota: Otros dominios relacionales sugeridos (tablas 1:N según aplique): `product_item_packaging`, `product_item_dimensions` (o `product_item_measurements`), `product_item_imprints`, `product_item_colors`, `product_item_sizes`. Ver sección 6.

---

## 4. Reglas de Negocio y Validación

- Por Grupo (JSON):
  - Derivar reglas desde `AttributeDefinition` de la subcategoría filtradas por Subgrupos del Grupo objetivo.
  - Calcular métricas: `required_count`, `filled_count`, `completion_pct`, `valid`.
  - Guardar en `product_item_group_specs` (UNIQUE: `product_item_id, group_id`).

- Comercial (relacional):
  - Validación con constraints y Form Requests (NOT NULL, enums, rangos, referencias a `SupplierQuotation`).
  - Persistir métricas (`valid`, `completion_pct`, `schema_version`, `spec_status`) en la misma fila 1:1.

- Gating operativo:
  - Antes de `PurchaseOrder`: todos los Grupos marcados como `required=true` en `subcategory_groups` deben tener `valid=true` y `completion_pct=100`.

---

## 5. UI y Flujo

- Formularios por Grupo (tabs/accordion) → paneles por Subgrupo → campos desde `AttributeDefinition`.
- Guardado parcial por Grupo:
  - Si el Grupo es JSON → enviar payload del Grupo → validar → `product_item_group_specs`.
  - Si el Grupo es relacional (p. ej., Comercial) → validar y persistir columnas tipadas (y tablas hijas si aplica).
- Banner/estado por Grupo: `VALID|OUTDATED` + `% completado`.
- Especificación unificada (vista/export): merge de todos los Grupos aplicables, mapeando los Grupos relacionales a JSON y combinando con `spec_json` de los demás.

---

## 5.1. Ejemplo de Flujo de Datos: Especificación y Cotización

A continuación, se detalla con un ejemplo práctico cómo fluyen los datos de especificación a través de las entidades, manejando una propuesta de cambio del proveedor.

### Paso 1: Especificación Original del Cliente

El Analista de Ventas define una hielera. El sistema distribuye los datos en las tablas correspondientes.

- **Tabla: `product_items`**
  ```json
  { "id": 123, "name": "Hielera Personalizada 25L", "subcategory_id": 15 }
  ```

- **Tabla: `product_item_dimensions` (Relacional)**
  ```json
  { "product_item_id": 123, "capacity_ml": 25000, "height_mm": 400, ... }
  ```

- **Tabla: `product_item_group_specs` (JSON por Grupo)**
  ```json
  // Fila para el grupo 'atributos_centrales'
  { "product_item_id": 123, "group_id": 2, "spec_json": { "material_exterior": "HDPE", "aislamiento_mm": 30 } }
  ```

### Paso 2: Propuesta Alternativa del Proveedor

Un proveedor oferta un precio competitivo pero usando un material distinto. El analista registra la `SupplierQuotationLine` con el campo `proposed_spec_json`, que contiene la especificación completa propuesta.

- **Tabla: `supplier_quotation_lines`**
  ```json
  {
    "id": 99,
    "supplier_quotation_id": 88,
    "unit_price": 18.50,
    "proposed_spec_json": {
      "material_exterior": "PP",       // <-- CAMBIO PROPUESTO
      "aislamiento_mm": 25,          // <-- CAMBIO PROPUESTO
      "capacity_litros": 25,
      "height_cm": 40
    }
  }
  ```

### Paso 3: Aceptación Interna y Sincronización Híbrida

El equipo interno acepta la propuesta. La acción **"Actualizar Producto con Propuesta de Proveedor"** dispara una sincronización que descompone el `proposed_spec_json` y actualiza las tablas de destino.

- **Tabla: `product_item_dimensions` (Relacional)**: No se modifica, ya que las dimensiones no cambiaron.

- **Tabla: `product_item_group_specs` (JSON por Grupo) - ACTUALIZADA**
  ```json
  // Se actualiza la fila del grupo 'atributos_centrales'
  {
    "product_item_id": 123,
    "group_id": 2,
    "spec_json": {
      "material_exterior": "PP", // <-- VALOR ACTUALIZADO
      "aislamiento_mm": 25       // <-- VALOR ACTUALIZADO
    }
  }
  ```

### Paso 4: Proyección de Datos Comerciales

Tras aceptar la especificación técnica, los datos comerciales de la cotización se proyectan a las tablas relacionales correspondientes.

- **Tabla: `product_item_commercial` (Relacional) - ACTUALIZADA**
  ```json
  {
    "product_item_id": 123,
    "valid": true,
    "completion_pct": 100,
    "selected_supplier_id": 55,
    "currency": "USD",
    "incoterm": "FOB",
    "fob_unit_cost_minor": 1850
  }
  ```

- **Tabla: `product_item_price_tiers` (Relacional) - CREADA**
  ```json
  { "product_item_id": 123, "incoterm": "FOB", "min_qty": 500, "unit_cost_minor": 1800 }
  ```

Este flujo demuestra cómo el modelo híbrido mantiene la integridad de los datos críticos en tablas relacionales mientras conserva la flexibilidad del JSON para atributos descriptivos, todo dentro de un proceso de cambio controlado y auditable.

---

## 6. Dominios relacionales sugeridos (tablas)

- Comercial y Abastecimiento
  - `product_item_commercial` (1:1 con `product_items`). Ver diagrama.
  - `product_item_price_tiers(product_item_id, incoterm, min_qty, unit_cost_minor, currency)`.

- Embalaje y Logística
  - `product_item_packaging(product_item_id, layer enum:UNIT,INNER,MASTER, type, material, length_mm, width_mm, height_mm, net_weight_g, gross_weight_g, units_per_layer, marking json)`
  - `product_item_palletization(product_item_id, units_per_pallet, pallet_type, layout json)`

- Dimensiones (Atributos Centrales)
  - Opción fija: `product_item_dimensions(product_item_id, length_mm, width_mm, height_mm, diameter_mm, capacity_ml, thickness_mm, tolerances json)`
  - Opción flexible: `product_item_measurements(product_item_id, key, value_int, unit)`

- Impresión (Visual y Marca)
  - `product_item_imprints(product_item_id, location, method, colors int, area_width_mm, area_height_mm, ink_type, notes)`

- Colores (Visual y Marca)
  - `product_item_colors(product_item_id, pantone_code, role enum:PRODUCT,IMPRINT)`

- Tallas (Textiles)
  - `product_item_sizes(product_item_id, size_code)`
  - Opcional: `product_item_size_charts(product_item_id, size_code, chest_mm, length_mm, sleeve_mm, tolerance_mm)`

- Etiquetado (Visual y Marca)
  - `product_item_labels(product_item_id, label_type, material, width_mm, height_mm, attachment, content json)`

---

## 7. Tablas de soporte para layout/obligatoriedad

- `subcategory_groups(subcategory_id, group_id, order, visible, required)`
- `subcategory_subgroups(subcategory_id, subgroup_id, order, visible)`

Estas tablas gobiernan el orden y obligatoriedad por subcategoría sin hardcodear en UI.

---

## 8. Migraciones (resumen)

- `product_item_group_specs`: `product_item_id:fk`, `group_id:fk`, `spec_json:json`, `schema_version:int`, `spec_status:enum`, `required_count:int`, `filled_count:int`, `completion_pct:decimal(5,2)`, `valid:boolean`, `timestamps`, UNIQUE(`product_item_id`,`group_id`).
- `product_item_commercial`: columnas indicadas en el diagrama + índices en `hs_code`, `selected_supplier_id` y `currency,incoterm`.
- Tablas de dominios (packaging, dimensions, imprints, colors, sizes): índices por `product_item_id` y claves de negocio (p. ej., `location`, `size_code`).
- `subcategory_groups` y `subcategory_subgroups` con índices por `subcategory_id, order`.

Ver `Guia_Implementacion_Taxonomia_Grupos_Subgrupos.md` y `Guia_Blueprint_Generar_Modelos_Laravel.md` para YAML y snippets.

---

## 9. Ejemplos de Especificación Híbrida (3 productos)

A continuación se muestran ejemplos de cómo se persistirían las especificaciones para tres productos distintos, distribuyendo los datos entre tablas relacionales y la tabla `product_item_group_specs` (JSON por Grupo).

### Ejemplo 1: Polera (Textiles) — id=101

- **Datos Relacionales:**
  - `product_items`: `{ id: 101, name: "Polera Básica 180G", ... }`
  - `product_item_imprints`: `[{ product_item_id: 101, location: "pecho_frontal", method: "serigrafia", colors: 1, area_width_mm: 280, area_height_mm: 200 }]`
  - `product_item_sizes`: `[{ product_item_id: 101, size_code: "S" }, { product_item_id: 101, size_code: "M" }, ...]`
  - `product_item_packaging`: `[{ product_item_id: 101, layer: "UNIT", type: "polybag" }, { product_item_id: 101, layer: "MASTER", type: "box", units_per_layer: 50 }]`
  - `product_item_commercial`: `{ product_item_id: 101, currency: "USD", incoterm: "FOB", hs_code: "6109.10", selected_supplier_id: 42, fob_unit_cost_minor: 350 }`
  - `product_item_price_tiers`: `[{ product_item_id: 101, incoterm: "FOB", min_qty: 500, unit_cost_minor: 320 }]`

- **Datos JSON por Grupo (`product_item_group_specs`):**
  - Fila `group_id`='atributos_centrales': `{ "spec_json": { "fabric_gsm": 180, "material": "algodon" } }`
  - Fila `group_id`='textiles': `{ "spec_json": { "care_symbols": ["wash_30c", "no_bleach", "iron_low"] } }`

### Ejemplo 2: Botella de Agua (Merchandising) — id=202

- **Datos Relacionales:**
  - `product_items`: `{ id: 202, name: "Botella 600ml Tritan", ... }`
  - `product_item_dimensions`: `{ product_item_id: 202, capacity_ml: 600, height_mm: 230, diameter_mm: 65 }`
  - `product_item_imprints`: `[{ product_item_id: 202, location: "cuerpo_lateral", method: "tampografia", colors: 1 }]`
  - `product_item_colors`: `[{ product_item_id: 202, pantone_code: "PMS 286C", role: "PRODUCT" }]`
  - `product_item_commercial`: `{ product_item_id: 202, currency: "USD", incoterm: "EXW", hs_code: "3926.90", selected_supplier_id: 57, exw_unit_cost_minor: 145 }`

- **Datos JSON por Grupo (`product_item_group_specs`):**
  - Fila `group_id`='visual_marca': `{ "spec_json": { "brand_notes": "Logo 1C lado A, aprobado según maqueta v2" } }`
  - Fila `group_id`='uso_ciclo_vida': `{ "spec_json": { "primary_intended_use": "giveaway", "expected_lifespan": "24m" } }`

### Ejemplo 3: Pendón Roller (Material POP/PDV) — id=303

- **Datos Relacionales:**
  - `product_items`: `{ id: 303, name: "Pendón Roller 85x200 cm", ... }`
  - `product_item_dimensions`: `{ product_item_id: 303, length_mm: 850, height_mm: 2000 }`
  - `product_item_imprints`: `[{ product_item_id: 303, location: "grafica_tela", method: "impresion_digital", colors: 4 }]`
  - `product_item_packaging`: `[{ product_item_id: 303, layer: "UNIT", type: "tube" }, { product_item_id: 303, layer: "MASTER", type: "box", units_per_layer: 6 }]`
  - `product_item_commercial`: `{ product_item_id: 303, currency: "USD", incoterm: "FOB", hs_code: "4911.10", selected_supplier_id: 73, fob_unit_cost_minor: 1250 }`

- **Datos JSON por Grupo (`product_item_group_specs`):**
  - Fila `group_id`='atributos_centrales': `{ "spec_json": { "structural_design": "roller", "assembly_required": true } }`
  - Fila `group_id`='arte_diseno': `{ "spec_json": { "accepted_file_formats": [".AI", ".PDF"], "color_mode": "CMYK" } }`

---

## 10. Gating y Métricas

- Para cada `ProductItem`, evaluar los Grupos `required` (por subcategoría) revisando:
  - Grupos JSON: `product_item_group_specs.valid = true` y `completion_pct = 100`.
  - Grupo Comercial: `product_item_commercial.valid = true` y `completion_pct = 100`.
- Bloquear emisión de `PurchaseOrder` si falta completitud en algún Grupo obligatorio.

---

## 11. Pros y Contras

Pros
- Modularidad: guardado/validación por Grupo; colaboración por secciones.
- Integridad y reporting donde importa (comercial, empaque, medidas, imprints, tallas, colores).
- Evolución sin migraciones para la mayoría de los atributos (JSON por Grupo).
- Gating claro por grupos obligatorios; dashboards rápidos con métricas por Grupo.

Contras
- Requiere merge para exportar la especificación unificada.
- Validaciones cross-Grupo, si existen, deben orquestar lecturas múltiples.
- Más tablas (por dominio), aunque bajas cardinalidades y beneficios claros.

---

## 12. Plan de Migración

1) Crear tablas: `product_item_group_specs`, `product_item_commercial` (+ `price_tiers`) y dominios priorizados (packaging, dimensions, imprints).
2) Backfill desde `product_items.spec_json`:
   - Separar por Grupo y cargar `product_item_group_specs`.
   - Mapear campos comerciales/medidas/packaging a sus tablas relacionales.
3) Actualizar UI a guardado por Grupo; comerciales a formularios tipados.
4) Activar métricas y gating por Grupo; auditar.
5) Opcional: retirar `product_items.spec_json` cuando toda la UI use el nuevo esquema.

---

## 13. Notas de Implementación

- Usar unidades SI canónicas (mm/ml/g) y `*_minor` para montos; representar moneda aparte.
- Mantener `AttributeDefinition.key` único global para evitar colisiones entre Grupos.
- Estrategia de cache: definiciones por subcategoría y composición Grupo/Subgrupo; invalidar al publicar cambios.
- Auditoría por Grupo: registrar diffs de `spec_json` o columnas alteradas y métricas recalculadas.

---

## 14. Evolución Futura

- Proyección EAV tipada (`product_spec_values`) para atributos “calientes” consultables (índices por atributo).
- Vistas materializadas (PostgreSQL) para KPIs por Grupo/Subgrupo.
- Generación automática de plantillas de arte y etiquetas desde las definiciones.

