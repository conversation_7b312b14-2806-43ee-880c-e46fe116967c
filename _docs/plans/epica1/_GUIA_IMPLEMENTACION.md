### **Guía de Implementación para Agente de IA: Taxonomía y Entidad de Producto**

**Objetivo:** Implementar el sistema de taxonomía de productos y la gestión de la entidad `ProductItem` con su modelo de especificaciones híbrido.

**Instrucción:** Procesa los siguientes documentos en el orden presentado para asegurar una correcta implementación.

---

#### **Sección 1: El Núcleo de la Implementación (Lectura Obligatoria)**

Estos documentos contienen la especificación técnica y las guías de implementación. Son la fuente de verdad para la arquitectura.

1.  **`@Especificacion_Enfoque_Hibrido_Grupos_Relacional_JSON.md`**
    *   **Contenido Clave:** Describe la arquitectura de datos fundamental que separa las especificaciones por grupos, usando tablas relacionales para datos críticos (comercial, dimensiones) y JSON para datos flexibles. Incluye el diagrama de Entidad-Relación (ERD) y las métricas de validación (`valid`, `completion_pct`). **Este es el documento más importante.**

2.  **`@Guia_Implementacion_Taxonomia_Grupos_Subgrupos.md`**
    *   **Contenido Clave:** Proporciona la guía técnica específica para implementar el catálogo de taxonomía, incluyendo la lógica para la composición de atributos por subcategoría y la gestión de los grupos y subgrupos.

3.  **`@Guia_Blueprint_Generar_Modelos_Laravel.md`**
    *   **Contenido Clave:** Ofrece los snippets de código y la estructura YAML para generar las migraciones y modelos en Laravel, asegurando que la implementación se alinee con el `blueprint_draft.yaml`.

4.  **`@ProductItem_StateMachine.md`**
    *   **Contenido Clave:** Detalla el ciclo de vida completo del `ProductItem`, las transiciones de estado válidas, los disparadores (triggers) y los efectos secundarios. Es esencial para implementar la lógica de negocio.

---

#### **Sección 2: Definiciones de Entidades de Soporte**

Estos documentos detallan los atributos y relaciones de cada entidad que conforma el sistema de taxonomía y producto. Deben ser usados como referencia al crear los modelos y las migraciones.

*   **Producto y Taxonomía:**
    *   `@Producto.md`
    *   `@Categoria_de_Producto.md`
    *   `@Subcategoria_de_Producto.md`
    *   `@Grupo_de_Atributos.md`
    *   `@Definicion_de_Atributo.md`

---

#### **Sección 3: Contexto y Ejemplos Prácticos**

Estos documentos proporcionan el contexto de negocio y ejemplos de datos que ayudarán al agente a validar su implementación y a comprender el propósito de los atributos.

1.  **`@Glosario_ES_EN.md`**
    *   **Contenido Clave:** El diccionario canónico para nombrar correctamente todas las tablas, modelos y campos.

2.  **`@Taxonomia_y_Especificaciones.md`**
    *   **Contenido Clave:** Una visión general de alto nivel del sistema de clasificación, útil para entender la intención de negocio antes de sumergirse en los detalles técnicos del enfoque híbrido.