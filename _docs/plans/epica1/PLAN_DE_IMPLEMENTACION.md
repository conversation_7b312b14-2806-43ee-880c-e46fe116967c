# Guía de Implementación para Agente de IA: Épica 1

**Objetivo:** Implementar el sistema de taxonomía de productos y la gestión de la entidad `ProductItem` con su modelo de especificaciones híbrido, siguiendo la arquitectura y patrones definidos.

**Arquitectura de Referencia:** `@ia_contexto_arquitectura_laravel.md`

---

## Orden de Implementación por Fases

La arquitectura del proyecto sugiere un enfoque **"bottom-up"** (de adentro hacia afuera). Implementa los componentes en el siguiente orden para asegurar que las dependencias se resuelvan correctamente.

### Fase 1: Dominio y Persistencia (Los 'Sustantivos')

Esta es la capa fundamental. Define las estructuras de datos.

1.  **Enums (`app/Enums/`):**
    *   **Qué:** Define todos los estados y tipos como `Backed Enums` de PHP 8.1+. (ej. `ProductItemStatus`, `CostObligationStatus`).
    *   **Por qué:** No tienen dependencias. Son la base del vocabulario del sistema.
    *   **Test:** Crea tests unitarios para cualquier método auxiliar dentro de los Enums (ej. `getLabel()`).

2.  **Modelos y Migraciones (`app/Models/`, `database/migrations/`):
    *   **Qué:** Usando la guía de **Laravel Blueprint** (`@Guia_Blueprint_Generar_Modelos_Laravel.md`), define todos los modelos de la épica en el `draft.yaml` y ejecuta `php artisan blueprint:build`.
    *   **Por qué:** Los modelos dependen de los Enums para los `casts` de atributos.
    *   **Test:** Crea tests unitarios para verificar que las relaciones Eloquent (`belongsTo`, `hasMany`, etc.) son correctas.

### Fase 2: Contratos de Datos

Define cómo fluyen los datos hacia tu lógica de negocio de forma estructurada.

3.  **DTOs - Data Transfer Objects (`app/Data/{DomainName}/`):
    *   **Qué:** Crea los DTOs necesarios para las acciones de negocio, usando `spatie/laravel-data`.
    *   **Por qué:** Dependen de los Modelos y Enums para su estructura.
    *   **Test:** Crea tests unitarios para asegurar que los DTOs se pueden crear desde diferentes fuentes (ej. `Request`, `array`).

### Fase 3: Lógica de Negocio (Los 'Verbos')

Implementa los casos de uso y procesos de negocio.

4.  **Actions / Services (`app/Actions/{DomainName}/`, `app/Services/`):
    *   **Qué:** Crea las clases `Action` para cada caso de uso (ej. `CreateProductItemAction`).
    *   **Por qué:** Consumen Modelos y DTOs para ejecutar la lógica.
    *   **Test:** Crea **Feature Tests** que simulen una petición HTTP y verifiquen que la acción ejecuta la lógica correctamente y el estado final en la base de datos es el esperado. Esta es la parte más importante del testing.

### Fase 4: Lógica de Autorización

Define las reglas de acceso antes de construir la interfaz.

5.  **Policies (`app/Policies/`):
    *   **Qué:** Crea una `Policy` para cada modelo que requiera protección (ej. `ProductItemPolicy`).
    *   **Por qué:** Dependen de los modelos. Definen la lógica de negocio de los permisos.

### Fase 5: Capa de Presentación (UI)

La capa final con la que interactúa el usuario.

6.  **Filament Resources (`app/Filament/Resources/`):
    *   **Qué:** Construye los recursos de Filament para la administración de la taxonomía y los productos.
    *   **Por qué:** Es la capa más externa. Depende de Modelos, Enums, Policies y, crucialmente, debe **delegar la lógica de negocio a las Actions** en lugar de manipular los modelos directamente.
    *   **Features v4:**
        *   **Relationship Managers:** Úsalos para gestionar relaciones uno-a-muchos (ej. `AttributeDefinitionRelationManager` en la página de `ProductSubcategoryResource`).
        *   **Nested Resources:** Aplica anidamiento para crear una navegación jerárquica intuitiva, especialmente para la taxonomía: `/product-categories/{category}/product-subcategories/{subcategory}`.

---

### Resumen del Flujo de Trabajo

1.  **Definir:** Enums → Modelos/Migraciones → DTOs.
2.  **Implementar Lógica:** Actions/Services → Policies.
3.  **Presentar:** Filament Resources / Controladores.
4.  **Testear:** **En paralelo a cada paso.**
