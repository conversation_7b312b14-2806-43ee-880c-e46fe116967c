# CONTEXTO DE PROYECTO: Nomenclatura e Idioma

Documentos relacionados
- Arquitectura y Patrones de Laravel: `ia_contexto_arquitectura_laravel.md`
- Glosario ES → `SI`: `1_docs/2_Diseno_Conceptual_Sistema/Glosario_ES_EN.md`

Índice
- 1. Principios Generales
- 2. Política de Idioma (docs y código)
- 3. Convenciones de Nomenclatura por Artefacto
- 4. Glosario de Dominio (fuente de verdad)
- 5. Roles y Códigos
- 6. Presentación de Estados en Documentación
- 7. Procedimiento para Nuevos Términos
- 8. <PERSON><PERSON> y Estilo (Laravel)

## 1. Principios Generales
- Idioma de código: Inglés.
- Idioma de UI y documentación: Español.
- Estándar de código: PSR-12.
- Principio rector: la claridad prevalece sobre la brevedad.

## 2. Política de Idioma (docs y código)
- En documentación y UI de negocio: usar el término en español seguido del término del sistema de información en backticks en la primera mención de cada sección o párrafo relevante. Ej.: Proyecto (`Project`), Envío de Importación (`ImportShipment`).
- Términos del sistema de información pueden aparecer solos más adelante en la misma sección, siempre que ya se haya introducido su par en español.
- Código (clases, métodos, variables, tablas/columnas, rutas, claves de configuración): siempre en inglés.
- Textos visibles al usuario final y documentación `.md`: español, con términos de SI en backticks según la convención anterior.

## 3. Convenciones de Nomenclatura por Artefacto

| Artefacto | Ubicación | Convención | Ejemplo |
| :--- | :--- | :--- | :--- |
| Model | `app/Models/` | PascalCase, singular | `ProductItem` |
| Table | (Migration) | snake_case, plural | `product_items` |
| Action | `app/Actions/{Domain}/` | `{Verb}{Entity}Action` | `CreateProjectAction` |
| DTO | `app/Data/{Domain}/` | `{UseCase}Data` | `CreateProjectData` |
| Enum | `app/Enums/` | `{Entity}{Property}` | `ProductItemStatus` |
| Service | `app/Services/` | `{Purpose}Service` | `ProjectProfitabilityCalculator` |
| Resource | `app/Filament/Resources/` | `{Entity}Resource` | `ProductItemResource` |
| Policy | `app/Policies/` | `{Entity}Policy` | `ProjectPolicy` |
| Test | `tests/Feature/{Type}/` | `{ClassUnderTest}Test` | `CreateProjectActionTest` |

Nota: Para detalles de relaciones Eloquent, morph maps, controladores, rutas y Blade, ver `ia_contexto_arquitectura_laravel.md` (secciones Persistencia y Eloquent, y HTTP y Presentación).

## 4. Glosario de Dominio (fuente de verdad)

- El glosario canónico se mantiene en: `1_docs/2_Diseno_Conceptual_Sistema/Glosario_ES_EN.md`.
- Antes de generar o revisar código y documentación, consultar y, de ser necesario, proponer actualizaciones en ese archivo.

## 5. Roles y Códigos

- Códigos canónicos de permisos: snake_case. Ej.: Analista de Ventas (`sales_analyst`), Líder de Equipo (`team_leader`).
- En textos de negocio, usar el nombre del rol en español y, opcionalmente, el código entre backticks en la primera mención.

## 6. Presentación de Estados en Documentación

- Usar etiqueta en español seguida del enum en backticks. Ej.: En Propuesta (`Proposed`), En Ejecución (`InProgress`), Entregado (`Delivered`).

## 7. Procedimiento para Nuevos Términos

- Proponer el término en español y su par en inglés en un pull request que actualice el glosario.
- Evitar introducir variantes no canónicas si ya existe un término oficial en el glosario.

## 8. Tooling y Estilo (Laravel)

- Formato: usar Laravel Pint (`vendor/bin/pint`) con preset `laravel` para consistencia con el ecosistema.
- Pruebas: preferir Pest; estructura en `tests/Feature` y `tests/Unit`. Nombrado de archivos consistente y descripciones claras.
- Docblocks: mantenerlos mínimos; preferir tipos nativos, `readonly` y tips modernos de PHP 8.2+.
