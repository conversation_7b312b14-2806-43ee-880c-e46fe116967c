# CONTEXTO DE PROYECTO: Arquitectura y Patrones de Laravel

Documentos relacionados
- Nomenclatura e Idioma: `ia_contexto_nomenclatura_idioma.md`
- Glosario ES → `SI`: `1_docs/2_Diseno_Conceptual_Sistema/Glosario_ES_EN.md`

Índice
- 1. Principios Arquitectónicos
- 2. Estructura de Directorios por Dominio
- 3. Arquitectura de Autorización (4 Capas)
- 4. Patrones de Código Idiomáticos
- 5. Persistencia y Eloquent
- 6. HTTP y Presentación

## 1. Principios Arquitectónicos
- **Separación de Responsabilidades (SoC):** Cada clase tiene un propósito único y bien definido.
- **Controladores Delgados:** La lógica se extrae de los controladores hacia `Actions` y `Services`.
- **Lógica Orientada al Dominio:** El código se organiza por capacidad de negocio, no por tipo de componente.

## 2. Estructura de Directorios por Dominio

La arquitectura es **híbrida y pragmática**: la lógica de negocio se agrupa por dominio, pero los componentes compartidos (Modelos, Enums) son globales y planos.

### Dominios de Negocio Definidos

- **`Sales` (Ventas y Cotización):** Gestiona la creación de proyectos y la interacción con el cliente.
- **`Procurement` (Adquisiciones):** Responsable de la búsqueda y gestión de proveedores.
- **`Production` (Preparación de Producción):** Gestiona las aprobaciones (maquetas, muestras).
- **`Logistics` (Logística):** Se encarga de embarques, aduanas y entrega final.
- **`Financials` (Finanzas):** Controla costos, pagos y rentabilidad.

### Tipos de Clases y su Ubicación

- **Action**
  - **Ubicación:** `app/Actions/{DomainName}/` (ej. `app/Actions/Procurement/`)
  - **Patrón:** Command.
  - **Responsabilidad:** Ejecutar un único caso de uso de negocio. Es un "verbo" del sistema.

- **DTO (Data Transfer Object)**
  - **Ubicación:** `app/Data/{DomainName}/` (ej. `app/Data/Sales/`)
  - **Patrón:** DTO (usando `spatie/laravel-data`).
  - **Responsabilidad:** Servir como un contrato de datos validado y estructurado entre capas.

- **Service**
  - **Ubicación:** `app/Services/` (Plano/Global)
  - **Patrón:** Service Layer.
  - **Responsabilidad:** Orquestar lógica compleja o realizar tareas que involucran **múltiples dominios**.

- **Model**
  - **Ubicación:** `app/Models/` (Plano/Global)
  - **Patrón:** Active Record (Eloquent).
  - **Responsabilidad:** Representar una entidad de la base de datos y sus relaciones.

## 3. Arquitectura de Autorización (4 Capas)

1.  **Capa de Datos (`spatie/laravel-permission`):**
    - **Responsabilidad:** Persistencia de `Roles` y `Permisos`. Es la fuente de verdad.
2.  **Capa de Aislamiento (`Global Scopes`):**
    - **Responsabilidad:** Filtrado automático de consultas a nivel de base de datos para multi-tenancy.
3.  **Capa de Lógica (`Policies`):**
    - **Responsabilidad:** Centralizar la lógica de negocio de las decisiones de autorización.
4.  **Capa de UI (`bezhanov/filament-shield`):**
    - **Responsabilidad:** Vincular automáticamente la visibilidad de los componentes de Filament a los permisos.

## 4. Patrones de Código Idiomáticos

- **Enums como Objetos de Valor:**
  - **Uso:** Además de valores, los `Enums` contienen métodos.
  - **Ejemplos:** `getLabel(): string` y `getColor(): string` para la UI; `canTransitionTo(Status $new): bool` para lógica de negocio.
  - **Principio:** Encapsular el comportamiento dependiente del estado junto al propio estado.

## 5. Persistencia y Eloquent

### 5.1 Relaciones y Pivots

- Tablas pivot: `singular_a`_`singular_b` en orden alfabético. Ej.: `product_item_project`.
- Modelos con pivots personalizados: nombre en PascalCase descriptivo. Ej.: `ProductItemProject` con `->using(ProductItemProject::class)`.
- Claves foráneas: `snake_case` singular terminado en `_id`. Ej.: `customer_id`, `project_id`.
- Migraciones: usar `foreignId()->constrained()` o `foreignIdFor(Model::class)` cuando sea posible.
- Timestamps estándar: `created_at`, `updated_at`; borrado lógico: `deleted_at` con `SoftDeletes`.

### 5.2 Enums y Casts (PHP 8.1+)

- Usar backed enums para estados y tipos y mapearlos con casts en Eloquent.

```php
// app/Enums/ProductItemStatus.php
enum ProductItemStatus: string { case Draft = 'Draft'; case InProduction = 'InProduction'; }

// app/Models/ProductItem.php
class ProductItem extends Model {
    protected $casts = [ 'status' => ProductItemStatus::class ];
}
```

### 5.3 Polimorfismo y Morph Map

- Definir un `morphMap` canónico para nombres cortos y estables que desacoplen el FQCN almacenado.
- Registrar en `AppServiceProvider`:

```php
use Illuminate\Database\Eloquent\Relations\Relation;

public function boot(): void
{
    Relation::enforceMorphMap([
        'product_item' => \App\Models\ProductItem::class,
        'project' => \App\Models\Project::class,
    ]);
}
```

## 6. HTTP y Presentación

### 6.1 Controladores y Rutas

- Controladores: `app/Http/Controllers`, nombre PascalCase con sufijo `Controller`. Ej.: `ProjectController`.
- Rutas REST: nombre de rutas en dot-notation. Ej.: `projects.index`, `projects.show`.
- `Route::resource('projects', ProjectController::class);` para CRUD convencionales.
- Requests: `app/Http/Requests`, nombre `{Action}{Entity}Request`. Ej.: `StoreProjectRequest`.
- API vs Web: prefijos y middlewares separados; nombrar recursos API con `api.` si aplica. Ej.: `api.projects.index`.

### 6.2 Componentes Blade

- Componentes en kebab-case. Ej.: `<x-project-card />` mapea a `resources/views/components/project-card.blade.php`.
- Vistas: rutas en `snake_case` o directorios por dominio. Ej.: `resources/views/projects/index.blade.php`.
