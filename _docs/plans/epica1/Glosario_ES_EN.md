# Glosario ES → `SI`

> Convención: siempre mencionar primero el término de negocio en español y, a continuación, el término del sistema de información en backticks.

## Entidades y Documentos

- Proyecto (`Project`)
- Línea Base del Proyecto (`ProjectBaseline`)
- Producto (`ProductItem`)
- Envío de Importación (`ImportShipment`)
- Cliente (`Customer`)
- Proveedor (`Supplier`)
- Cotización al Cliente (`CustomerQuotation`)
- Cotización de Proveedor (`SupplierQuotation`)
- Orden de Compra a Proveedor (`PurchaseOrder`)
- Orden de Compra del Cliente (`CustomerPurchaseOrder`)
- Obligación de Costo (`CostObligation`)
- Pago (`Payment`)
- Costo de Excepción (`ExceptionCost`)
- Incidente de Proveedor (`SupplierIncident`)
- Cancelación de Cliente (`CustomerCancellation`)
- Consolidación de Producción (`ProductionConsolidation`)
- Contrato de Cambio a Plazo (FEC) (`ForwardExchangeContract`)
- Asignación de FEC (`FecAllocation`)
- Categoría de Producto (`ProductCategory`)
- Subcategoría de Producto (`ProductSubcategory`)
- Historial de eventos (`ActivityLog`)

## Estados y Enumeraciones

- Proyecto (`Project`): En Propuesta (`Proposed`), Confirmado (`Confirmed`), En Ejecución (`InProgress`), Completado (`Completed`), Facturado (`Invoiced`), Archivado (`Archived`), Cancelado (`Cancelled`).
- Producto (`ProductItem`): Borrador (`Draft`), Listo para Sourcing (`ReadyForSourcing`), Sourcing en Progreso (`SourcingInProgress`), Pendiente de Revisión Interna (`InternalReviewPending`), Cotizado al Cliente (`QuotedToCustomer`), Pendiente Aprobación Maqueta (`PendingVmApproval`), Pendiente Aprobación Muestra (`PendingPpsApproval`), Pendiente Aprobación Factura Proforma (`PendingPiApproval`), Listo para Producción (`ReadyForProduction`), En Producción (`InProduction`), En Tránsito Internacional (`InternationalTransit`), En Despacho de Aduanas (`CustomsClearance`), Listo para Entrega Doméstica (`ReadyForDomesticDelivery`), En Entrega Doméstica (`InDomesticDelivery`), Entregado (`Delivered`), Cancelado (`Cancelled`), En Riesgo (`AtRisk`).
- Envío de Importación (`ImportShipment`): Planificación (`Planning`), Recolección Programada (`PickupScheduled`), En Tránsito (`InTransit`), Llegada a Puerto (`ArrivedPort`), En Despacho de Aduanas (`CustomsClearance`), Liberado de Aduana (`Cleared`), En Entrega Doméstica (`DomesticDelivery`), Entregado (`Delivered`).
- Obligación de Costo (`CostObligation.status`): Provisionado (`PROVISIONED`), Comprometido (`COMMITTED`), Aprobado (`APPROVED`).
- Estado de Liquidación de Pago (derivado `payment_status`): Pendiente (`PENDING`), Parcialmente Pagado (`PARTIALLY_PAID`), Pagado (`PAID`).
- Tipo de Costo (`CostObligation.cost_type`): `FOB`, `MOLD`, `SAMPLE`, `SHIPPING`, `CUSTOMS`, `OTHER`, `CREDIT`.

## Roles y Códigos (canónicos)

- Analista de Ventas (`sales_analyst`)
- Analista de Adquisiciones (`procurement_analyst`)
- Analista de Producción (`production_analyst`)
- Analista de COMEX (`comex_analyst`)
- Analista de Logística Nacional (`national_logistics_analyst`)
- Analista de Diseño (`design_analyst`)
- Analista Financiero (`financial_analyst`)
- Líder de Equipo (`team_leader`)
- Administrador (`admin`)

## Variantes no canónicas a evitar

- Evitar: `Client`, `CustomerQuote`, `Baseline`, y siglas como `CPO`. Usar equivalentes oficiales: Cliente (`Customer`), Cotización al Cliente (`CustomerQuotation`), Orden de Compra del Cliente (`CustomerPurchaseOrder`), Línea Base del Proyecto (`ProjectBaseline`).
