# Especificación Detallada: Categoría de Producto (`ProductCategory`)

> Propósito: Nivel superior de la taxonomía para agrupar subcategorías y facilitar navegación, administración y reporting.

Documentos relacionados: ver `Especificacion_Enfoque_Hibrido_Grupos_Relacional_JSON.md`.

---

## 1. Atributos Clave

- `id` (PK)
- `name` (Texto): Nombre legible.
- `slug` (Texto, único): Identificador canónico estable.
- `description` (Texto, opcional)
- `status` (Enum): `ACTIVE`, `INACTIVE` (opcional para ocultar en UI sin borrar).

Índices: único en `slug`.

---

## 2. Relaciones

- `hasMany ProductSubcategory`

---

## 3. Reglas de Negocio

- Modificación de taxonomía reservada a `admin`.
- No eliminar si tiene subcategorías activas; usar `INACTIVE` para despublicar.

Aceptación
- Dado que una `ProductCategory` tiene subcategorías activas, cuando se intente eliminar, entonces el sistema debe bloquear la acción con error claro y sugerir marcar `INACTIVE`.
- Dado que se crea una nueva categoría, cuando se guarda, entonces el `slug` se genera una sola vez y no puede editarse desde UI.

---

## 4. Implementación

- Slug generado una sola vez; cambios requieren migración controlada.
- Orden opcional para UI por campo `name` o lista configurable.

Ejemplos (canónicos sugeridos)
- `Merchandising`
- `Material POP / PDV`
- `Textiles`

Buenas prácticas
- Nombres en singular/colectivo consistente (evitar mezclar "Textil"/"Textiles").
- Descripciones breves orientadas a navegación y reporting.
