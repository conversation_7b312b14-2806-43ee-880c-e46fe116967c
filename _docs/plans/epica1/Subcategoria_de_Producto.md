# Especificación Detallada: Subcategoría de Producto (`ProductSubcategory`)

> Propósito: Segundo nivel de la taxonomía. Determina la composición de atributos y su agrupación por Grupos y Subgrupos aplicable a los `ProductItem` clasificados bajo ella.

Documentos relacionados: ver `Especificacion_Enfoque_Hibrido_Grupos_Relacional_JSON.md`.

---

## 1. Atributos Clave

- `id` (PK)
- `category_id` (FK): `ProductCategory` padre.
- `name` (Texto)
- `slug` (Texto, único)
- `description` (Texto, opcional)
- `status` (Enum): `ACTIVE`, `INACTIVE`

Índices: único en `slug`; índice en `category_id`.

---

## 2. Relaciones

- `belongsTo ProductCategory`
- `hasMany AttributeDefinition`
- `hasMany ProductItem`

---

## 3. Reglas de Negocio

- Define el conjunto de atributos y su orden de presentación (vía `AttributeDefinition.order`) dentro de Subgrupos, organizados bajo Grupos.
- Cambios de definiciones pueden marcar `ProductItem.spec_status = OUTDATED` para los productos existentes.

Aceptación
- Dado que una subcategoría cambia sus `AttributeDefinition` marcadas como requeridas, cuando un `ProductItem` existente se abre en UI, entonces debe visualizarse `spec_status = OUTDATED` con un CTA de revalidación.
- Dado que se desactiva (`INACTIVE`) una subcategoría, cuando se intenta asignar a un nuevo `ProductItem`, entonces la UI debe ocultarla del selector.

---

## 4. Implementación

- Slug inmutable; cambios requieren migración planificada.
- UI genera formularios con navegación por Grupos (tabs/accordion) y paneles por Subgrupos asociados a sus `AttributeDefinition`.

Ejemplos (mínimos viables, alineados a Grupos/Subgrupos canónicos)
- `polera` (Categoría: `Textiles`)
  - Grupos/Subgrupos:
    - `informacion_basica`: `identificacion_producto`, `mensajes_marketing`.
    - `atributos_centrales`: `materiales`, `dimensiones`, `peso_carga` (si aplica), `ensamblaje_estructura` (si aplica).
    - `visual_marca`: `impresion`, `marca`, `color`, `etiquetado`, `arte_diseno`.
    - `textiles`: `textil_corte_calce`, `textil_tallas`, `cuidado`, `textil_gramaje`.
    - `embalaje_logistica`: `embalaje_unitario`, `embalaje_master`.
    - `uso_ciclo_vida`: `uso_previsto`, `almacenamiento`.
    - `comercial_abastecimiento`: según necesidad (`costos_precios`, `moq`, `proveedor`, `aranceles_hs`).
  - Atributos ejemplo: `fabric_gsm` (integer, required, unit=gsm), `material` (enum), `print_method` (enum), `pantone_numbers` (array), `sizes_available` (array), `care_symbols` (array), `unit_packaging_description` (string), `master_carton_dimensions` (object).
- `botella_agua` (Categoría: `Merchandising`)
  - Grupos/Subgrupos:
    - `informacion_basica`: `identificacion_producto`, `mensajes_marketing`.
    - `atributos_centrales`: `materiales`, `dimensiones`, `peso_carga`, `ensamblaje_estructura`.
    - `visual_marca`: `impresion`, `marca`, `color`, `etiquetado`, `arte_diseno`.
    - `embalaje_logistica`: `embalaje_unitario`, `embalaje_interior`, `embalaje_master`, `palletizacion`, `unidad_medida`, `despacho_estimado`.
    - `uso_ciclo_vida`: `uso_previsto`, `almacenamiento`, `vida_util_caducidad` (si aplica).
    - `comercial_abastecimiento`: `costos_precios`, `moq`, `proveedor`, `aranceles_hs`, `regulatorio`, `documentacion_certificacion`.
  - Atributos ejemplo: `capacity_ml` (integer, required, unit=ml), `height_mm` (integer, unit=mm), `diameter_mm` (integer, unit=mm), `material_body` (enum), `print_area_mm` (object), `print_method` (enum), `hs_code` (string), `exw_unit_cost` (integer), `moq_per_sku` (integer).

Convenciones
- `slug` de subcategorías en snake_case sin acentos: `polera`, `botella_agua`.
- Reusar claves canónicas definidas en `AttributeDefinition.key` para favorecer consistencia inter-subcategorías (ej. `print_method`).
