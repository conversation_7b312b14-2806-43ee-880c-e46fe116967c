# Especificación Detallada: Definición de Atributo (`AttributeDefinition`)

> Propósito: Definir, para una subcategoría y su Subgrupo de Atributos, la clave canónica y reglas de validación de cada atributo usado para construir la especificación de un `ProductItem`.

Documentos relacionados: ver `Especificacion_Enfoque_Hibrido_Grupos_Relacional_JSON.md`.

---

## 1. Atributos Clave

- `id` (PK)
- `subcategory_id` (FK): Subcategoría a la que aplica.
- `subgroup_id` (FK): Subgrupo de presentación (pertenece a un Grupo de Atributos).
- `key` (Texto, único): Slug canónico, inmutable (ej. `width_mm`).
- `label_es` (Texto): Etiqueta visible en UI.
- `label_en` (Texto, opcional): Etiqueta en inglés para i18n.
- `type` (Enum): `string|integer|boolean|enum|array|object`.
- `unit` (Texto, opcional): Unidad (ej. `mm`, `gsm`).
- `required` (Boolean)
- `enum_values` (JSON, opcional): Opciones válidas si `type=enum`.
- `min` / `max` (Entero, opcional): Acotaciones numéricas.
- `order` (Entero): Posición en UI dentro del grupo.
- `help_text_es` (Texto, opcional): Ayuda/contexto para el usuario.
- `help_text_en` (Texto, opcional): Ayuda en inglés (i18n).

Índices: único en `key`; índice en (`subcategory_id`, `order`).

---

## 2. Reglas de Negocio y Validación

- `key` es canónica en todo el sistema para evitar ambigüedad; cada subcategoría compone su propio subconjunto y orden.
- Cambios en definiciones relevantes pueden marcar productos existentes como `spec_status = OUTDATED` para revalidación.
- Genera reglas de validación Laravel para `spec_json` (required, tipos, rangos, `in` para enums).

Consideraciones de tipos y unidades
- Números con decimales: en V1 usar `integer` más `unit` con escala conocida (ej. `weight_g` en gramos) o representar como `string` formateada si es imprescindible el decimal. Alternativamente, planificar extensión futura del `type` a `decimal` si el uso lo exige de forma amplia.
- `array`: se interpreta como lista ordenada de valores homogéneos (ej. tallas disponibles). Validar tamaño mínimo/máximo si aplica.
- `object`: JSON estructurado (ej. áreas de impresión complejas). Validación por `json` + reglas específicas en Actions.

---

## 3. Implementación

- Edición reservada a `admin`; auditar cambios (qué, quién, cuándo) y versionar el esquema.
- UI: tabs/accordion por Grupo; paneles por Subgrupo ordenados por `order`.

Ejemplos (extracto)
```yaml
# Subcategoría: polera
- key: fabric_gsm
  label_es: GSM (Gramaje)
  label_en: GSM (Fabric Weight)
  type: integer
  unit: gsm
  required: true
  min: 100
  max: 300
  subgroup: materiales
  order: 10

- key: print_method
  label_es: Método de impresión
  label_en: Print Method
  type: enum
  enum_values: [serigrafia, dtf, bordado]
  required: true
  subgroup: impresion
  order: 20

# Subcategoría: botella_agua
- key: capacity_ml
  label_es: Capacidad
  label_en: Capacity
  type: integer
  unit: ml
  required: true
  min: 100
  max: 2000
  subgroup: dimensiones
  order: 10
```
