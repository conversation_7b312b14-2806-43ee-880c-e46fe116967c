### **Fase 1: <PERSON>ini<PERSON> y Persistencia (Los 'Sustantivos')**

Esta fase establece la base del sistema, definiendo las estructuras de datos, sus relaciones y su vocabulario.

1.  **Crear Enumeraciones (Enums):**
    *   **Tarea:** Definir todos los estados y tipos como `Backed Enums` de PHP 8.1+ en `app/Enums/`.
    *   **Enums a Implementar:**
        *   `ProductItemStatus`: Gestiona el ciclo de vida de un producto (ej. `Draft`, `InProduction`).
        *   `TaxonomyStatus`: Estado para `ProductCategory` y `ProductSubcategory` (ej. `ACTIVE`, `INACTIVE`).
        *   `AttributeDefinitionType`: Tipos de datos para los atributos (ej. `string`, `integer`, `enum`).
        *   `ProductItemGroupSpecStatus`: Estado de validación de una especificación por grupo (ej. `VALID`, `OUTDATED`).
        *   `Uom (Unit of Measure)`: Unidad de medida para productos (ej. `UNITS`, `SETS`).
        *   `PackagingLayer`: Capa de embalaje para `ProductItemPackaging` (ej. `UNIT`, `INNER`, `MASTER`).
        *   `ColorRole`: Rol de un color para `ProductItemColor` (ej. `PRODUCT`, `IMPRINT`).

2.  **Definir y Generar Modelos y Migraciones:**
    *   **Tarea:** Usar `@Guia_Blueprint_Generar_Modelos_Laravel.md` para definir los modelos en `draft.yaml` y ejecutar `php artisan blueprint:build`.
    *   **Modelos de Taxonomía y Producto:**
        *   `ProductCategory`, `ProductSubcategory`, `AttributeGroup`, `AttributeSubgroup`, `AttributeDefinition`: El núcleo del catálogo de atributos.
        *   `ProductItem`: La entidad central que representa un producto específico.
    *   **Modelos del Enfoque Híbrido (Especificaciones):**
        *   `ProductItemGroupSpec`: Almacena especificaciones flexibles en JSON, por grupo.
        *   `ProductItemCommercial`: Tabla relacional 1:1 para datos comerciales críticos.
        *   `ProductItemPriceTier`: Almacena las escalas de precios por volumen.
        *   `ProductItemPackaging`: Datos de embalaje (unitario, interior, master).
        *   `ProductItemDimension`: Medidas físicas del producto.
        *   `ProductItemImprint`: Detalles de las áreas y métodos de impresión.
        *   `ProductItemColor`: Colores específicos del producto o de la impresión.
        *   `ProductItemSize`: Tallas disponibles para el producto (ej. textiles).
        *   `ProductItemLabel`: Información sobre las etiquetas del producto.
    *   **Modelos de Soporte para Layout (UI):**
        *   `SubcategoryGroup`: Tabla pivote para asociar `AttributeGroup` a `ProductSubcategory`, definiendo visibilidad y obligatoriedad.
        *   `SubcategorySubgroup`: Tabla pivote para asociar `AttributeSubgroup` a `ProductSubcategory`, definiendo visibilidad.
    *   **Pruebas:** Crear tests unitarios para verificar relaciones y `casts` en todos los modelos.

### **Fase 2: Contratos de Datos**

Define cómo los datos fluyen de forma estructurada hacia la lógica de negocio.

3.  **Crear DTOs (Data Transfer Objects):**
    *   **Tarea:** Crear DTOs con `spatie/laravel-data` en `app/Data/{DomainName}/`.
    *   **DTOs a Implementar:**
        *   `TaxonomyData`, `AttributeDefinitionData`: Para gestionar el catálogo.
        *   `CreateProductItemData`: Para la creación inicial de un producto.
        *   `UpdateProductSpecsData`: Payload completo para actualizar las especificaciones de un `ProductItem`, incluyendo tanto los datos para JSON como para las tablas relacionales.
        *   `SubcategoryLayoutData`: Para gestionar la configuración de layout en `SubcategoryGroup` y `SubcategorySubgroup`.

### **Fase 3: Lógica de Negocio (Los 'Verbos')**

Implementa los casos de uso de forma aislada y reutilizable.

4.  **Implementar Actions:**
    *   **Tarea:** Crear clases `Action` en `app/Actions/{DomainName}/`.
    *   **Actions a Implementar:**
        *   `CreateProductItemAction`: Orquesta la creación de un `ProductItem`.
        *   `UpdateProductSpecsAction`: Lógica central que recibe `UpdateProductSpecsData` y distribuye los datos de especificaciones entre `ProductItemGroupSpec` y las diversas tablas relacionales (`ProductItemDimension`, `ProductItemPackaging`, etc.).
        *   `PublishTaxonomyChangeAction`: Gestiona los cambios en la taxonomía y marca los `ProductItem` afectados como `OUTDATED`.
        *   `UpdateSubcategoryLayoutAction`: Gestiona la configuración de visibilidad y obligatoriedad de grupos/subgrupos para una subcategoría.

### **Fase 4: Lógica de Autorización**

Define las reglas de acceso para proteger las entidades y acciones del sistema.

5.  **Crear Policies:**
    *   **Tarea:** Generar una `Policy` en `app/Policies/` para cada modelo gestionable.
    *   **Policies a Implementar:** `ProductItemPolicy`, `ProductCategoryPolicy`, `ProductSubcategoryPolicy`, `AttributeDefinitionPolicy`, y policies para los modelos de layout si se gestionan directamente.

### **Fase 5: Capa de Presentación (UI)**

Construye la interfaz de administración para que los usuarios interactúen con el sistema.

6.  **Construir Recursos de Filament:**
    *   **Tarea:** Crear los recursos de Filament en `app/Filament/Resources/`.
    *   **Recursos a Crear y su Configuración:**
        *   `ProductCategoryResource`: CRUD para Categorías.
        *   `ProductSubcategoryResource`: CRUD para Subcategorías, anidado bajo `ProductCategoryResource`. Debe incluir:
            *   **Relationship Manager** para `AttributeDefinition`.
            *   **Relationship Manager** para `SubcategoryGroup` y `SubcategorySubgroup` para gestionar el layout de la UI y los campos requeridos.
        *   `AttributeGroupResource` y `AttributeSubgroupResource`: CRUDs para el catálogo de grupos.
        *   `ProductItemResource`: Recurso principal para productos. El formulario debe:
            *   Delegar la lógica de guardado a las `Actions` (`CreateProductItemAction`, `UpdateProductSpecsAction`).
            *   Renderizar dinámicamente los campos de especificaciones usando Pestañas (Tabs) para los `AttributeGroup` y Secciones (Sections) para los `AttributeSubgroup`, respetando la configuración de `SubcategoryGroup` y `SubcategorySubgroup`.
            *   Gestionar la entrada de datos para **todas** las tablas de especificaciones (JSON y relacionales).