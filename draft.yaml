models:
  ProductCategory:
    name: string:120
    slug: string:120 unique
    status: enum:ACTIVE,INACTIVE default:ACTIVE
    description: text nullable
    softDeletes
    relationships:
      hasMany: ProductSubcategory

  ProductSubcategory:
    category_id: id foreign:product_categories
    name: string:120
    slug: string:120 unique
    status: enum:ACTIVE,INACTIVE default:ACTIVE
    description: text nullable
    softDeletes
    relationships:
      belongsTo: ProductCategory
      hasMany: AttributeDefinition, ProductItem

  AttributeGroup:
    name: string:120
    slug: string:120 unique
    order: integer index
    description: text nullable
    softDeletes
    relationships:
      hasMany: AttributeSubgroup

  AttributeSubgroup:
    group_id: id foreign:attribute_groups
    name: string:120
    slug: string:120 unique
    order: integer index
    description: text nullable
    softDeletes
    relationships:
      belongsTo: AttributeGroup
      hasMany: AttributeDefinition

  AttributeDefinition:
    subcategory_id: id foreign:product_subcategories
    subgroup_id: id foreign:attribute_subgroups
    key: string:120
    label_es: string:200
    label_en: string:200 nullable
    type: enum:string,integer,boolean,enum,array,object
    unit: string:50 nullable
    required: boolean default:false
    enum_values: json nullable
    min: integer nullable
    max: integer nullable
    order: integer default:0
    help_text_es: text nullable
    help_text_en: text nullable
    softDeletes
    relationships:
      belongsTo: ProductSubcategory, AttributeSubgroup
    indexes:
      - unique: subcategory_id, key
      - index: subcategory_id, order
      - index: key

  ProductItem:
    project_id: id nullable foreign:projects
    subcategory_id: id foreign:product_subcategories
    name: string:200
    status: enum:Draft,ReadyForSourcing,SourcingInProgress,InternalReviewPending,QuotedToCustomer,PendingVmApproval,PendingPpsApproval,PendingPiApproval,ReadyForProduction,InProduction,InternationalTransit,CustomsClearance,ReadyForDomesticDelivery,InDomesticDelivery,Delivered,Cancelled,AtRisk default:Draft
    quantity: integer default:0
    uom: enum:UNITS,SETS nullable
    notes: text nullable
    softDeletes
    relationships:
      belongsTo: ProductSubcategory
      hasOne: ProductItemCommercialSpec, ProductItemPalletizationSpec
      hasMany: ProductItemGroupSpec, ProductItemPriceTierSpec, ProductItemPackagingSpec, ProductItemDimensionSpec, ProductItemImprintSpec, ProductItemColorSpec, ProductItemSizeSpec, ProductItemLabelSpec

  ProductItemGroupSpec:
    product_item_id: id foreign:product_items
    group_id: id foreign:attribute_groups
    spec_json: json
    schema_version: integer
    spec_status: enum:VALID,OUTDATED default:VALID
    valid: boolean default:false
    completion_pct: decimal:5,2 default:0
    required_count: integer nullable
    filled_count: integer nullable
    softDeletes
    relationships:
      belongsTo: ProductItem, AttributeGroup
    indexes:
      - unique: product_item_id, group_id

  ProductItemCommercialSpec:
    product_item_id: id foreign:product_items
    selected_supplier_id: id nullable foreign:suppliers
    supplier_quotation_id: id nullable
    currency: string:3 nullable
    incoterm: string:10 nullable
    country_of_origin: string:100 nullable
    hs_code: string:50 nullable
    schema_version: integer nullable
    spec_status: enum:VALID,OUTDATED default:VALID
    valid: boolean default:false
    completion_pct: decimal:5,2 default:0
    softDeletes
    relationships:
      belongsTo: ProductItem
    indexes:
      - index: hs_code
      - index: selected_supplier_id
      - index: currency, incoterm

  ProductItemPriceTierSpec:
    product_item_id: id foreign:product_items
    incoterm: string:10 nullable
    min_qty: integer
    unit_cost_minor: integer
    currency: string:3
    softDeletes
    relationships:
      belongsTo: ProductItem
    indexes:
      - unique: product_item_id, incoterm, min_qty

  ProductItemPackagingSpec:
    product_item_id: id foreign:product_items
    layer: enum:UNIT,INNER,MASTER
    type: string:100 nullable
    material: string:100 nullable
    length_mm: integer nullable
    width_mm: integer nullable
    height_mm: integer nullable
    net_weight_g: integer nullable
    gross_weight_g: integer nullable
    units_per_layer: integer nullable
    marking: json nullable
    softDeletes
    relationships:
      belongsTo: ProductItem

  ProductItemDimensionSpec:
    product_item_id: id foreign:product_items
    length_mm: integer nullable
    width_mm: integer nullable
    height_mm: integer nullable
    diameter_mm: integer nullable
    capacity_ml: integer nullable
    thickness_mm: integer nullable
    tolerances: json nullable
    softDeletes
    relationships:
      belongsTo: ProductItem

  ProductItemImprintSpec:
    product_item_id: id foreign:product_items
    location: string:100 nullable
    method: string:100 nullable
    colors: integer nullable
    area_width_mm: integer nullable
    area_height_mm: integer nullable
    ink_type: string:100 nullable
    notes: text nullable
    softDeletes
    relationships:
      belongsTo: ProductItem

  ProductItemColorSpec:
    product_item_id: id foreign:product_items
    pantone_code: string:100 nullable
    role: enum:PRODUCT,IMPRINT
    softDeletes
    relationships:
      belongsTo: ProductItem

  ProductItemSizeSpec:
    product_item_id: id foreign:product_items
    size_code: string:50
    softDeletes
    relationships:
      belongsTo: ProductItem

  ProductItemLabelSpec:
    product_item_id: id foreign:product_items
    label_type: string:100 nullable
    material: string:100 nullable
    width_mm: integer nullable
    height_mm: integer nullable
    attachment: string:100 nullable
    content: json nullable
    softDeletes
    relationships:
      belongsTo: ProductItem

  ProductItemPalletizationSpec:
    product_item_id: id foreign:product_items
    units_per_pallet: integer nullable
    pallet_type: string:100 nullable
    layout: json nullable
    softDeletes
    relationships:
      belongsTo: ProductItem
