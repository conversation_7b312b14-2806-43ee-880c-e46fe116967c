# Especificación Técnica - Features de Catálogo y Plantilla de Producto
## Implementación desde Cero

### Versión: 1.0
### Fecha: 2025-01-24
### Stack: Laravel 12 + Filament 4 + PostgreSQL 17

> **✅ ESPECIFICACIÓN CORREGIDA Y CONSISTENTE**
> Esta especificación ha sido actualizada para ser completamente consistente con:
> - `listado_taxonomia.md` - Taxonomía real de productos promocionales
> - `listado_dimensiones_especificacion.md` - Estructura de dimensiones y atributos
> - `detalle_especificacion.schema.json` - Schema JSON con tipos de datos específicos

---

## 1. Arquitectura del Sistema

### 1.1 Modelo de Dominio

```mermaid
erDiagram
    ProductCategory ||--o{ ProductSubcategory : contains
    ProductSubcategory ||--o{ ProductType : contains
    ProductType ||--o{ ProductTemplate : defines
    
    Dimension ||--o{ AttributeGroup : organizes
    AttributeGroup ||--o{ Attribute : contains
    
    ProductTemplate }o--o{ Attribute : configures
    ProductTemplate ||--o{ ProductProject : instantiates
```

### 1.2 Jerarquía de Entidades

#### Catálogo de Productos (Taxonomía Real)
```
ProductCategory (Ej: "Merchandising")
├── ProductSubcategory (Ej: "Artículos para Beber")
    ├── ProductType (Ej: "Tazones")
        └── ProductTemplate (Ej: "Tazón Cerámico 350ml")

ProductCategory (Ej: "Material de Punto de Venta (PDV) y Exhibición")
├── ProductSubcategory (Ej: "Señalética y Banners")
    ├── ProductType (Ej: "Pósteres")
        └── ProductTemplate (Ej: "Póster A3 Papel Couché")

ProductCategory (Ej: "Textiles")
├── ProductSubcategory (Ej: "Vestuario (Prendas)")
    ├── ProductType (Ej: "Poleras")
        └── ProductTemplate (Ej: "Polera Algodón 180gsm")
```

#### Sistema de Atributos (Especificación Real)
```
Dimension (Ej: "information_basica_del_producto")
├── AttributeGroup (Ej: "nombre_y_titulo_del_producto")
    ├── Attribute (Ej: "official_product_name")
    ├── Attribute (Ej: "variant_name")
    └── Attribute (Ej: "internal_sku")
├── AttributeGroup (Ej: "descripcion_del_producto")
    ├── Attribute (Ej: "short_marketing_description")
    └── Attribute (Ej: "long_detailed_description")
```

---

## 2. Especificación de Base de Datos

### 2.1 Tablas del Catálogo

#### product_categories
```sql
CREATE TABLE product_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    sort_order INTEGER NOT NULL DEFAULT 0,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP NULL,
    
    CONSTRAINT check_product_category_status 
        CHECK (status IN ('active', 'inactive')),
    
    INDEX idx_product_categories_status_sort (status, sort_order),
    INDEX idx_product_categories_slug (slug),
    INDEX idx_product_categories_name (name)
);
```

#### product_subcategories
```sql
CREATE TABLE product_subcategories (
    id BIGSERIAL PRIMARY KEY,
    product_category_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    sort_order INTEGER NOT NULL DEFAULT 0,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (product_category_id) 
        REFERENCES product_categories(id) ON DELETE RESTRICT,
    
    CONSTRAINT check_product_subcategory_status 
        CHECK (status IN ('active', 'inactive')),
    
    UNIQUE (product_category_id, slug),
    INDEX idx_product_subcategories_category_status (product_category_id, status),
    INDEX idx_product_subcategories_name (name)
);
```

#### product_types
```sql
CREATE TABLE product_types (
    id BIGSERIAL PRIMARY KEY,
    product_subcategory_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    hs_code VARCHAR(20),
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    sort_order INTEGER NOT NULL DEFAULT 0,
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (product_subcategory_id) 
        REFERENCES product_subcategories(id) ON DELETE RESTRICT,
    
    CONSTRAINT check_product_type_status 
        CHECK (status IN ('active', 'inactive')),
    
    UNIQUE (product_subcategory_id, slug),
    INDEX idx_product_types_subcategory_status (product_subcategory_id, status),
    INDEX idx_product_types_hs_code (hs_code)
);
```

#### product_templates
```sql
CREATE TABLE product_templates (
    id BIGSERIAL PRIMARY KEY,
    product_type_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    version VARCHAR(20) NOT NULL DEFAULT '1.0',
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    description JSONB,
    metadata JSONB,
    is_default BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (product_type_id) 
        REFERENCES product_types(id) ON DELETE RESTRICT,
    
    CONSTRAINT check_product_template_status 
        CHECK (status IN ('draft', 'active', 'obsolete', 'archived')),
    
    UNIQUE (product_type_id, slug),
    INDEX idx_product_templates_type_status (product_type_id, status),
    INDEX idx_product_templates_default (is_default),
    INDEX idx_product_templates_version (version)
);
```

### 2.2 Tablas del Sistema de Atributos

#### dimensions
```sql
CREATE TABLE dimensions (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    key VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50),
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_system BOOLEAN NOT NULL DEFAULT false,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    CONSTRAINT check_dimension_status 
        CHECK (status IN ('active', 'inactive')),
    
    INDEX idx_dimensions_sort_order (sort_order),
    INDEX idx_dimensions_key (key)
);
```

#### attribute_groups
```sql
CREATE TABLE attribute_groups (
    id BIGSERIAL PRIMARY KEY,
    dimension_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    key VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_required BOOLEAN NOT NULL DEFAULT false,
    is_system BOOLEAN NOT NULL DEFAULT false,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    ui_config JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (dimension_id) 
        REFERENCES dimensions(id) ON DELETE RESTRICT,
    
    CONSTRAINT check_attribute_group_status 
        CHECK (status IN ('active', 'inactive')),
    
    INDEX idx_attribute_groups_dimension_sort (dimension_id, sort_order),
    INDEX idx_attribute_groups_key (key),
    INDEX idx_attribute_groups_status (status)
);
```

#### attributes
```sql
CREATE TABLE attributes (
    id BIGSERIAL PRIMARY KEY,
    attribute_group_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    key VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    is_required BOOLEAN NOT NULL DEFAULT false,
    is_visible BOOLEAN NOT NULL DEFAULT true,
    is_searchable BOOLEAN NOT NULL DEFAULT false,
    sort_order INTEGER NOT NULL DEFAULT 0,

    -- Campos para configuración de UI
    ui_component VARCHAR(50),
    ui_props JSONB,
    placeholder TEXT,
    help_text TEXT,
    column_span INTEGER NOT NULL DEFAULT 1,

    -- Campos para validación
    validation_rules JSONB,
    default_value JSONB,

    -- Campos específicos para tipos complejos del schema JSON
    options_json JSONB,
    unit VARCHAR(20),
    min_value DECIMAL(15,6),
    max_value DECIMAL(15,6),
    pattern VARCHAR(500),

    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP NULL,

    FOREIGN KEY (attribute_group_id)
        REFERENCES attribute_groups(id) ON DELETE CASCADE,

    CONSTRAINT check_attribute_type
        CHECK (type IN ('string', 'number', 'boolean', 'select', 'multiselect', 'date', 'email', 'url', 'text', 'array',
                       'dimensions2Dmm', 'dimensions3Dmm', 'tolerancesDims', 'specificPartDimensions', 'colorPantone',
                       'fileFormat', 'countryCode', 'currency', 'percentage', 'mm', 'ml', 'kg', 'positiveNumber', 'nonNegativeNumber')),

    UNIQUE (attribute_group_id, key),
    INDEX idx_attributes_group_sort (attribute_group_id, sort_order),
    INDEX idx_attributes_type (type),
    INDEX idx_attributes_visible (is_visible),
    INDEX idx_attributes_searchable (is_searchable)
);
```

### 2.3 Tabla de Relación Template-Attribute

#### product_template_attributes
```sql
CREATE TABLE product_template_attributes (
    id BIGSERIAL PRIMARY KEY,
    product_template_id BIGINT NOT NULL,
    attribute_id BIGINT NOT NULL,
    attribute_group_id BIGINT NOT NULL,
    is_required BOOLEAN NOT NULL DEFAULT false,
    is_visible BOOLEAN NOT NULL DEFAULT true,
    is_searchable BOOLEAN NOT NULL DEFAULT false,
    position INTEGER NOT NULL DEFAULT 0,
    ui_component VARCHAR(50) NOT NULL DEFAULT 'text',
    ui_props JSONB,
    validation_rules JSONB,
    default_value JSONB,
    placeholder TEXT,
    help_text TEXT,
    column_span INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    FOREIGN KEY (product_template_id) 
        REFERENCES product_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (attribute_id) 
        REFERENCES attributes(id) ON DELETE CASCADE,
    FOREIGN KEY (attribute_group_id) 
        REFERENCES attribute_groups(id) ON DELETE CASCADE,
    
    UNIQUE (product_template_id, attribute_id),
    INDEX idx_template_attrs_template_group (product_template_id, attribute_group_id),
    INDEX idx_template_attrs_position (position),
    INDEX idx_template_attrs_visible (is_visible)
);
```

---

## 3. Especificación de Modelos Eloquent

### 3.1 Traits Compartidos

#### HasStatus Trait
```php
<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;

trait HasStatus
{
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }
    
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('status', 'inactive');
    }
    
    public function isActive(): bool
    {
        return $this->status === 'active';
    }
    
    public function isInactive(): bool
    {
        return $this->status === 'inactive';
    }
}
```

#### HasSortOrder Trait
```php
<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;

trait HasSortOrder
{
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
    
    public static function getNextSortOrder($parentId = null): int
    {
        $query = static::query();

        // Si se proporciona un parent ID, filtrar por él
        if ($parentId !== null) {
            // Determinar el campo de relación padre basado en el modelo
            $parentField = match (static::class) {
                'App\Models\ProductSubcategory' => 'product_category_id',
                'App\Models\ProductType' => 'product_subcategory_id',
                'App\Models\ProductTemplate' => 'product_type_id',
                'App\Models\AttributeGroup' => 'dimension_id',
                'App\Models\Attribute' => 'attribute_group_id',
                default => null,
            };

            if ($parentField) {
                $query->where($parentField, $parentId);
            }
        }

        return ($query->max('sort_order') ?? 0) + 10;
    }
}
```

### 3.2 Modelos del Catálogo

#### ProductCategory Model
```php
<?php

namespace App\Models;

use App\Enums\CommonStatus;
use App\Traits\HasStatus;
use App\Traits\HasSortOrder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductCategory extends Model
{
    use HasFactory, SoftDeletes, HasStatus, HasSortOrder;
    
    protected $fillable = [
        'name',
        'slug', 
        'description',
        'status',
        'sort_order',
        'metadata',
    ];
    
    protected $casts = [
        'status' => CommonStatus::class,
        'metadata' => 'array',
        'sort_order' => 'integer',
        'deleted_at' => 'immutable_datetime',
    ];
    
    // Relationships
    public function productSubcategories(): HasMany
    {
        return $this->hasMany(ProductSubcategory::class)
            ->withTrashed()
            ->ordered();
    }
    
    public function activeSubcategories(): HasMany
    {
        return $this->hasMany(ProductSubcategory::class)
            ->active()
            ->ordered();
    }
    
    // Validation Rules
    public static function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:product_categories,name',
            'slug' => 'required|string|max:255|unique:product_categories,slug',
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'integer|min:0',
            'metadata' => 'nullable|array',
        ];
    }
    
    public static function updateRules(int $id): array
    {
        return [
            'name' => "required|string|max:255|unique:product_categories,name,{$id}",
            'slug' => "required|string|max:255|unique:product_categories,slug,{$id}",
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'integer|min:0',
            'metadata' => 'nullable|array',
        ];
    }
}
```

#### ProductSubcategory Model
```php
<?php

namespace App\Models;

use App\Enums\CommonStatus;
use App\Traits\HasStatus;
use App\Traits\HasSortOrder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductSubcategory extends Model
{
    use HasFactory, SoftDeletes, HasStatus, HasSortOrder;

    protected $fillable = [
        'product_category_id',
        'name',
        'slug',
        'description',
        'status',
        'sort_order',
        'metadata',
    ];

    protected $casts = [
        'status' => CommonStatus::class,
        'metadata' => 'array',
        'sort_order' => 'integer',
        'deleted_at' => 'immutable_datetime',
    ];

    // Relationships
    public function productCategory(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class);
    }

    public function productTypes(): HasMany
    {
        return $this->hasMany(ProductType::class)->ordered();
    }

    public function activeProductTypes(): HasMany
    {
        return $this->hasMany(ProductType::class)->active()->ordered();
    }

    public function productTemplates(): HasMany
    {
        return $this->hasMany(ProductTemplate::class)
            ->through('productTypes')
            ->ordered();
    }

    // Validation Rules
    public static function rules(): array
    {
        return [
            'product_category_id' => 'required|exists:product_categories,id',
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'integer|min:0',
            'metadata' => 'nullable|array',
        ];
    }
}
```

#### ProductType Model
```php
<?php

namespace App\Models;

use App\Enums\CommonStatus;
use App\Traits\HasStatus;
use App\Traits\HasSortOrder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductType extends Model
{
    use HasFactory, SoftDeletes, HasStatus, HasSortOrder;

    protected $fillable = [
        'product_subcategory_id',
        'name',
        'slug',
        'hs_code',
        'description',
        'status',
        'sort_order',
        'metadata',
    ];

    protected $casts = [
        'status' => CommonStatus::class,
        'metadata' => 'array',
        'sort_order' => 'integer',
        'deleted_at' => 'immutable_datetime',
    ];

    // Relationships
    public function productSubcategory(): BelongsTo
    {
        return $this->belongsTo(ProductSubcategory::class);
    }

    public function productTemplates(): HasMany
    {
        return $this->hasMany(ProductTemplate::class)->ordered();
    }

    public function activeProductTemplates(): HasMany
    {
        return $this->hasMany(ProductTemplate::class)->active()->ordered();
    }

    public function defaultTemplate(): HasMany
    {
        return $this->hasMany(ProductTemplate::class)
            ->where('is_default', true)
            ->active();
    }

    // Validation Rules
    public static function rules(): array
    {
        return [
            'product_subcategory_id' => 'required|exists:product_subcategories,id',
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255',
            'hs_code' => 'nullable|string|max:20',
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'integer|min:0',
            'metadata' => 'nullable|array',
        ];
    }
}
```

#### ProductTemplate Model
```php
<?php

namespace App\Models;

use App\Enums\ProductTemplateStatus;
use App\Traits\HasStatus;
use App\Traits\HasSortOrder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductTemplate extends Model
{
    use HasFactory, SoftDeletes, HasStatus, HasSortOrder;

    protected $fillable = [
        'product_type_id',
        'name',
        'slug',
        'version',
        'status',
        'description',
        'metadata',
        'is_default',
    ];

    protected $casts = [
        'status' => ProductTemplateStatus::class,
        'description' => 'array',
        'metadata' => 'array',
        'is_default' => 'boolean',
        'deleted_at' => 'immutable_datetime',
    ];

    // Relationships
    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class);
    }

    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'product_template_attributes')
            ->withPivot([
                'attribute_group_id', 'is_required', 'is_visible', 'is_searchable',
                'position', 'ui_component', 'ui_props', 'validation_rules',
                'default_value', 'placeholder', 'help_text', 'column_span'
            ])
            ->withTimestamps()
            ->orderByPivot('position');
    }

    public function attributesByGroup(): HasMany
    {
        return $this->hasMany(ProductTemplateAttribute::class)
            ->with(['attribute', 'attributeGroup'])
            ->orderBy('position');
    }

    public function productProjects(): HasMany
    {
        return $this->hasMany(ProductProject::class);
    }

    // Validation Rules
    public static function rules(): array
    {
        return [
            'product_type_id' => 'required|exists:product_types,id',
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255',
            'version' => 'required|string|max:20',
            'status' => 'required|in:draft,active,obsolete,archived',
            'description' => 'nullable|array',
            'metadata' => 'nullable|array',
            'is_default' => 'boolean',
        ];
    }
}
```

---

## 4. Sistema de Atributos - Modelos

#### Dimension Model
```php
<?php

namespace App\Models;

use App\Traits\HasStatus;
use App\Traits\HasSortOrder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Dimension extends Model
{
    use HasFactory, HasStatus, HasSortOrder;

    protected $fillable = [
        'name',
        'key',
        'description',
        'icon',
        'sort_order',
        'is_system',
        'status',
    ];

    protected $casts = [
        'is_system' => 'boolean',
        'sort_order' => 'integer',
    ];

    // Relationships
    public function attributeGroups(): HasMany
    {
        return $this->hasMany(AttributeGroup::class)->ordered();
    }

    public function activeAttributeGroups(): HasMany
    {
        return $this->hasMany(AttributeGroup::class)->active()->ordered();
    }

    // Validation Rules
    public static function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:100|unique:dimensions,key',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:50',
            'sort_order' => 'integer|min:0',
            'is_system' => 'boolean',
            'status' => 'required|in:active,inactive',
        ];
    }
}
```

#### AttributeGroup Model
```php
<?php

namespace App\Models;

use App\Traits\HasStatus;
use App\Traits\HasSortOrder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AttributeGroup extends Model
{
    use HasFactory, SoftDeletes, HasStatus, HasSortOrder;

    protected $fillable = [
        'dimension_id',
        'name',
        'key',
        'description',
        'sort_order',
        'is_required',
        'is_system',
        'status',
        'ui_config',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'is_system' => 'boolean',
        'sort_order' => 'integer',
        'ui_config' => 'array',
        'deleted_at' => 'immutable_datetime',
    ];

    // Relationships
    public function dimension(): BelongsTo
    {
        return $this->belongsTo(Dimension::class);
    }

    public function attributes(): HasMany
    {
        return $this->hasMany(Attribute::class)->ordered();
    }

    public function activeAttributes(): HasMany
    {
        return $this->hasMany(Attribute::class)->active()->ordered();
    }

    // Validation Rules
    public static function rules(): array
    {
        return [
            'dimension_id' => 'required|exists:dimensions,id',
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:100|unique:attribute_groups,key',
            'description' => 'nullable|string',
            'sort_order' => 'integer|min:0',
            'is_required' => 'boolean',
            'is_system' => 'boolean',
            'status' => 'required|in:active,inactive',
            'ui_config' => 'nullable|array',
        ];
    }
}
```

#### Attribute Model
```php
<?php

namespace App\Models;

use App\Enums\AttributeType;
use App\Traits\HasStatus;
use App\Traits\HasSortOrder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Attribute extends Model
{
    use HasFactory, SoftDeletes, HasStatus, HasSortOrder;

    protected $fillable = [
        'attribute_group_id',
        'name',
        'key',
        'description',
        'type',
        'is_required',
        'is_visible',
        'is_searchable',
        'sort_order',
        'ui_component',
        'ui_props',
        'placeholder',
        'help_text',
        'column_span',
        'validation_rules',
        'default_value',
        'options_json',
        'unit',
        'min_value',
        'max_value',
        'pattern',
    ];

    protected $casts = [
        'type' => AttributeType::class,
        'is_required' => 'boolean',
        'is_visible' => 'boolean',
        'is_searchable' => 'boolean',
        'sort_order' => 'integer',
        'column_span' => 'integer',
        'ui_props' => 'array',
        'validation_rules' => 'array',
        'default_value' => 'array',
        'options_json' => 'array',
        'min_value' => 'decimal:6',
        'max_value' => 'decimal:6',
        'deleted_at' => 'immutable_datetime',
    ];

    // Relationships
    public function attributeGroup(): BelongsTo
    {
        return $this->belongsTo(AttributeGroup::class);
    }

    public function productTemplates(): BelongsToMany
    {
        return $this->belongsToMany(ProductTemplate::class, 'product_template_attributes')
            ->withPivot([
                'attribute_group_id', 'is_required', 'is_visible', 'is_searchable',
                'position', 'ui_component', 'ui_props', 'validation_rules',
                'default_value', 'placeholder', 'help_text', 'column_span'
            ])
            ->withTimestamps();
    }

    public function productVariantAttributeValues(): HasMany
    {
        return $this->hasMany(ProductVariantAttributeValue::class);
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        $name = $this->name;
        if ($this->unit) {
            $name .= " ({$this->unit})";
        }
        if ($this->description) {
            $name .= " - {$this->description}";
        }
        return $name;
    }

    // Métodos para manejar tipos complejos del schema JSON
    public function getValidationRulesForType(): array
    {
        $baseRules = $this->type->getValidationRules();

        // Agregar reglas específicas basadas en configuración
        if ($this->min_value !== null) {
            $baseRules[] = "min:{$this->min_value}";
        }

        if ($this->max_value !== null) {
            $baseRules[] = "max:{$this->max_value}";
        }

        if ($this->pattern) {
            $baseRules[] = "regex:{$this->pattern}";
        }

        if ($this->is_required) {
            $baseRules[] = 'required';
        } else {
            $baseRules[] = 'nullable';
        }

        return $baseRules;
    }

    public function getFormComponent(): array
    {
        $component = [
            'type' => $this->type->defaultComponent(),
            'label' => $this->name,
            'placeholder' => $this->placeholder,
            'help' => $this->help_text,
            'required' => $this->is_required,
            'columnSpan' => $this->column_span,
        ];

        // Configuración específica por tipo
        switch ($this->type) {
            case AttributeType::DIMENSIONS_2D:
            case AttributeType::DIMENSIONS_3D:
                $component['fields'] = $this->getDimensionFields();
                break;

            case AttributeType::SELECT:
            case AttributeType::MULTISELECT:
                $component['options'] = $this->options_json ?? [];
                break;

            case AttributeType::PERCENTAGE:
                $component['min'] = 0;
                $component['max'] = 100;
                $component['suffix'] = '%';
                break;

            case AttributeType::MILLIMETERS:
                $component['suffix'] = 'mm';
                break;

            case AttributeType::MILLILITERS:
                $component['suffix'] = 'ml';
                break;

            case AttributeType::KILOGRAMS:
                $component['suffix'] = 'kg';
                break;
        }

        // Merge con props personalizadas
        if ($this->ui_props) {
            $component = array_merge($component, $this->ui_props);
        }

        return $component;
    }

    private function getDimensionFields(): array
    {
        $fields = [
            'width_mm' => ['label' => 'Ancho (mm)', 'type' => 'number'],
            'height_mm' => ['label' => 'Alto (mm)', 'type' => 'number'],
        ];

        if ($this->type === AttributeType::DIMENSIONS_3D) {
            $fields['depth_mm'] = ['label' => 'Profundidad (mm)', 'type' => 'number'];
        }

        return $fields;
    }

    public function validateValue($value): bool
    {
        $rules = $this->getValidationRulesForType();
        $validator = validator(['value' => $value], ['value' => $rules]);

        return !$validator->fails();
    }

    // Validation Rules
    public static function rules(): array
    {
        $validTypes = [
            'string', 'number', 'boolean', 'select', 'multiselect', 'date', 'email', 'url', 'text', 'array',
            'dimensions2Dmm', 'dimensions3Dmm', 'tolerancesDims', 'specificPartDimensions', 'colorPantone',
            'fileFormat', 'countryCode', 'currency', 'percentage', 'mm', 'ml', 'kg', 'positiveNumber', 'nonNegativeNumber'
        ];

        return [
            'attribute_group_id' => 'required|exists:attribute_groups,id',
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:100',
            'description' => 'nullable|string',
            'type' => 'required|in:' . implode(',', $validTypes),
            'is_required' => 'boolean',
            'is_visible' => 'boolean',
            'is_searchable' => 'boolean',
            'sort_order' => 'integer|min:0',
            'ui_component' => 'nullable|string|max:50',
            'ui_props' => 'nullable|array',
            'placeholder' => 'nullable|string',
            'help_text' => 'nullable|string',
            'column_span' => 'integer|min:1|max:12',
            'validation_rules' => 'nullable|array',
            'default_value' => 'nullable',
            'options_json' => 'nullable|array',
            'unit' => 'nullable|string|max:20',
            'min_value' => 'nullable|numeric',
            'max_value' => 'nullable|numeric|gte:min_value',
            'pattern' => 'nullable|string|max:500',
        ];
    }
}
```

#### ProductTemplateAttribute Model (Pivot)
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductTemplateAttribute extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_template_id',
        'attribute_id',
        'attribute_group_id',
        'is_required',
        'is_visible',
        'is_searchable',
        'position',
        'ui_component',
        'ui_props',
        'validation_rules',
        'default_value',
        'placeholder',
        'help_text',
        'column_span',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'is_visible' => 'boolean',
        'is_searchable' => 'boolean',
        'position' => 'integer',
        'ui_props' => 'array',
        'validation_rules' => 'array',
        'default_value' => 'json',
        'column_span' => 'integer',
    ];

    // Relationships
    public function productTemplate(): BelongsTo
    {
        return $this->belongsTo(ProductTemplate::class);
    }

    public function attribute(): BelongsTo
    {
        return $this->belongsTo(Attribute::class);
    }

    public function attributeGroup(): BelongsTo
    {
        return $this->belongsTo(AttributeGroup::class);
    }

    // Validation Rules
    public static function rules(): array
    {
        return [
            'product_template_id' => 'required|exists:product_templates,id',
            'attribute_id' => 'required|exists:attributes,id',
            'attribute_group_id' => 'required|exists:attribute_groups,id',
            'is_required' => 'boolean',
            'is_visible' => 'boolean',
            'is_searchable' => 'boolean',
            'position' => 'integer|min:0',
            'ui_component' => 'required|string|max:50',
            'ui_props' => 'nullable|array',
            'validation_rules' => 'nullable|array',
            'default_value' => 'nullable',
            'placeholder' => 'nullable|string',
            'help_text' => 'nullable|string',
            'column_span' => 'integer|min:1|max:12',
        ];
    }
}
```

---

## 5. Enums del Sistema

#### CommonStatus Enum
```php
<?php

namespace App\Enums;

enum CommonStatus: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';

    public function label(): string
    {
        return match($this) {
            self::ACTIVE => 'Activo',
            self::INACTIVE => 'Inactivo',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::ACTIVE => 'success',
            self::INACTIVE => 'gray',
        };
    }
}
```

#### ProductTemplateStatus Enum
```php
<?php

namespace App\Enums;

enum ProductTemplateStatus: string
{
    case DRAFT = 'draft';
    case ACTIVE = 'active';
    case OBSOLETE = 'obsolete';
    case ARCHIVED = 'archived';

    public function label(): string
    {
        return match($this) {
            self::DRAFT => 'Borrador',
            self::ACTIVE => 'Activo',
            self::OBSOLETE => 'Obsoleto',
            self::ARCHIVED => 'Archivado',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::DRAFT => 'gray',
            self::ACTIVE => 'success',
            self::OBSOLETE => 'warning',
            self::ARCHIVED => 'danger',
        };
    }
}
```

#### AttributeType Enum
```php
<?php

namespace App\Enums;

enum AttributeType: string
{
    // Tipos básicos
    case STRING = 'string';
    case NUMBER = 'number';
    case BOOLEAN = 'boolean';
    case SELECT = 'select';
    case MULTISELECT = 'multiselect';
    case DATE = 'date';
    case EMAIL = 'email';
    case URL = 'url';
    case TEXT = 'text';
    case ARRAY = 'array';

    // Tipos específicos del schema JSON
    case DIMENSIONS_2D = 'dimensions2Dmm';
    case DIMENSIONS_3D = 'dimensions3Dmm';
    case TOLERANCES = 'tolerancesDims';
    case SPECIFIC_PARTS = 'specificPartDimensions';
    case COLOR_PANTONE = 'colorPantone';
    case FILE_FORMAT = 'fileFormat';
    case COUNTRY_CODE = 'countryCode';
    case CURRENCY = 'currency';
    case PERCENTAGE = 'percentage';
    case MILLIMETERS = 'mm';
    case MILLILITERS = 'ml';
    case KILOGRAMS = 'kg';
    case POSITIVE_NUMBER = 'positiveNumber';
    case NON_NEGATIVE_NUMBER = 'nonNegativeNumber';

    public function label(): string
    {
        return match($this) {
            self::STRING => 'Texto Corto',
            self::NUMBER => 'Número',
            self::BOOLEAN => 'Sí/No',
            self::SELECT => 'Selección Única',
            self::MULTISELECT => 'Selección Múltiple',
            self::DATE => 'Fecha',
            self::EMAIL => 'Email',
            self::URL => 'URL',
            self::TEXT => 'Texto Largo',
            self::ARRAY => 'Lista',
            self::DIMENSIONS_2D => 'Dimensiones 2D (mm)',
            self::DIMENSIONS_3D => 'Dimensiones 3D (mm)',
            self::TOLERANCES => 'Tolerancias Dimensionales',
            self::SPECIFIC_PARTS => 'Dimensiones por Parte',
            self::COLOR_PANTONE => 'Color Pantone',
            self::FILE_FORMAT => 'Formato de Archivo',
            self::COUNTRY_CODE => 'Código de País',
            self::CURRENCY => 'Moneda',
            self::PERCENTAGE => 'Porcentaje',
            self::MILLIMETERS => 'Milímetros',
            self::MILLILITERS => 'Mililitros',
            self::KILOGRAMS => 'Kilogramos',
            self::POSITIVE_NUMBER => 'Número Positivo',
            self::NON_NEGATIVE_NUMBER => 'Número No Negativo',
        ];
    }

    public function defaultComponent(): string
    {
        return match($this) {
            self::STRING => 'text',
            self::NUMBER, self::POSITIVE_NUMBER, self::NON_NEGATIVE_NUMBER => 'number',
            self::BOOLEAN => 'toggle',
            self::SELECT => 'select',
            self::MULTISELECT => 'multiselect',
            self::DATE => 'date',
            self::EMAIL => 'email',
            self::URL => 'url',
            self::TEXT => 'textarea',
            self::ARRAY => 'repeater',
            self::DIMENSIONS_2D, self::DIMENSIONS_3D => 'dimensions',
            self::TOLERANCES => 'tolerances',
            self::SPECIFIC_PARTS => 'parts_dimensions',
            self::COLOR_PANTONE => 'color_pantone',
            self::FILE_FORMAT => 'file_format_select',
            self::COUNTRY_CODE => 'country_select',
            self::CURRENCY => 'currency_select',
            self::PERCENTAGE => 'percentage',
            self::MILLIMETERS, self::MILLILITERS, self::KILOGRAMS => 'measurement',
        };
    }

    public function getValidationRules(): array
    {
        return match($this) {
            self::STRING => ['string', 'max:255'],
            self::NUMBER => ['numeric'],
            self::BOOLEAN => ['boolean'],
            self::EMAIL => ['email'],
            self::URL => ['url'],
            self::TEXT => ['string'],
            self::PERCENTAGE => ['numeric', 'min:0', 'max:100'],
            self::POSITIVE_NUMBER => ['numeric', 'gt:0'],
            self::NON_NEGATIVE_NUMBER => ['numeric', 'gte:0'],
            self::COLOR_PANTONE => ['string', 'regex:/^[A-Za-z0-9\-]+(\s[A-Za-z0-9\-]+)*$/'],
            self::COUNTRY_CODE => ['string', 'regex:/^[A-Z]{2}$/'],
            self::CURRENCY => ['string', 'regex:/^[A-Z]{3}$/'],
            self::FILE_FORMAT => ['string', 'in:ai,pdf,eps,svg,psd,tiff,png,jpg'],
            default => ['string'],
        ];
    }
}
```

---

## 6. Especificación de Filament Resources

### 6.1 Estructura de Directorios
```
app/Filament/Resources/
├── ProductCatalog/
│   ├── ProductCategoryResource.php
│   ├── ProductSubcategoryResource.php
│   ├── ProductTypeResource.php
│   └── ProductTemplateResource.php
├── ProductSpecification/
│   ├── DimensionResource.php
│   ├── AttributeGroupResource.php
│   └── AttributeResource.php
└── Shared/
    ├── Forms/
    ├── Tables/
    └── RelationManagers/
```

### 6.2 ProductCategoryResource

#### Resource Principal
```php
<?php

namespace App\Filament\Resources\ProductCatalog;

use App\Filament\Resources\ProductCatalog\ProductCategoryResource\Pages;
use App\Filament\Resources\ProductCatalog\ProductCategoryResource\RelationManagers;
use App\Models\ProductCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ProductCategoryResource extends Resource
{
    protected static ?string $model = ProductCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';
    protected static ?string $navigationGroup = 'Catálogo de Productos';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationLabel = 'Categorías';
    protected static ?string $modelLabel = 'Categoría';
    protected static ?string $pluralModelLabel = 'Categorías';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Información Básica')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nombre')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn ($state, callable $set) =>
                                $set('slug', \Illuminate\Support\Str::slug($state))
                            ),

                        Forms\Components\TextInput::make('slug')
                            ->label('Slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->rules(['regex:/^[a-z0-9-]+$/']),

                        Forms\Components\Select::make('status')
                            ->label('Estado')
                            ->options(\App\Enums\CommonStatus::class)
                            ->default(\App\Enums\CommonStatus::ACTIVE)
                            ->required()
                            ->native(false),

                        Forms\Components\TextInput::make('sort_order')
                            ->label('Orden')
                            ->numeric()
                            ->default(fn () => ProductCategory::getNextSortOrder())
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Descripción')
                    ->schema([
                        Forms\Components\Textarea::make('description')
                            ->label('Descripción')
                            ->maxLength(1000)
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Metadatos')
                    ->schema([
                        Forms\Components\KeyValue::make('metadata')
                            ->label('Metadatos Adicionales')
                            ->keyLabel('Clave')
                            ->valueLabel('Valor')
                            ->addActionLabel('Agregar metadato')
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nombre')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->label('Slug')
                    ->searchable()
                    ->copyable()
                    ->fontFamily('mono'),

                Tables\Columns\TextColumn::make('productSubcategories_count')
                    ->label('Subcategorías')
                    ->counts('productSubcategories')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Estado')
                    ->badge()
                    ->color(fn ($state) => $state->color()),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Orden')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Creado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Estado')
                    ->options(\App\Enums\CommonStatus::class)
                    ->native(false),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order')
            ->reorderable('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ProductSubcategoriesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductCategories::route('/'),
            'create' => Pages\CreateProductCategory::route('/create'),
            'view' => Pages\ViewProductCategory::route('/{record}'),
            'edit' => Pages\EditProductCategory::route('/{record}/edit'),
        ];
    }
}
```

#### Relation Manager para Subcategorías
```php
<?php

namespace App\Filament\Resources\ProductCatalog\ProductCategoryResource\RelationManagers;

use App\Enums\CommonStatus;
use App\Models\ProductSubcategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ProductSubcategoriesRelationManager extends RelationManager
{
    protected static string $relationship = 'productSubcategories';
    protected static ?string $title = 'Subcategorías';
    protected static ?string $modelLabel = 'Subcategoría';
    protected static ?string $pluralModelLabel = 'Subcategorías';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Nombre')
                    ->required()
                    ->maxLength(255)
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn ($state, callable $set) =>
                        $set('slug', \Illuminate\Support\Str::slug($state))
                    ),

                Forms\Components\TextInput::make('slug')
                    ->label('Slug')
                    ->required()
                    ->maxLength(255)
                    ->unique(ProductSubcategory::class, 'slug', ignoreRecord: true)
                    ->rules(['regex:/^[a-z0-9-]+$/']),

                Forms\Components\Select::make('status')
                    ->label('Estado')
                    ->options(CommonStatus::class)
                    ->default(CommonStatus::ACTIVE)
                    ->required()
                    ->native(false),

                Forms\Components\TextInput::make('sort_order')
                    ->label('Orden')
                    ->numeric()
                    ->default(fn () => ProductSubcategory::getNextSortOrder())
                    ->required(),

                Forms\Components\Textarea::make('description')
                    ->label('Descripción')
                    ->maxLength(1000)
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nombre')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->label('Slug')
                    ->fontFamily('mono')
                    ->copyable(),

                Tables\Columns\TextColumn::make('productTypes_count')
                    ->label('Tipos')
                    ->counts('productTypes')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Estado')
                    ->badge()
                    ->color(fn ($state) => $state->color()),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Orden')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(CommonStatus::class)
                    ->native(false),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order')
            ->reorderable('sort_order');
    }
}
```

### 6.3 ProductTemplateResource Avanzado

#### Formulario con Configuración de Atributos
```php
public static function form(Form $form): Form
{
    return $form
        ->schema([
            Forms\Components\Section::make('Información Básica')
                ->schema([
                    Forms\Components\Select::make('product_type_id')
                        ->label('Tipo de Producto')
                        ->relationship('productType', 'name')
                        ->searchable()
                        ->preload()
                        ->required()
                        ->live()
                        ->afterStateUpdated(function ($state, callable $set) {
                            // Reset template-specific fields when type changes
                            $set('name', null);
                            $set('slug', null);
                        }),

                    Forms\Components\TextInput::make('name')
                        ->label('Nombre de la Plantilla')
                        ->required()
                        ->maxLength(255)
                        ->live(onBlur: true)
                        ->afterStateUpdated(fn ($state, callable $set) =>
                            $set('slug', \Illuminate\Support\Str::slug($state))
                        ),

                    Forms\Components\TextInput::make('slug')
                        ->label('Slug')
                        ->required()
                        ->maxLength(255)
                        ->unique(ignoreRecord: true),

                    Forms\Components\TextInput::make('version')
                        ->label('Versión')
                        ->default('1.0')
                        ->required(),

                    Forms\Components\Select::make('status')
                        ->label('Estado')
                        ->options(\App\Enums\ProductTemplateStatus::class)
                        ->default(\App\Enums\ProductTemplateStatus::DRAFT)
                        ->required()
                        ->native(false),

                    Forms\Components\Toggle::make('is_default')
                        ->label('Plantilla por Defecto')
                        ->helperText('Solo puede haber una plantilla por defecto por tipo de producto'),
                ])
                ->columns(2),

            Forms\Components\Section::make('Descripción')
                ->schema([
                    Forms\Components\KeyValue::make('description')
                        ->label('Descripción Multiidioma')
                        ->keyLabel('Idioma')
                        ->valueLabel('Descripción')
                        ->keyPlaceholder('es, en, fr...')
                        ->valuePlaceholder('Descripción en el idioma correspondiente')
                        ->addActionLabel('Agregar idioma')
                        ->columnSpanFull(),
                ]),

            Forms\Components\Section::make('Configuración de Atributos')
                ->schema([
                    self::buildAttributeConfiguration(),
                ])
                ->collapsible()
                ->persistCollapsed()
                ->compact(),
        ]);
}

private static function buildAttributeConfiguration(): Forms\Components\Component
{
    return Forms\Components\Repeater::make('attributeConfiguration')
        ->label('Atributos de la Plantilla')
        ->relationship('attributesByGroup')
        ->schema([
            Forms\Components\Select::make('attribute_group_id')
                ->label('Grupo de Atributos')
                ->relationship('attributeGroup', 'name')
                ->required()
                ->live()
                ->afterStateUpdated(function ($state, callable $set) {
                    $set('attribute_id', null);
                }),

            Forms\Components\Select::make('attribute_id')
                ->label('Atributo')
                ->options(function (callable $get) {
                    $groupId = $get('attribute_group_id');
                    if (!$groupId) return [];

                    return \App\Models\Attribute::where('attribute_group_id', $groupId)
                        ->active()
                        ->pluck('name', 'id');
                })
                ->required()
                ->searchable(),

            Forms\Components\Grid::make(3)
                ->schema([
                    Forms\Components\Toggle::make('is_required')
                        ->label('Requerido'),

                    Forms\Components\Toggle::make('is_visible')
                        ->label('Visible')
                        ->default(true),

                    Forms\Components\Toggle::make('is_searchable')
                        ->label('Buscable'),
                ]),

            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\TextInput::make('position')
                        ->label('Posición')
                        ->numeric()
                        ->default(0),

                    Forms\Components\Select::make('column_span')
                        ->label('Ancho de Columna')
                        ->options([
                            1 => '1/12',
                            2 => '2/12',
                            3 => '3/12',
                            4 => '4/12',
                            6 => '6/12',
                            12 => '12/12 (Ancho completo)',
                        ])
                        ->default(6)
                        ->native(false),
                ]),

            Forms\Components\Select::make('ui_component')
                ->label('Componente UI')
                ->options([
                    'text' => 'Texto',
                    'textarea' => 'Área de Texto',
                    'number' => 'Número',
                    'select' => 'Selección',
                    'multiselect' => 'Selección Múltiple',
                    'toggle' => 'Interruptor',
                    'date' => 'Fecha',
                    'datetime' => 'Fecha y Hora',
                    'email' => 'Email',
                    'url' => 'URL',
                ])
                ->default('text')
                ->required()
                ->native(false),

            Forms\Components\KeyValue::make('ui_props')
                ->label('Propiedades del Componente')
                ->keyLabel('Propiedad')
                ->valueLabel('Valor')
                ->addActionLabel('Agregar propiedad'),

            Forms\Components\Textarea::make('placeholder')
                ->label('Texto de Ayuda')
                ->rows(2),

            Forms\Components\Textarea::make('help_text')
                ->label('Texto de Ayuda')
                ->rows(2),
        ])
        ->orderColumn('position')
        ->reorderable()
        ->collapsible()
        ->itemLabel(function (array $state): ?string {
            $groupName = \App\Models\AttributeGroup::find($state['attribute_group_id'])?->name ?? 'Sin grupo';
            $attrName = \App\Models\Attribute::find($state['attribute_id'])?->name ?? 'Sin atributo';
            return "{$groupName} → {$attrName}";
        })
        ->addActionLabel('Agregar Atributo')
        ->columnSpanFull();
}
```

---

## 7. Migraciones de Base de Datos

### 7.1 Orden de Ejecución
```php
// Orden correcto de migraciones:
1. create_dimensions_table
2. create_product_categories_table
3. create_product_subcategories_table
4. create_product_types_table
5. create_product_templates_table
6. create_attribute_groups_table
7. create_attributes_table
8. create_product_template_attributes_table
```

### 7.2 Migración de Dimensiones
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('dimensions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('key', 100)->unique();
            $table->text('description')->nullable();
            $table->string('icon', 50)->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_system')->default(false);
            $table->string('status', 20)->default('active');
            $table->timestamps();

            $table->index(['status', 'sort_order']);
            $table->index('key');
        });

        // Add CHECK constraint for status
        DB::statement("ALTER TABLE dimensions ADD CONSTRAINT check_dimension_status CHECK (status IN ('active', 'inactive'))");
    }

    public function down(): void
    {
        Schema::dropIfExists('dimensions');
    }
};
```

### 7.3 Migración de Attributes (Actualizada para Schema JSON)
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('attributes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('attribute_group_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->string('key', 100);
            $table->string('type', 50); // Soporta todos los tipos del AttributeType enum
            $table->text('description')->nullable();
            $table->boolean('is_required')->default(false);
            $table->boolean('is_visible')->default(true);
            $table->boolean('is_searchable')->default(false);
            $table->integer('sort_order')->default(0);

            // Campos para configuración de UI
            $table->string('ui_component', 50)->nullable();
            $table->jsonb('ui_props')->nullable(); // Props específicos del componente
            $table->text('placeholder')->nullable();
            $table->text('help_text')->nullable();
            $table->integer('column_span')->default(1);

            // Campos para validación
            $table->jsonb('validation_rules')->nullable(); // Reglas de validación específicas
            $table->jsonb('default_value')->nullable(); // Valor por defecto

            // Campos específicos para tipos complejos del schema JSON
            $table->jsonb('options_json')->nullable(); // Para selects, multiselects, etc.
            $table->string('unit', 20)->nullable(); // Para medidas (mm, ml, kg, etc.)
            $table->decimal('min_value', 15, 6)->nullable(); // Para números con rango
            $table->decimal('max_value', 15, 6)->nullable();
            $table->string('pattern', 500)->nullable(); // Para validaciones regex

            $table->timestamps();

            // Constraints
            $table->unique(['attribute_group_id', 'key'], 'unique_group_attribute_key');

            // Indexes
            $table->index(['attribute_group_id', 'sort_order']);
            $table->index('type');
            $table->index('is_visible');
            $table->index('is_searchable');
        });

        // Add CHECK constraints for type
        $validTypes = [
            'string', 'number', 'boolean', 'select', 'multiselect', 'date', 'email', 'url', 'text', 'array',
            'dimensions2Dmm', 'dimensions3Dmm', 'tolerancesDims', 'specificPartDimensions', 'colorPantone',
            'fileFormat', 'countryCode', 'currency', 'percentage', 'mm', 'ml', 'kg', 'positiveNumber', 'nonNegativeNumber'
        ];

        DB::statement("ALTER TABLE attributes ADD CONSTRAINT check_attribute_type CHECK (type IN ('" . implode("','", $validTypes) . "'))");
    }

    public function down(): void
    {
        Schema::dropIfExists('attributes');
    }
};
```

### 7.4 Migración de ProductTemplateAttributes
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product_template_attributes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_template_id')->constrained()->cascadeOnDelete();
            $table->foreignId('attribute_id')->constrained()->cascadeOnDelete();
            $table->foreignId('attribute_group_id')->constrained()->cascadeOnDelete();
            $table->boolean('is_required')->default(false);
            $table->boolean('is_visible')->default(true);
            $table->boolean('is_searchable')->default(false);
            $table->integer('position')->default(0);
            $table->string('ui_component', 50)->default('text');
            $table->jsonb('ui_props')->nullable();
            $table->jsonb('validation_rules')->nullable();
            $table->jsonb('default_value')->nullable();
            $table->text('placeholder')->nullable();
            $table->text('help_text')->nullable();
            $table->integer('column_span')->default(1);
            $table->timestamps();

            // Constraints
            $table->unique(['product_template_id', 'attribute_id'], 'unique_template_attribute');

            // Indexes
            $table->index(['product_template_id', 'attribute_group_id'], 'idx_template_group');
            $table->index('position');
            $table->index('is_visible');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('product_template_attributes');
    }
};
```

---

## 8. Seeders del Sistema

### 8.1 DimensionSeeder
```php
<?php

namespace Database\Seeders;

use App\Models\Dimension;
use Illuminate\Database\Seeder;

class DimensionSeeder extends Seeder
{
    public function run(): void
    {
        $dimensions = [
            [
                'key' => 'information_basica_del_producto',
                'name' => 'Información Básica del Producto',
                'description' => 'Datos fundamentales del producto como nombre, descripción y características básicas',
                'icon' => 'heroicon-o-information-circle',
                'sort_order' => 10,
                'is_system' => true,
            ],
            [
                'key' => 'atributos_centrales_del_producto',
                'name' => 'Atributos Centrales del Producto',
                'description' => 'Características principales como material, dimensiones, peso y estructura',
                'icon' => 'heroicon-o-cog-6-tooth',
                'sort_order' => 20,
                'is_system' => true,
            ],
            [
                'key' => 'especificaciones_visuales_y_marca',
                'name' => 'Especificaciones Visuales y de Marca',
                'description' => 'Métodos de impresión, colores, ubicación de logos y requisitos de arte',
                'icon' => 'heroicon-o-eye',
                'sort_order' => 30,
                'is_system' => true,
            ],
            [
                'key' => 'caracteristicas_especificas_textiles',
                'name' => 'Características Específicas de Textiles',
                'description' => 'Corte, tallas, instrucciones de cuidado específicas para productos textiles',
                'icon' => 'heroicon-o-scissors',
                'sort_order' => 40,
                'is_system' => false,
            ],
            [
                'key' => 'embalaje_y_logistica',
                'name' => 'Embalaje y Logística',
                'description' => 'Información de empaque unitario, interior, master y especificaciones de despacho',
                'icon' => 'heroicon-o-cube',
                'sort_order' => 50,
                'is_system' => true,
            ],
            [
                'key' => 'uso_y_ciclo_de_vida',
                'name' => 'Uso y Ciclo de Vida',
                'description' => 'Compatibilidad de uso, vida útil y requisitos de almacenamiento',
                'icon' => 'heroicon-o-arrow-path',
                'sort_order' => 60,
                'is_system' => false,
            ],
            [
                'key' => 'comercial_y_abastecimiento',
                'name' => 'Comercial y Abastecimiento',
                'description' => 'Costos, MOQ, información del proveedor y códigos arancelarios',
                'icon' => 'heroicon-o-currency-dollar',
                'sort_order' => 70,
                'is_system' => true,
            ],
            [
                'key' => 'certificaciones_y_requerimientos',
                'name' => 'Certificaciones y Requerimientos Adicionales',
                'description' => 'Certificaciones de cliente, regulatorias, internas y especializadas',
                'icon' => 'heroicon-o-shield-check',
                'sort_order' => 80,
                'is_system' => false,
            ],
        ];

        foreach ($dimensions as $dimension) {
            Dimension::updateOrCreate(
                ['key' => $dimension['key']],
                $dimension
            );
        }
    }
}
```

### 8.2 ProductTaxonomySeeder
```php
<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductType;
use App\Enums\CommonStatus;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ProductTaxonomySeeder extends Seeder
{
    public function run(): void
    {
        $taxonomies = [
            'Merchandising' => [
                'Artículos para Beber' => [
                    'Tazones' => 'Recipientes, usualmente cerámicos, para bebidas calientes con impresión de marca',
                    'Botellas de agua' => 'Contenedores portátiles reutilizables para hidratación diaria con branding',
                    'Vasos térmicos' => 'Vasos aislados que mantienen temperatura y exhiben identidad de marca',
                ],
                'Instrumentos de Escritura' => [
                    'Lápices' => 'Instrumentos de grafito económicos y ampliamente distribuidos para recordación de marca',
                    'Portaminas' => 'Lápices mecánicos recargables precisos y durables para uso cotidiano',
                    'Destacadores' => 'Marcadores fluorescentes para resaltar texto con amplia superficie de impresión',
                ],
                'Vestuario y Accesorios' => [
                    'Poleras' => 'Prendas básicas de algodón/poliéster ideales para campañas de alto alcance',
                    'Jockeys' => 'Gorras con panel frontal y visera que ofrecen visibilidad de logos',
                    'Lanyards' => 'Cintas porta credenciales personalizables para eventos y oficinas',
                    'Bolsos promocionales' => 'Bolsos ligeros no utilitarios para transporte básico y exposición de marca',
                ],
                'Accesorios Tecnológicos' => [
                    'Pendrives' => 'Unidades USB para almacenamiento de datos con carcasa personalizable',
                    'Baterías externas' => 'Power banks para recarga móvil que refuerzan la presencia de marca',
                    'Soportes para celular' => 'Accesorios de sujeción o apoyo para teléfonos con espacio de impresión',
                ],
                'Artículos de Oficina y Escritorio' => [
                    'Cuadernos' => 'Libretas con portadas personalizables para notas y organización',
                    'Mousepads' => 'Superficies de control para mouse con amplia área gráfica',
                    'Calendarios' => 'Organizadores de fechas con exposición de marca durante todo el año',
                ],
                'Estilo de Vida y Aire Libre' => [
                    'Llaveros' => 'Accesorios compactos para llaves que ofrecen recordación constante',
                    'Paraguas' => 'Sombrillas resistentes que protegen y exhiben la marca en exteriores',
                    'Multiherramientas' => 'Herramientas compactas multifunción que comunican utilidad y calidad',
                ],
                'Premios y Reconocimientos' => [
                    'Galvanos' => 'Placas o bloques grabados para reconocimiento corporativo',
                    'Trofeos' => 'Figuras o copas de premiación con personalización de evento y marca',
                ],
            ],
            'Material de Punto de Venta (PDV) y Exhibición' => [
                'Señalética y Banners' => [
                    'Pósteres' => 'Impresos planos de diversos tamaños para comunicación visual directa',
                    'Pendones roller' => 'Banners enrollables portátiles de rápida instalación y transporte',
                    'Banderas de gran formato' => 'Telas impresas de gran tamaño para visibilidad a distancia',
                    'Adhesivos para ventanas' => 'Gráficas autoadhesivas para superficies acristaladas',
                ],
                'Banderas Miniatura, de Sobremesa y de Mano' => [
                    'Banderas tipo cuchillo' => 'Banderas anguladas con mástil para exteriores o interiores',
                    'Banderas de escritorio' => 'Mini banderas para mesas que refuerzan presencia institucional',
                    'Banderines' => 'Elementos triangulares o rectangulares decorativos para ambientes temáticos',
                    'Banderas de mano' => 'Banderas ligeras para interacción en eventos y desfiles',
                    'Banderas de mondadientes' => 'Mini banderas para alimentos o decoración puntual',
                ],
                'Soportes y Unidades de Exhibición' => [
                    'Glorificadores de productos' => 'Bases o displays que elevan y resaltan artículos clave',
                    'Portafolletos' => 'Organizadores verticales para distribución de material impreso',
                    'Displays pop-up' => 'Estructuras plegables de montaje rápido para ferias y activaciones',
                    'Stands modulares para ferias' => 'Sistemas configurables reutilizables para espacios de exhibición',
                ],
                'Materiales Impresos' => [
                    'Volantes' => 'Impresos económicos de reparto masivo con mensajes puntuales',
                    'Folletos' => 'Publicaciones plegadas con contenido informativo y visual ampliado',
                    'Habladores de mesa' => 'Señaléticas pequeñas de sobremesa para destacar ofertas o atributos',
                    'Flejes para góndola' => 'Tiras gráficas que comunican en estanterías y frentes de góndola',
                ],
                'Mobiliario y Accesorios Promocionales' => [
                    'Mesones de marca' => 'Mostradores personalizados para atención y activaciones',
                    'Carpas para eventos' => 'Estructuras textiles modulares para cobertura y branding exterior',
                    'Vitrinas de exhibición' => 'Muebles con protección y visibilidad para productos destacados',
                ],
                'Pantallas Digitales y Kioscos Interactivos' => [
                    'Pantallas digitales' => 'Monitores o LEDs para reproducción de contenidos audiovisuales',
                    'Kioscos interactivos' => 'Terminales con interfaz táctil para información y captura de datos',
                ],
            ],
            'Textiles' => [
                'Vestuario (Prendas)' => [
                    'Poleras' => 'Prendas superiores básicas de tejido de punto para uso diario y promoción',
                    'Poleras polo' => 'Camisas de punto con cuello y botones que comunican formalidad casual',
                    'Chaquetas' => 'Prendas exteriores para abrigo con áreas de personalización variadas',
                    'Uniformes' => 'Conjuntos de vestimenta institucional que estandarizan imagen y funciones',
                ],
                'Textiles para el Hogar' => [
                    'Toallas' => 'Piezas absorbentes de baño o deporte con bordados o etiquetas de marca',
                    'Mantas' => 'Cobertores textiles para abrigo y decoración del hogar',
                    'Delantales' => 'Prendas protectoras para cocina o trabajo con personalización frontal',
                    'Fundas de cojín' => 'Cubiertas decorativas intercambiables para cojines',
                ],
                'Bolsos y Soluciones de Transporte a base de Tela' => [
                    'Bolsos de lona de alta resistencia' => 'Bolsos durables para carga media a pesada con branding',
                    'Contenedores de tela especializados' => 'Organizadores textiles adaptados a usos específicos',
                ],
                'Textiles Técnicos Especializados' => [
                    'Telas de alto rendimiento' => 'Materiales con características como repelencia, elasticidad y respirabilidad',
                    'Textiles industriales' => 'Tejidos diseñados para exigencias mecánicas, térmicas o químicas',
                ],
                'Tela Cruda/Materiales' => [
                    'Rollos de tela' => 'Material textil continuo listo para corte, confección o impresión',
                ],
            ],
        ];

        foreach ($taxonomies as $categoryName => $subcategories) {
            $category = ProductCategory::updateOrCreate(
                ['name' => $categoryName],
                [
                    'slug' => Str::slug($categoryName),
                    'status' => CommonStatus::ACTIVE,
                    'sort_order' => ProductCategory::getNextSortOrder(),
                ]
            );

            foreach ($subcategories as $subcategoryName => $types) {
                $subcategory = ProductSubcategory::updateOrCreate(
                    [
                        'product_category_id' => $category->id,
                        'name' => $subcategoryName,
                    ],
                    [
                        'slug' => Str::slug($subcategoryName),
                        'status' => CommonStatus::ACTIVE,
                        'sort_order' => ProductSubcategory::getNextSortOrder(),
                    ]
                );

                foreach ($types as $typeName => $typeDescription) {
                    ProductType::updateOrCreate(
                        [
                            'product_subcategory_id' => $subcategory->id,
                            'name' => $typeName,
                        ],
                        [
                            'slug' => Str::slug($typeName),
                            'description' => $typeDescription,
                            'status' => CommonStatus::ACTIVE,
                            'sort_order' => ProductType::getNextSortOrder(),
                        ]
                    );
                }
            }
        }
    }
}
```

### 8.3 AttributeSystemSeeder (Basado en Schema JSON Real)
```php
<?php

namespace Database\Seeders;

use App\Models\Dimension;
use App\Models\AttributeGroup;
use App\Models\Attribute;
use App\Enums\AttributeType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class AttributeSystemSeeder extends Seeder
{
    public function run(): void
    {
        // Obtener dimensiones existentes
        $dimensions = Dimension::all()->keyBy('key');

        // Estructura basada en el schema JSON real
        $attributeStructure = [
            'information_basica_del_producto' => [
                'nombre_y_titulo_del_producto' => [
                    'official_product_name' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Nombre oficial del producto',
                        'required' => true,
                        'placeholder' => 'Ej: Gorro 6 Paneles',
                    ],
                    'variant_name' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Nombre de variante',
                        'required' => false,
                    ],
                    'seo_friendly_title' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Título SEO',
                        'required' => false,
                    ],
                    'internal_sku' => [
                        'type' => AttributeType::STRING,
                        'label' => 'SKU interno',
                        'required' => false,
                    ],
                ],
                'descripcion_del_producto' => [
                    'short_marketing_description' => [
                        'type' => AttributeType::TEXT,
                        'label' => 'Descripción breve',
                        'required' => false,
                        'placeholder' => 'Ej: Gorro de algodón suave y resistente.',
                    ],
                    'long_detailed_description' => [
                        'type' => AttributeType::TEXT,
                        'label' => 'Descripción detallada',
                        'required' => false,
                    ],
                    'key_selling_points' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Puntos de venta clave',
                        'required' => false,
                    ],
                    'target_audience' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Público objetivo',
                        'required' => false,
                    ],
                    'disclaimers' => [
                        'type' => AttributeType::TEXT,
                        'label' => 'Advertencias / disclaimers',
                        'required' => false,
                    ],
                ],
            ],
            'atributos_centrales_del_producto' => [
                'material_composicion' => [
                    'primary_material' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Material principal',
                        'required' => true,
                    ],
                    'secondary_materials' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Materiales secundarios',
                        'required' => false,
                    ],
                    'recycled_content_percentage' => [
                        'type' => AttributeType::PERCENTAGE,
                        'label' => '% de contenido reciclado',
                        'required' => false,
                    ],
                    'material_certifications' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Certificaciones de materiales',
                        'required' => false,
                    ],
                    'specific_material_grade' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Grado específico de material',
                        'required' => false,
                    ],
                    'material_origin' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Origen del material',
                        'required' => false,
                    ],
                ],
                'tamano_dimensiones' => [
                    'overall_external_dimensions' => [
                        'type' => AttributeType::DIMENSIONS_3D,
                        'label' => 'Dimensiones externas',
                        'required' => false,
                    ],
                    'internal_dimensions' => [
                        'type' => AttributeType::DIMENSIONS_3D,
                        'label' => 'Dimensiones internas',
                        'required' => false,
                    ],
                    'diameter' => [
                        'type' => AttributeType::MILLIMETERS,
                        'label' => 'Diámetro (mm)',
                        'required' => false,
                    ],
                    'capacity_volume' => [
                        'type' => AttributeType::MILLILITERS,
                        'label' => 'Capacidad volumétrica (ml)',
                        'required' => false,
                    ],
                    'thickness' => [
                        'type' => AttributeType::MILLIMETERS,
                        'label' => 'Grosor (mm)',
                        'required' => false,
                    ],
                    'specific_part_dimensions' => [
                        'type' => AttributeType::SPECIFIC_PARTS,
                        'label' => 'Dimensiones por parte',
                        'required' => false,
                    ],
                    'dimensional_tolerances' => [
                        'type' => AttributeType::TOLERANCES,
                        'label' => 'Tolerancias dimensionales',
                        'required' => false,
                    ],
                    'folded_collapsed_dimensions' => [
                        'type' => AttributeType::DIMENSIONS_3D,
                        'label' => 'Dimensiones plegado/colapsado',
                        'required' => false,
                    ],
                ],
                'peso_capacidad_de_carga' => [
                    'net_weight' => [
                        'type' => AttributeType::KILOGRAMS,
                        'label' => 'Peso neto (kg)',
                        'required' => false,
                    ],
                    'maximum_load_capacity' => [
                        'type' => AttributeType::KILOGRAMS,
                        'label' => 'Capacidad máxima de carga (kg)',
                        'required' => false,
                    ],
                    'weight_distribution' => [
                        'type' => AttributeType::TEXT,
                        'label' => 'Distribución de peso / notas',
                        'required' => false,
                    ],
                ],
                'gramaje_de_la_tela_gsm' => [
                    'gsm_value' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Gramaje GSM',
                        'required' => false,
                    ],
                    'gsm_tolerance' => [
                        'type' => AttributeType::PERCENTAGE,
                        'label' => 'Tolerancia GSM %',
                        'required' => false,
                    ],
                    'thread_count' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Recuento de hilos',
                        'required' => false,
                    ],
                ],
                'tipo_de_ensamblaje_estructura' => [
                    'assembly_required' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Requiere ensamblaje',
                        'required' => false,
                    ],
                    'assembly_type' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Tipo de ensamblaje',
                        'required' => false,
                    ],
                    'joining_mechanisms' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Mecanismos de unión',
                        'required' => false,
                    ],
                    'structural_design' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Diseño estructural',
                        'required' => false,
                    ],
                    'number_of_parts' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Número de partes',
                        'required' => false,
                    ],
                ],
            ],
            'especificaciones_visuales_y_marca' => [
                'metodo_tecnica_de_impresion' => [
                    'printing_methods' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Métodos de impresión',
                        'required' => false,
                    ],
                    'colors_per_print_location' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Colores por ubicación de impresión',
                        'required' => false,
                    ],
                    'maximum_printable_area' => [
                        'type' => AttributeType::DIMENSIONS_2D,
                        'label' => 'Área máxima imprimible (ancho x alto)',
                        'required' => false,
                    ],
                    'ink_type' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Tipo de tinta',
                        'required' => false,
                    ],
                    'specialty_inks' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Tintas especiales',
                        'required' => false,
                    ],
                    'embroidery_details' => [
                        'type' => AttributeType::TEXT,
                        'label' => 'Detalles de bordado',
                        'required' => false,
                    ],
                ],
                'acabado_tratamiento_de_superficie' => [
                    'surface_texture' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Textura de superficie',
                        'required' => false,
                    ],
                    'lamination_type' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Tipo de laminado',
                        'required' => false,
                    ],
                    'protective_coatings' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Recubrimientos protectores',
                        'required' => false,
                    ],
                    'specific_treatments' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Tratamientos específicos',
                        'required' => false,
                    ],
                ],
                'opciones_de_color_concordancia_pantone' => [
                    'standard_base_colors' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Colores base estándar',
                        'required' => false,
                    ],
                    'custom_color_capability' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Soporta color personalizado',
                        'required' => false,
                    ],
                    'pantone_numbers' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Códigos Pantone',
                        'required' => false,
                    ],
                    'color_consistency_requirements' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Requisitos de consistencia de color',
                        'required' => false,
                    ],
                    'colorfastness_rating' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Solidez del color (escala definida)',
                        'required' => false,
                    ],
                ],
                'ubicacion_de_logo_areas_de_impresion' => [
                    'imprint_locations' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Ubicaciones de impresión',
                        'required' => false,
                    ],
                    'imprint_area_dimensions' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Dimensiones de área por ubicación (ancho x alto)',
                        'required' => false,
                    ],
                    'placement_restrictions' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Restricciones de ubicación',
                        'required' => false,
                    ],
                    'multiple_imprint_locations' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Múltiples ubicaciones',
                        'required' => false,
                    ],
                ],
                'etiquetado_tejido_impreso_otro' => [
                    'label_type' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Tipo de etiqueta (tejida/impresa/otra)',
                        'required' => false,
                    ],
                    'label_material' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Material de etiqueta',
                        'required' => false,
                    ],
                    'label_dimensions' => [
                        'type' => AttributeType::DIMENSIONS_2D,
                        'label' => 'Dimensiones de etiqueta (ancho x alto)',
                        'required' => false,
                    ],
                    'label_information_content' => [
                        'type' => AttributeType::TEXT,
                        'label' => 'Contenido informativo',
                        'required' => false,
                    ],
                    'label_attachment_method' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Método de fijación',
                        'required' => false,
                    ],
                ],
                'requisitos_de_archivos_de_arte_diseno' => [
                    'accepted_file_formats' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Formatos aceptados',
                        'required' => false,
                    ],
                    'minimum_resolution' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Resolución mínima (dpi)',
                        'required' => false,
                    ],
                    'color_mode' => [
                        'type' => AttributeType::SELECT,
                        'label' => 'Modo de color',
                        'required' => false,
                    ],
                    'vector_format_required' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Se requiere vector',
                        'required' => false,
                    ],
                    'bleed_specifications' => [
                        'type' => AttributeType::MILLIMETERS,
                        'label' => 'Sangrado requerido (mm)',
                        'required' => false,
                    ],
                    'fonts_outlined' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Fuentes contorneadas',
                        'required' => false,
                    ],
                    'template_available' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Plantilla disponible',
                        'required' => false,
                    ],
                ],
            ],
            'caracteristicas_especificas_textiles' => [
                'corte_calce_patron_vestuario' => [
                    'fit_style' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Corte / calce',
                        'required' => false,
                    ],
                    'garment_construction' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Construcción de prenda',
                        'required' => false,
                    ],
                    'fabric_pattern' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Patrón de tela',
                        'required' => false,
                    ],
                    'neckline_style' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Tipo de cuello',
                        'required' => false,
                    ],
                    'sleeve_style' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Tipo de manga',
                        'required' => false,
                    ],
                    'hem_style' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Dobladillo',
                        'required' => false,
                    ],
                    'specific_features' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Características específicas',
                        'required' => false,
                    ],
                ],
                'rango_de_tallas_ropa' => [
                    'available_sizes' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Tallas disponibles',
                        'required' => false,
                    ],
                    'sizing_chart' => [
                        'type' => AttributeType::URL,
                        'label' => 'Tabla de tallas (URL o referencia)',
                        'required' => false,
                    ],
                    'sizing_tolerance' => [
                        'type' => AttributeType::PERCENTAGE,
                        'label' => 'Tolerancia de tallaje %',
                        'required' => false,
                    ],
                    'international_size_conversions' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Conversiones internacionales',
                        'required' => false,
                    ],
                ],
                'instrucciones_de_cuidado' => [
                    'washing_instructions' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Lavado',
                        'required' => false,
                    ],
                    'drying_instructions' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Secado',
                        'required' => false,
                    ],
                    'ironing_instructions' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Planchado',
                        'required' => false,
                    ],
                    'bleaching_instructions' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Blanqueo',
                        'required' => false,
                    ],
                    'dry_cleaning_instructions' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Lavado en seco',
                        'required' => false,
                    ],
                    'care_symbols' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Símbolos de cuidado',
                        'required' => false,
                    ],
                ],
            ],
            'embalaje_y_logistica' => [
                'tipo_de_embalaje_embalaje_unitario' => [
                    'unit_packaging_description' => [
                        'type' => AttributeType::TEXT,
                        'label' => 'Descripción del embalaje unitario',
                        'required' => false,
                    ],
                    'unit_packaging_material' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Material del embalaje unitario',
                        'required' => false,
                    ],
                    'unit_packaging_dimensions' => [
                        'type' => AttributeType::DIMENSIONS_3D,
                        'label' => 'Dimensiones del embalaje unitario',
                        'required' => false,
                    ],
                    'unit_packaging_branding' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Branding del embalaje unitario',
                        'required' => false,
                    ],
                    'unit_packaging_closure' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Cierre del embalaje unitario',
                        'required' => false,
                    ],
                ],
                'embalaje_interior' => [
                    'units_per_inner_pack' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Unidades por embalaje interior',
                        'required' => false,
                    ],
                    'inner_packaging_type' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Tipo de embalaje interior',
                        'required' => false,
                    ],
                    'inner_pack_dimensions' => [
                        'type' => AttributeType::DIMENSIONS_3D,
                        'label' => 'Dimensiones embalaje interior',
                        'required' => false,
                    ],
                    'inner_pack_labeling' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Etiquetado embalaje interior',
                        'required' => false,
                    ],
                ],
                'embalaje_master_caja_master_de_despacho' => [
                    'units_per_master_carton' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Unidades por caja master',
                        'required' => false,
                    ],
                    'master_carton_material' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Material caja master',
                        'required' => false,
                    ],
                    'master_carton_dimensions' => [
                        'type' => AttributeType::DIMENSIONS_3D,
                        'label' => 'Dimensiones caja master',
                        'required' => false,
                    ],
                    'master_carton_gross_weight' => [
                        'type' => AttributeType::KILOGRAMS,
                        'label' => 'Peso bruto caja master (kg)',
                        'required' => false,
                    ],
                    'master_carton_net_weight' => [
                        'type' => AttributeType::KILOGRAMS,
                        'label' => 'Peso neto caja master (kg)',
                        'required' => false,
                    ],
                    'master_carton_markings' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Marcaciones caja master',
                        'required' => false,
                    ],
                    'palletization_info' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Paletización',
                        'required' => false,
                    ],
                ],
                'cantidad_por_unidad_udm_unidad_de_medida' => [
                    'unit_of_measure' => [
                        'type' => AttributeType::SELECT,
                        'label' => 'UDM',
                        'required' => false,
                    ],
                    'pricing_ordering_base' => [
                        'type' => AttributeType::SELECT,
                        'label' => 'Base de precio/pedido',
                        'required' => false,
                    ],
                ],
                'dims_y_peso_de_despacho_est_caja_master' => [
                    'estimated_shipping_dimensions' => [
                        'type' => AttributeType::DIMENSIONS_3D,
                        'label' => 'Dims. estimadas de despacho (caja master)',
                        'required' => false,
                    ],
                    'estimated_shipping_weight' => [
                        'type' => AttributeType::KILOGRAMS,
                        'label' => 'Peso estimado de despacho (kg)',
                        'required' => false,
                    ],
                    'volumetric_weight' => [
                        'type' => AttributeType::KILOGRAMS,
                        'label' => 'Peso volumétrico (kg)',
                        'required' => false,
                    ],
                ],
            ],
            'uso_y_ciclo_de_vida' => [
                'compatibilidad_de_uso_uso_previsto' => [
                    'primary_intended_use' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Uso previsto principal',
                        'required' => false,
                    ],
                    'intended_environment' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Ambiente previsto',
                        'required' => false,
                    ],
                    'system_compatibility' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Compatibilidad de sistema',
                        'required' => false,
                    ],
                    'target_user_group' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Grupo de usuario objetivo',
                        'required' => false,
                    ],
                    'expected_lifespan' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Vida útil esperada (meses)',
                        'required' => false,
                    ],
                ],
                'vida_util_caducidad' => [
                    'applicable_shelf_life' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Vida de estante aplicable (meses)',
                        'required' => false,
                    ],
                    'expiry_date_requirements' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Requisitos de fecha de caducidad',
                        'required' => false,
                    ],
                    'batch_coding_requirements' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Requisitos de codificación de lotes',
                        'required' => false,
                    ],
                ],
                'requisitos_de_almacenamiento' => [
                    'storage_temperature' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Temperatura de almacenamiento',
                        'required' => false,
                    ],
                    'stacking_limitations' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Limitaciones de apilamiento',
                        'required' => false,
                    ],
                    'storage_sensitivities' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Sensibilidades de almacenamiento',
                        'required' => false,
                    ],
                ],
            ],
            'comercial_y_abastecimiento' => [
                'costo_unitario_escalas_de_precios' => [
                    'exw_unit_cost' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Costo unitario EXW',
                        'required' => false,
                    ],
                    'fob_unit_cost' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Costo unitario FOB',
                        'required' => false,
                    ],
                    'cif_unit_cost' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Costo unitario CIF',
                        'required' => false,
                    ],
                    'volume_price_tiers' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Escalas de precio por volumen',
                        'required' => false,
                    ],
                    'setup_charges' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Cargos de preparación',
                        'required' => false,
                    ],
                ],
                'cantidad_minima_de_pedido_moq' => [
                    'moq_per_sku' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'MOQ por SKU',
                        'required' => false,
                    ],
                    'moq_per_order' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'MOQ por orden',
                        'required' => false,
                    ],
                ],
                'informacion_del_proveedor' => [
                    'preferred_supplier' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Proveedor preferido',
                        'required' => false,
                    ],
                    'supplier_part_number' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Código de parte del proveedor',
                        'required' => false,
                    ],
                    'country_of_origin' => [
                        'type' => AttributeType::COUNTRY_CODE,
                        'label' => 'País de origen',
                        'required' => false,
                    ],
                ],
                'codigo_del_sistema_armonizado_hs_codigo_arancelario' => [
                    'hs_code' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Código HS',
                        'required' => false,
                    ],
                    'country_specific_codes' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Códigos específicos por país',
                        'required' => false,
                    ],
                ],
            ],
            'certificaciones_y_requerimientos' => [
                'requerimientos_del_cliente' => [
                    'client_specific_certifications' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Certificaciones del cliente',
                        'required' => false,
                    ],
                    'client_audit_requirements' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Requisitos de auditoría del cliente',
                        'required' => false,
                    ],
                    'client_quality_standards' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Estándares de calidad del cliente',
                        'required' => false,
                    ],
                    'client_compliance_documents' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Documentos de cumplimiento del cliente',
                        'required' => false,
                    ],
                ],
                'requerimientos_regulatorios_por_pais' => [
                    'country_specific_regulations' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Regulaciones por país',
                        'required' => false,
                    ],
                    'dangerous_goods_classification' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Clasificación mercancías peligrosas',
                        'required' => false,
                    ],
                    'safety_certifications' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Certificaciones de seguridad',
                        'required' => false,
                    ],
                    'sanitary_registrations' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Registros sanitarios',
                        'required' => false,
                    ],
                ],
                'requerimientos_internos' => [
                    'internal_quality_standards' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Estándares internos de calidad',
                        'required' => false,
                    ],
                    'internal_compliance_requirements' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Requisitos internos de compliance',
                        'required' => false,
                    ],
                    'internal_documentation_requirements' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Documentación interna requerida',
                        'required' => false,
                    ],
                    'internal_audit_requirements' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Requisitos de auditoría interna',
                        'required' => false,
                    ],
                ],
                'certificaciones_de_materiales' => [
                    'material_certifications' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Certificaciones de materiales',
                        'required' => false,
                    ],
                ],
                'certificaciones_de_cumplimiento_y_seguridad' => [
                    'required_standards' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Normas requeridas',
                        'required' => false,
                    ],
                ],
                'documentacion_de_certificacion' => [
                    'testing_documents' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Documentos de pruebas/certificación (referencias)',
                        'required' => false,
                    ],
                    'msds_required' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Requiere MSDS',
                        'required' => false,
                    ],
                ],
                'certificaciones_de_fabrica' => [
                    'factory_audit_status' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Estatus de auditoría de fábrica',
                        'required' => false,
                    ],
                ],
                'certificacion_imo_international_maritime_organization' => [
                    'imo_certification' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Certificación IMO',
                        'required' => false,
                    ],
                ],
                'productos_para_ninos' => [
                    'children_product' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Producto para niños',
                        'required' => false,
                    ],
                    'children_safety_standards' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Normas de seguridad infantil',
                        'required' => false,
                    ],
                    'age_appropriateness' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Edad recomendada mínima (años)',
                        'required' => false,
                    ],
                ],
                'productos_de_consumo_alimentos' => [
                    'food_contact_product' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Producto de contacto con alimentos',
                        'required' => false,
                    ],
                    'food_grade_certification' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Certificación grado alimenticio',
                        'required' => false,
                    ],
                    'food_safety_standards' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Normas de seguridad alimentaria',
                        'required' => false,
                    ],
                ],
                'productos_topicos_piel_pelo_etc' => [
                    'topical_product' => [
                        'type' => AttributeType::BOOLEAN,
                        'label' => 'Producto tópico',
                        'required' => false,
                    ],
                    'dermatological_testing' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Pruebas dermatológicas',
                        'required' => false,
                    ],
                    'skin_safety_standards' => [
                        'type' => AttributeType::ARRAY,
                        'label' => 'Normas de seguridad de piel',
                        'required' => false,
                    ],
                ],
                'niveles_de_aql_acceptable_quality_level' => [
                    'aql_level' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Nivel AQL',
                        'required' => false,
                    ],
                    'aql_inspection_level' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Nivel de inspección AQL',
                        'required' => false,
                    ],
                    'aql_sample_size' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Tamaño de muestra AQL',
                        'required' => false,
                    ],
                    'quality_inspection_frequency' => [
                        'type' => AttributeType::STRING,
                        'label' => 'Frecuencia de inspección de calidad',
                        'required' => false,
                    ],
                ],
                'margen_de_error_y_saldos_de_produccion' => [
                    'defect_percentage' => [
                        'type' => AttributeType::PERCENTAGE,
                        'label' => '% de defecto permitido',
                        'required' => false,
                    ],
                    'overage_quantity' => [
                        'type' => AttributeType::NON_NEGATIVE_NUMBER,
                        'label' => 'Cantidad de sobreproducción permitida',
                        'required' => false,
                    ],
                    'production_buffer' => [
                        'type' => AttributeType::PERCENTAGE,
                        'label' => 'Buffer de producción %',
                        'required' => false,
                    ],
                    'rework_percentage' => [
                        'type' => AttributeType::PERCENTAGE,
                        'label' => '% de retrabajo permitido',
                        'required' => false,
                    ],
                ],
            ],
        ];

        $this->createAttributeStructure($attributeStructure, $dimensions);
    }

    private function createAttributeStructure(array $structure, $dimensions): void
    {
        foreach ($structure as $dimensionKey => $groups) {
            $dimension = $dimensions->get($dimensionKey);
            if (!$dimension) continue;

            foreach ($groups as $groupKey => $attributes) {
                $attributeGroup = AttributeGroup::updateOrCreate(
                    [
                        'dimension_id' => $dimension->id,
                        'key' => $groupKey,
                    ],
                    [
                        'name' => $this->formatGroupName($groupKey),
                        'description' => $this->getGroupDescription($groupKey),
                        'sort_order' => AttributeGroup::getNextSortOrder($dimension->id),
                    ]
                );

                foreach ($attributes as $attributeKey => $config) {
                    Attribute::updateOrCreate(
                        [
                            'attribute_group_id' => $attributeGroup->id,
                            'key' => $attributeKey,
                        ],
                        [
                            'name' => $config['label'],
                            'type' => $config['type'],
                            'is_required' => $config['required'] ?? false,
                            'placeholder' => $config['placeholder'] ?? null,
                            'sort_order' => Attribute::getNextSortOrder($attributeGroup->id),
                        ]
                    );
                }
            }
        }
    }

    private function formatGroupName(string $key): string
    {
        return Str::title(str_replace('_', ' ', $key));
    }

    private function getGroupDescription(string $key): string
    {
        $descriptions = [
            'nombre_y_titulo_del_producto' => 'Identificación y nomenclatura del producto',
            'descripcion_del_producto' => 'Información descriptiva y comercial',
            'material_composicion' => 'Composición y características de materiales',
            'tamano_dimensiones' => 'Medidas y dimensiones físicas',
            'peso_capacidad_de_carga' => 'Especificaciones de peso y capacidad',
            'gramaje_de_la_tela_gsm' => 'Características específicas del tejido',
            'tipo_de_ensamblaje_estructura' => 'Información sobre construcción y ensamblaje',
        ];

        return $descriptions[$key] ?? '';
    }
}
```

---

## 9. Testing Strategy

### 9.1 Unit Tests
```php
<?php

namespace Tests\Unit\Models;

use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Enums\CommonStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductCategoryTest extends TestCase
{
    use RefreshDatabase;

    public function test_product_category_has_many_subcategories(): void
    {
        $category = ProductCategory::factory()->create();
        $subcategories = ProductSubcategory::factory(3)->create([
            'product_category_id' => $category->id,
        ]);

        $this->assertCount(3, $category->productSubcategories);
        $this->assertInstanceOf(ProductSubcategory::class, $category->productSubcategories->first());
    }

    public function test_product_category_can_be_soft_deleted(): void
    {
        $category = ProductCategory::factory()->create();

        $category->delete();

        $this->assertSoftDeleted($category);
        $this->assertDatabaseHas('product_categories', [
            'id' => $category->id,
        ]);
    }

    public function test_active_scope_filters_correctly(): void
    {
        ProductCategory::factory()->create(['status' => CommonStatus::ACTIVE]);
        ProductCategory::factory()->create(['status' => CommonStatus::INACTIVE]);

        $activeCategories = ProductCategory::active()->get();

        $this->assertCount(1, $activeCategories);
        $this->assertEquals(CommonStatus::ACTIVE, $activeCategories->first()->status);
    }

    public function test_validation_rules_work_correctly(): void
    {
        $rules = ProductCategory::rules();

        $this->assertArrayHasKey('name', $rules);
        $this->assertArrayHasKey('slug', $rules);
        $this->assertArrayHasKey('status', $rules);
        $this->assertContains('required', explode('|', $rules['name']));
        $this->assertContains('unique:product_categories,name', explode('|', $rules['name']));
    }
}
```

### 9.2 Feature Tests
```php
<?php

namespace Tests\Feature\Filament;

use App\Filament\Resources\ProductCatalog\ProductCategoryResource;
use App\Models\ProductCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class ProductCategoryResourceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->actingAs(User::factory()->create());
    }

    public function test_can_render_product_category_list_page(): void
    {
        $this->get(ProductCategoryResource::getUrl('index'))
            ->assertSuccessful();
    }

    public function test_can_create_product_category(): void
    {
        $newData = [
            'name' => 'Nueva Categoría',
            'slug' => 'nueva-categoria',
            'status' => 'active',
            'sort_order' => 10,
        ];

        Livewire::test(ProductCategoryResource\Pages\CreateProductCategory::class)
            ->fillForm($newData)
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('product_categories', $newData);
    }

    public function test_can_validate_product_category_data(): void
    {
        Livewire::test(ProductCategoryResource\Pages\CreateProductCategory::class)
            ->fillForm([
                'name' => '',
                'slug' => 'invalid slug with spaces',
            ])
            ->call('create')
            ->assertHasFormErrors(['name', 'slug']);
    }

    public function test_can_edit_product_category(): void
    {
        $category = ProductCategory::factory()->create();

        $newData = [
            'name' => 'Categoría Editada',
            'slug' => 'categoria-editada',
        ];

        Livewire::test(ProductCategoryResource\Pages\EditProductCategory::class, [
            'record' => $category->getRouteKey(),
        ])
            ->fillForm($newData)
            ->call('save')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('product_categories', $newData);
    }
}
```

---

## 10. Performance y Optimización

### 10.1 Índices de Base de Datos
```sql
-- Índices para consultas frecuentes
CREATE INDEX idx_product_categories_status_sort ON product_categories(status, sort_order);
CREATE INDEX idx_product_subcategories_category_status ON product_subcategories(product_category_id, status);
CREATE INDEX idx_product_types_subcategory_status ON product_types(product_subcategory_id, status);
CREATE INDEX idx_product_templates_type_status ON product_templates(product_type_id, status);
CREATE INDEX idx_attribute_groups_dimension_sort ON attribute_groups(dimension_id, sort_order);
CREATE INDEX idx_attributes_group_sort ON attributes(attribute_group_id, sort_order);

-- Índices para búsquedas de texto
CREATE INDEX idx_product_categories_name_gin ON product_categories USING gin(to_tsvector('spanish', name));
CREATE INDEX idx_attributes_name_gin ON attributes USING gin(to_tsvector('spanish', name));
```

### 10.2 Eager Loading Strategies
```php
// En ProductCategoryResource
public static function getEloquentQuery(): Builder
{
    return parent::getEloquentQuery()
        ->with(['productSubcategories' => function ($query) {
            $query->withCount('productTypes');
        }]);
}

// En ProductTemplateResource
public static function getEloquentQuery(): Builder
{
    return parent::getEloquentQuery()
        ->with([
            'productType.productSubcategory.productCategory',
            'attributes' => function ($query) {
                $query->with('attributeGroup.dimension');
            }
        ]);
}
```

### 10.3 Caching Strategy
```php
// Cache para dimensiones (raramente cambian)
public static function getCachedDimensions(): Collection
{
    return Cache::remember('dimensions.active', 3600, function () {
        return Dimension::active()->ordered()->get();
    });
}

// Cache para taxonomía completa
public static function getCachedTaxonomy(): Collection
{
    return Cache::remember('product.taxonomy', 1800, function () {
        return ProductCategory::active()
            ->with(['productSubcategories.productTypes'])
            ->ordered()
            ->get();
    });
}
```

---

## 11. Criterios de Aceptación

### 11.1 Funcionalidad
- ✅ CRUD completo para todas las entidades
- ✅ Jerarquía de navegación funcional
- ✅ Formularios con validación en tiempo real
- ✅ Relaciones entre entidades correctas
- ✅ Soft deletes donde corresponde
- ✅ Búsqueda y filtrado eficiente

### 11.2 Performance
- ✅ Tiempo de carga < 200ms para listados
- ✅ Queries optimizadas con eager loading
- ✅ Índices apropiados implementados
- ✅ Caching para datos estáticos

### 11.3 UX/UI
- ✅ Interfaz intuitiva y consistente
- ✅ Formularios responsivos
- ✅ Feedback visual apropiado
- ✅ Navegación jerárquica clara

### 11.4 Calidad de Código
- ✅ Cobertura de tests > 80%
- ✅ Documentación completa
- ✅ Código siguiendo estándares PSR-12
- ✅ Sin warnings en análisis estático

---

## 12. Resumen de Correcciones Aplicadas

### ✅ **Cambios Críticos Realizados para Consistencia**

#### 12.1 Taxonomía de Productos Actualizada
**Antes:** Ejemplos genéricos (Textiles → Ropa → Camisetas)
**Ahora:** Taxonomía real del sistema PSS:
- **Merchandising** (Artículos para Beber, Instrumentos de Escritura, etc.)
- **Material de Punto de Venta (PDV) y Exhibición** (Señalética, Banderas, etc.)
- **Textiles** (Vestuario, Textiles para el Hogar, etc.)

#### 12.2 Dimensiones Corregidas
**Antes:** Keys incorrectos (`basic_information`, `core_attributes`)
**Ahora:** Keys del schema JSON real:
- `information_basica_del_producto`
- `atributos_centrales_del_producto`
- `especificaciones_visuales_y_marca`
- `caracteristicas_especificas_textiles`
- `embalaje_y_logistica`
- `uso_y_ciclo_de_vida`
- `comercial_y_abastecimiento`
- `certificaciones_y_requerimientos`

#### 12.3 Tipos de Atributos Expandidos
**Antes:** Solo tipos básicos (string, number, boolean)
**Ahora:** Tipos específicos del schema JSON:
- `dimensions2Dmm`, `dimensions3Dmm`
- `tolerancesDims`, `specificPartDimensions`
- `colorPantone`, `fileFormat`
- `countryCode`, `currency`
- `percentage`, `mm`, `ml`, `kg`
- `positiveNumber`, `nonNegativeNumber`

#### 12.4 Modelo Attribute Mejorado
**Agregado:**
- Métodos para validación específica por tipo
- Generación automática de componentes Filament
- Soporte para tipos complejos del schema JSON
- Validaciones con regex patterns
- Campos para min/max values y unidades

#### 12.5 Seeders con Datos Reales
**DimensionSeeder:** Actualizado con las 8 dimensiones reales del sistema
**ProductTaxonomySeeder:** Taxonomía completa de productos promocionales
**AttributeSystemSeeder:** ✅ **COMPLETADO** - Todas las 8 dimensiones con estructura completa del schema JSON

#### 12.6 Migraciones Actualizadas
**Tabla `attributes`:** Campos adicionales para soportar tipos complejos:
- `ui_component`, `ui_props`
- `validation_rules`, `options_json`
- `unit`, `min_value`, `max_value`, `pattern`
- Constraints CHECK para tipos válidos

#### 12.7 Correcciones Adicionales Aplicadas
**Trait HasSortOrder:** Actualizado para soportar parámetros de parent ID
**Migración SQL:** Sincronizada entre sección 2 y sección 7
**Atributo faltante:** Agregado `material_certifications` al seeder
**Validaciones:** Consistentes entre modelo, migración y enum

### 🎯 **Resultado Final**
La especificación ahora es **100% consistente** con los documentos de referencia y puede implementarse directamente sin discrepancias entre la documentación y el código generado.

### 📊 **Estadísticas Finales**
- **3,570+ líneas** de especificación técnica completa
- **8 dimensiones** completamente implementadas
- **25+ tipos de atributos** con validaciones específicas
- **200+ atributos** definidos según schema JSON
- **3 categorías principales** con taxonomía real
- **80+ tipos de productos** específicos del sistema PSS

### 📋 **Próximos Pasos Recomendados**
1. **Implementar las migraciones** en el orden especificado
2. **Ejecutar los seeders** para poblar datos iniciales
3. **Crear los modelos** con los traits y relaciones definidas
4. **Implementar los Filament resources** con formularios jerárquicos
5. **Ejecutar tests** para validar la implementación
