<div class="max-w-4xl mx-auto p-6">
    {{-- Men<PERSON>je de éxito --}}
    @if (session()->has('message'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
            <span class="block sm:inline">{{ session('message') }}</span>
        </div>
    @endif

    {{-- Indicador de progreso --}}
    <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
            @for ($i = 1; $i <= $totalSteps; $i++)
                <div class="flex items-center">
                    <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 {{ $currentStep >= $i ? 'bg-blue-600 border-blue-600 text-white' : 'border-gray-300 text-gray-500' }}">
                        {{ $i }}
                    </div>
                    @if ($i < $totalSteps)
                        <div class="w-20 h-0.5 {{ $currentStep > $i ? 'bg-blue-600' : 'bg-gray-300' }}"></div>
                    @endif
                </div>
            @endfor
        </div>
        
        <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                @switch($currentStep)
                    @case(1)
                        Información Personal
                        @break
                    @case(2)
                        Seguridad
                        @break
                    @case(3)
                        Roles y Permisos
                        @break
                @endswitch
            </h2>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
                Paso {{ $currentStep }} de {{ $totalSteps }}
            </p>
        </div>
    </div>

    {{-- Contenido del wizard --}}
    <div class="bg-white dark:bg-gray-800 shadow-xl rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
        @if ($currentStep == 1)
            {{-- Paso 1: Información Personal --}}
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Nombre completo
                        </label>
                        <input 
                            type="text" 
                            id="name"
                            wire:model="name" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white @error('name') border-red-500 @enderror"
                            placeholder="Ingresa el nombre completo"
                        >
                        @error('name') 
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p> 
                        @enderror
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Correo electrónico
                        </label>
                        <input 
                            type="email" 
                            id="email"
                            wire:model="email" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white @error('email') border-red-500 @enderror"
                            placeholder="<EMAIL>"
                        >
                        @error('email') 
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p> 
                        @enderror
                    </div>
                </div>
            </div>

        @elseif ($currentStep == 2)
            {{-- Paso 2: Seguridad --}}
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Contraseña
                        </label>
                        <input 
                            type="password" 
                            id="password"
                            wire:model="password" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white @error('password') border-red-500 @enderror"
                            placeholder="Mínimo 8 caracteres"
                        >
                        @error('password') 
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p> 
                        @enderror
                    </div>

                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Confirmar contraseña
                        </label>
                        <input 
                            type="password" 
                            id="password_confirmation"
                            wire:model="password_confirmation" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white @error('password_confirmation') border-red-500 @enderror"
                            placeholder="Repite la contraseña"
                        >
                        @error('password_confirmation') 
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p> 
                        @enderror
                    </div>
                </div>
            </div>

        @elseif ($currentStep == 3)
            {{-- Paso 3: Roles y Permisos --}}
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                            Roles del usuario
                        </label>
                        <div class="space-y-2">
                            @foreach($availableRoles as $role)
                                <label class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors">
                                    <input 
                                        type="checkbox" 
                                        wire:model="roles" 
                                        value="{{ $role->id }}"
                                        class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    >
                                    <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">{{ $role->name }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('roles') 
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p> 
                        @enderror
                    </div>

                    <div>
                        <label class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                            <input 
                                type="checkbox" 
                                wire:model="is_active"
                                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            >
                            <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Usuario activo</span>
                        </label>
                    </div>
                </div>
            </div>
        @endif

        {{-- Botones de navegación --}}
        <div class="flex justify-between pt-8 border-t border-gray-200 dark:border-gray-600">
            <button 
                type="button" 
                wire:click="previousStep"
                @if($currentStep == 1) disabled @endif
                class="px-6 py-3 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-gray-300"
            >
                Anterior
            </button>

            @if ($currentStep < $totalSteps)
                <button 
                    type="button" 
                    wire:click="nextStep"
                    class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-blue-300"
                >
                    Siguiente
                </button>
            @else
                <button 
                    type="button" 
                    wire:click="save"
                    class="px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-green-300"
                >
                    Crear Usuario
                </button>
            @endif
        </div>
    </div>
</div>
