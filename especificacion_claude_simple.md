# Especificación Técnica SIMPLE - Catálogo de Productos PSS
## Laravel 12 + Filament 4 - Enfoque Pragmático

### Versión: 1.0 - SIMPLE
### Fecha: 2025-01-24
### Volumen Real: 10 plantillas/año
### Principio: **"Solve the problem you have, not the one you think you might have"**

---

## 🎯 **Realidad del Negocio**

- **10 plantillas nuevas por año** (< 1 por mes)
- **< 5 modificaciones anuales** de plantillas existentes
- **< 10 usuarios concurrentes**
- **Complejidad media-baja** de productos promocionales

### **Anti-Sobreingeniería:**
- ❌ NO caching complejo
- ❌ NO compilation de schemas
- ❌ NO debugging tools especializados
- ❌ NO factory methods elaborados
- ❌ NO Artisan commands custom
- ❌ NO validadores compilados

---

## 1. **Arquitectura Minimalista**

### 1.1 Base de Datos - Solo lo Esencial

```sql
-- Usar las tablas existentes sin cambios
-- Solo agregar campos JSONB donde sea necesario

ALTER TABLE product_templates
ADD COLUMN attribute_schema JSONB DEFAULT '{}';
```

### 1.2 Schema JSON Ultra-Simple

```json
{
  "sections": [
    {
      "title": "Información Básica",
      "fields": {
        "product_name": {
          "type": "string",
          "label": "Nombre del Producto",
          "required": true
        },
        "description": {
          "type": "textarea",
          "label": "Descripción"
        }
      }
    },
    {
      "title": "Dimensiones",
      "fields": {
        "width_mm": {
          "type": "number",
          "label": "Ancho (mm)",
          "required": true
        },
        "height_mm": {
          "type": "number",
          "label": "Alto (mm)",
          "required": true
        },
        "depth_mm": {
          "type": "number",
          "label": "Profundidad (mm)"
        }
      }
    },
    {
      "title": "Especificaciones",
      "fields": {
        "material": {
          "type": "select",
          "label": "Material",
          "options": ["Algodón", "Poliéster", "Mezcla", "Plástico", "Metal"]
        },
        "colors": {
          "type": "array",
          "label": "Colores Disponibles"
        }
      }
    }
  ]
}
```

---

## 2. **Implementación Simple**

### 2.1 ProductTemplate (Minimalista)

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Filament\Forms\Components;

class ProductTemplate extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'product_type_id',
        'name',
        'slug',
        'status',
        'attribute_schema',
        'is_default'
    ];

    protected function casts(): array
    {
        return [
            'attribute_schema' => 'array',
            'is_default' => 'boolean'
        ];
    }

    // Relationships
    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class);
    }

    // SIMPLE: Generate form directly
    public function buildFilamentForm(): array
    {
        $components = [];

        foreach ($this->attribute_schema['sections'] ?? [] as $section) {
            $sectionComponents = [];

            foreach ($section['fields'] ?? [] as $fieldKey => $config) {
                $sectionComponents[] = $this->buildFormField($fieldKey, $config);
            }

            if (!empty($sectionComponents)) {
                $components[] = Components\Section::make($section['title'])
                    ->schema($sectionComponents)
                    ->columnSpanFull();
            }
        }

        return $components;
    }

    private function buildFormField(string $key, array $config): Components\Component
    {
        $component = match($config['type']) {
            'string' => Components\TextInput::make($key),
            'textarea' => Components\Textarea::make($key)->rows(3),
            'number' => Components\TextInput::make($key)->numeric(),
            'select' => Components\Select::make($key)
                ->options(array_combine($config['options'] ?? [], $config['options'] ?? [])),
            'array' => Components\TagsInput::make($key),
            default => Components\TextInput::make($key)
        };

        return $component
            ->label($config['label'] ?? $key)
            ->required($config['required'] ?? false);
    }

    // SIMPLE: Get validation rules
    public function getValidationRules(): array
    {
        $rules = [];

        foreach ($this->attribute_schema['sections'] ?? [] as $section) {
            foreach ($section['fields'] ?? [] as $fieldKey => $config) {
                $fieldRules = [];

                if ($config['required'] ?? false) {
                    $fieldRules[] = 'required';
                } else {
                    $fieldRules[] = 'nullable';
                }

                $fieldRules[] = match($config['type']) {
                    'string' => 'string',
                    'textarea' => 'string',
                    'number' => 'numeric',
                    'select' => 'string',
                    'array' => 'array',
                    default => 'string'
                };

                $rules[$fieldKey] = $fieldRules;
            }
        }

        return $rules;
    }
}
```

### 2.2 ProductVariant (Configuración Real)

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductVariant extends Model
{
    protected $fillable = [
        'product_project_id',
        'product_template_id',
        'variant_name',
        'configuration', // JSONB con los valores reales
        'status'
    ];

    protected function casts(): array
    {
        return [
            'configuration' => 'array'
        ];
    }

    public function productTemplate(): BelongsTo
    {
        return $this->belongsTo(ProductTemplate::class);
    }

    // SIMPLE: Validate against template
    public function validateConfiguration(): bool
    {
        $rules = $this->productTemplate->getValidationRules();
        $validator = validator($this->configuration, $rules);

        return !$validator->fails();
    }
}
```

---

## 3. **Filament Resources - Ultra Simple**

### 3.1 ProductTemplateResource

```php
<?php

namespace App\Filament\Resources;

use App\Models\ProductTemplate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ProductTemplateResource extends Resource
{
    protected static ?string $model = ProductTemplate::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-duplicate';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Template Basic Info')
                ->schema([
                    Forms\Components\Select::make('product_type_id')
                        ->relationship('productType', 'name')
                        ->required(),

                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->live(onBlur: true)
                        ->afterStateUpdated(fn ($state, callable $set) =>
                            $set('slug', \Str::slug($state))
                        ),

                    Forms\Components\TextInput::make('slug')
                        ->required()
                        ->unique(ignoreRecord: true),

                    Forms\Components\Toggle::make('is_default')
                        ->label('Default Template'),
                ])
                ->columns(2),

            Forms\Components\Section::make('Attribute Schema')
                ->schema([
                    Forms\Components\Textarea::make('attribute_schema')
                        ->label('Schema JSON (Simple)')
                        ->rows(20)
                        ->columnSpanFull()
                        ->formatStateUsing(fn ($state) =>
                            json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                        )
                        ->dehydrateStateUsing(fn ($state) =>
                            json_decode($state, true)
                        )
                        ->helperText('Define your form schema in simple JSON format'),

                    Forms\Components\Actions::make([
                        Forms\Components\Actions\Action::make('load_pss_template')
                            ->label('Load PSS Template')
                            ->action(function (callable $set) {
                                $set('attribute_schema', self::getPSSTemplate());
                            })
                    ])
                ])
                ->collapsible()
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable(),
                Tables\Columns\TextColumn::make('productType.name'),
                Tables\Columns\IconColumn::make('is_default')->boolean(),
                Tables\Columns\TextColumn::make('created_at')->dateTime(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('preview')
                    ->icon('heroicon-o-eye')
                    ->url(fn ($record) => route('templates.preview', $record))
                    ->openUrlInNewTab()
            ]);
    }

    private static function getPSSTemplate(): array
    {
        return [
            "sections" => [
                [
                    "title" => "Información Básica",
                    "fields" => [
                        "product_name" => [
                            "type" => "string",
                            "label" => "Nombre del Producto",
                            "required" => true
                        ],
                        "variant_description" => [
                            "type" => "textarea",
                            "label" => "Descripción de Variante"
                        ]
                    ]
                ],
                [
                    "title" => "Dimensiones y Medidas",
                    "fields" => [
                        "width_mm" => [
                            "type" => "number",
                            "label" => "Ancho (mm)",
                            "required" => true
                        ],
                        "height_mm" => [
                            "type" => "number",
                            "label" => "Alto (mm)",
                            "required" => true
                        ],
                        "depth_mm" => [
                            "type" => "number",
                            "label" => "Profundidad (mm)"
                        ],
                        "weight_g" => [
                            "type" => "number",
                            "label" => "Peso (g)"
                        ]
                    ]
                ],
                [
                    "title" => "Especificaciones del Producto",
                    "fields" => [
                        "primary_material" => [
                            "type" => "select",
                            "label" => "Material Principal",
                            "required" => true,
                            "options" => [
                                "Algodón", "Poliéster", "Mezcla", "Plástico ABS",
                                "Plástico PP", "Metal", "Cartón", "Papel"
                            ]
                        ],
                        "available_colors" => [
                            "type" => "array",
                            "label" => "Colores Disponibles"
                        ],
                        "pantone_colors" => [
                            "type" => "array",
                            "label" => "Códigos Pantone"
                        ]
                    ]
                ],
                [
                    "title" => "Área de Personalización",
                    "fields" => [
                        "printable_area_width" => [
                            "type" => "number",
                            "label" => "Ancho Área Imprimible (mm)"
                        ],
                        "printable_area_height" => [
                            "type" => "number",
                            "label" => "Alto Área Imprimible (mm)"
                        ],
                        "printing_methods" => [
                            "type" => "array",
                            "label" => "Métodos de Impresión Disponibles"
                        ]
                    ]
                ],
                [
                    "title" => "Información Comercial",
                    "fields" => [
                        "min_order_quantity" => [
                            "type" => "number",
                            "label" => "Cantidad Mínima de Pedido"
                        ],
                        "production_time_days" => [
                            "type" => "number",
                            "label" => "Tiempo de Producción (días)"
                        ],
                        "country_of_origin" => [
                            "type" => "string",
                            "label" => "País de Origen"
                        ]
                    ]
                ]
            ]
        ];
    }
}
```

### 3.2 ProductVariantResource

```php
<?php

namespace App\Filament\Resources;

use App\Models\ProductVariant;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class ProductVariantResource extends Resource
{
    protected static ?string $model = ProductVariant::class;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Variant Info')
                ->schema([
                    Forms\Components\Select::make('product_template_id')
                        ->relationship('productTemplate', 'name')
                        ->required()
                        ->live()
                        ->afterStateUpdated(fn (callable $set) => $set('configuration', [])),

                    Forms\Components\TextInput::make('variant_name')
                        ->required(),
                ])
                ->columns(2),

            // DYNAMIC: Generate form based on selected template
            Forms\Components\Section::make('Configuration')
                ->schema(fn (Forms\Get $get) =>
                    self::buildDynamicConfiguration($get('product_template_id'))
                )
                ->visible(fn (Forms\Get $get) => $get('product_template_id'))
        ]);
    }

    private static function buildDynamicConfiguration(?int $templateId): array
    {
        if (!$templateId) return [];

        $template = \App\Models\ProductTemplate::find($templateId);
        if (!$template) return [];

        return $template->buildFilamentForm();
    }
}
```

---

## 4. **Template Preview (Bonus Simple)**

### 4.1 Preview Route

```php
// routes/web.php
Route::get('/templates/{template}/preview', function (ProductTemplate $template) {
    return view('templates.preview', compact('template'));
})->name('templates.preview');
```

### 4.2 Preview View

```blade
{{-- resources/views/templates/preview.blade.php --}}
@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">Preview: {{ $template->name }}</h1>

    <div class="bg-white rounded-lg shadow-lg p-6">
        <form class="space-y-6">
            @foreach($template->attribute_schema['sections'] ?? [] as $section)
                <div class="border-l-4 border-blue-500 pl-4">
                    <h3 class="text-lg font-semibold mb-4">{{ $section['title'] }}</h3>

                    <div class="grid grid-cols-2 gap-4">
                        @foreach($section['fields'] ?? [] as $fieldKey => $config)
                            <div class="space-y-2">
                                <label class="block text-sm font-medium text-gray-700">
                                    {{ $config['label'] ?? $fieldKey }}
                                    @if($config['required'] ?? false)
                                        <span class="text-red-500">*</span>
                                    @endif
                                </label>

                                @switch($config['type'])
                                    @case('string')
                                        <input type="text"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                                               placeholder="Enter {{ $config['label'] ?? $fieldKey }}">
                                        @break
                                    @case('number')
                                        <input type="number"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                                               placeholder="Enter {{ $config['label'] ?? $fieldKey }}">
                                        @break
                                    @case('textarea')
                                        <textarea rows="3"
                                                  class="w-full border border-gray-300 rounded-md px-3 py-2"
                                                  placeholder="Enter {{ $config['label'] ?? $fieldKey }}"></textarea>
                                        @break
                                    @case('select')
                                        <select class="w-full border border-gray-300 rounded-md px-3 py-2">
                                            <option>Select {{ $config['label'] ?? $fieldKey }}</option>
                                            @foreach($config['options'] ?? [] as $option)
                                                <option>{{ $option }}</option>
                                            @endforeach
                                        </select>
                                        @break
                                    @default
                                        <input type="text"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                                               placeholder="Enter {{ $config['label'] ?? $fieldKey }}">
                                @endswitch
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </form>
    </div>
</div>
@endsection
```

---

## 5. **Seeder Simple**

```php
<?php

namespace Database\Seeders;

use App\Models\{ProductCategory, ProductSubcategory, ProductType, ProductTemplate};
use Illuminate\Database\Seeder;

class SimpleProductTemplateSeeder extends Seeder
{
    public function run(): void
    {
        // Create basic PSS templates
        $merchandising = ProductType::where('name', 'like', '%merchandising%')->first();

        if ($merchandising) {
            ProductTemplate::create([
                'product_type_id' => $merchandising->id,
                'name' => 'Plantilla Básica PSS',
                'slug' => 'plantilla-basica-pss',
                'is_default' => true,
                'attribute_schema' => [
                    "sections" => [
                        [
                            "title" => "Información Básica",
                            "fields" => [
                                "product_name" => [
                                    "type" => "string",
                                    "label" => "Nombre del Producto",
                                    "required" => true
                                ]
                            ]
                        ],
                        [
                            "title" => "Dimensiones",
                            "fields" => [
                                "width_mm" => [
                                    "type" => "number",
                                    "label" => "Ancho (mm)",
                                    "required" => true
                                ],
                                "height_mm" => [
                                    "type" => "number",
                                    "label" => "Alto (mm)",
                                    "required" => true
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
        }
    }
}
```

---

## 6. **Testing Mínimo (Solo lo Esencial)**

```php
<?php

use App\Models\ProductTemplate;
use function Pest\Laravel\{assertDatabaseHas};

test('can create product template with schema', function () {
    $template = ProductTemplate::create([
        'product_type_id' => 1,
        'name' => 'Test Template',
        'slug' => 'test-template',
        'attribute_schema' => [
            'sections' => [
                [
                    'title' => 'Basic',
                    'fields' => [
                        'name' => ['type' => 'string', 'required' => true]
                    ]
                ]
            ]
        ]
    ]);

    expect($template->attribute_schema)->toBeArray();
    expect($template->buildFilamentForm())->toHaveCount(1);
});

test('validates product configuration against template', function () {
    $template = ProductTemplate::factory()->create([
        'attribute_schema' => [
            'sections' => [
                [
                    'title' => 'Basic',
                    'fields' => [
                        'name' => ['type' => 'string', 'required' => true]
                    ]
                ]
            ]
        ]
    ]);

    $rules = $template->getValidationRules();
    expect($rules)->toHaveKey('name');
    expect($rules['name'])->toContain('required');
});
```

---

## **🏆 Resumen: ULTRA PRAGMÁTICO**

### **Lo que SÍ tenemos:**
- ✅ **JSONB simple** para esquemas flexibles
- ✅ **Filament forms dinámicos** generados automáticamente
- ✅ **Validación básica** contra el template
- ✅ **Preview funcional** de templates
- ✅ **Admin simple** para gestionar todo

### **Lo que NO tenemos (y está bien):**
- ❌ Caching complejo (innecesario para 10 templates/año)
- ❌ Compilation de schemas (overkill)
- ❌ Factory methods elaborados (YAGNI)
- ❌ Debugging tools (usar Telescope si necesitas)
- ❌ Artisan commands custom (usar tinker)

### **Implementación:**
1. **Agregar campo JSONB** a product_templates
2. **Implementar ProductTemplate model** (50 líneas)
3. **Crear Filament Resources** (100 líneas cada uno)
4. **Seedear template básico**
5. **¡LISTO!**

**Total: ~300 líneas de código vs 2000+ de la versión compleja**

**Esta es la especificación que necesitas para tu volumen real. Simple, mantenible, y que resuelve el problema que tienes, no el que podrías tener.**