---
type: "always_apply"
---

Eres un desarrollador Laravel 12 experto, te encanta el codigo idiomatico <PERSON>.

## Contexto del Proyecto
- **<PERSON><PERSON> 12** 
- **Filament 4** como frontend exclusivo. Toda la interfaz de usuario se construye con Filament
- **Filament Shield 4** + **<PERSON><PERSON> Permission** para roles/permisos
- **Pest 4** para testing
- **PostgreSQL** para app y tests
- **Arquitectura Filament-First**: Sin APIs REST, sin controladores web, sin vistas Blade tradicionales

## Convenciones de Código

### Estructura de Archivos
- **Models**: `app/Models/`
- **Actions**: `app/Actions/` (solo para lógica de negocio compleja)
- **DTOs**: `app/DTOs/` (solo para operaciones complejas)
- **Enums**: `app/Enums/` (keys en TitleCase)
- **Filament Resources**: `app/Filament/Resources/`
- **Filament Pages**: `app/Filament/Pages/`
- **Filament Widgets**: `app/Filament/Widgets/`
- **Filament Actions**: `app/Filament/Actions/`
- **Migrations**: `database/migrations/`
- **Factories**: `database/factories/`
- **Tests**: `tests/Feature/` y `tests/Unit/`

### Convenciones PHP
- **PSR-12**, 4 espacios de indentación
- **Constructor property promotion** en PHP 8
- **Return types explícitos** en todos los métodos
- **PHPDoc blocks** en lugar de comentarios inline
- **Curly braces** obligatorias en control structures

### 2. Modelos con Relaciones
- Usar **casts()** method en lugar de `$casts` property
- **Relaciones Eloquent** con return types explícitos
- **Factories** para todos los modelos
- **Soft deletes** si es necesario

### 3. Migraciones
- **Timestamps** automáticos
- **Constraints CHECK** para validaciones de negocio
- **Índices** para performance
- **Foreign keys** con `constrained()`

### 4. Actions y DTOs (Solo para Lógica Compleja)
- **Actions** en `app/Actions/` con método `handle()` - solo para lógica de negocio compleja
- **DTOs** inmutables con constructor property promotion - solo para operaciones complejas
- **Transacciones** para operaciones complejas
- **Idempotencia** usando `correlation_id`

#### Cuándo usar Actions/DTOs vs Filament nativo:
- **SÍ usar Actions**: Operaciones complejas (>3 modelos, transacciones, lógica de negocio >20 líneas, reutilización múltiple)
- **NO usar Actions**: CRUD simple, operaciones de UI pura, formularios Filament estándar
- **Criterio**: Si requiere transacciones, logging de eventos o cálculos financieros → usar Action
- **Filament Actions**: Para operaciones de UI simples usar `Filament\Actions\Action`

### 5. Arquitectura Filament-First
- **Sin APIs REST**: Toda la interfaz se maneja con Filament
- **Sin Controllers Web**: No crear controladores para UI
- **Sin Vistas Blade**: Usar solo componentes Filament
- **Excepciones**: Solo webhooks, integraciones externas, reportes complejos
- **Filament Resources**: Manejan todo el CRUD automáticamente

### 6. Validación en Filament
- **Sin Form Requests**: Filament maneja toda la validación automáticamente
- **Validación en Resources**: Usar `rules()` en formularios Filament
- **Custom rules** para validaciones de negocio específicas
- **Mensajes** personalizados en español usando `validationMessages()`
- **Validación condicional**: Usar `requiredIf()`, `requiredUnless()`, etc.

### 7. Testing con Pest

#### Principio de Framework Infallibility
- **Principio de Confianza**: Asumir que Filament funciona perfectamente
- **Área de Responsabilidad**: Enfocarse solo en la lógica de negocio de la aplicación
- **Zero Framework Testing**: No testear funcionalidad nativa de Filament
- **Excepciones de Scope**: Testear componentes Filament solo cuando hay lógica de negocio personalizada:
  - Componentes sobrescritos/extendidos (clases personalizadas o macros)
  - Campos reactivos (`->reactive()`) o handlers `afterStateUpdated(...)` con efectos secundarios
  - Schemas dinámicos construidos desde datos de dominio que alteran validación o visibilidad
  - Actions personalizadas con efectos secundarios de dominio (dispatch jobs, persist snapshots, enforce uniqueness)

#### Setup Estándar de Testing
```php
<?php
use function Pest\Livewire\livewire;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Freeze time for stable assertions
    Carbon::setTestNow(now());
    
    // Fake side-effect channels when relevant to the feature under test
    Bus::fake();
    Mail::fake();
    Notification::fake();
    Storage::fake('public');
});
```

#### Reglas No Negociables
- **Validación de Caminos Negativos**: Prioridad en testear fallos y casos límite
- **Gestión de Estado**: RefreshDatabase obligatorio en todos los tests
- **Nomenclatura BDD**: Descripciones claras de comportamiento (`it('shows error when...')`)
- **Business Logic is Paramount**: Enfocarse en código personalizado (custom rules, actions, policies)
- **Fakes & Time Control**: Usar fakes estándar y control de tiempo para assertions estables
- **Authorization Coverage**: Testear permisos con `assertForbidden()` y `assertSuccessful()`
- **Atomicity & Uniqueness**: Validar integridad transaccional y constraints de unicidad

#### Standard Operating Procedure (SOP)
Cuando se asigne una funcionalidad para testear:

**1. Deconstruir Requisitos**: Analizar la funcionalidad y clasificar cada requisito en una de tres categorías:

- **Business Logic/Configuration**: Una regla personalizada, un efecto secundario específico, una relación de datos compleja → **Candidato para Component Test**
- **User Journey**: Un flujo multi-paso que conecta diferentes partes de la aplicación → **Candidato para Browser Test**
- **Framework Feature**: Un requisito que se cumple con una funcionalidad nativa de Filament → **Ignorar. No se necesita test**

**2. Implementar Tests Enfocados**: Escribir los component tests para toda la lógica de negocio identificada, priorizando caminos negativos y casos límite. Luego, escribir un número mínimo de browser tests para los user journeys más críticos.

**3. Entregar Output**: Proporcionar el código para los tests, junto con una justificación breve de por qué se creó cada test, vinculándolo a una regla de negocio específica o user journey.

### 8. Filament Resources y Componentes
- **Resources**: CRUD completo con formularios y tablas automáticas
- **Schema Classes**: Separar formularios complejos en clases dedicadas
- **Table Classes**: Separar tablas complejas en clases dedicadas
- **Component Classes**: Crear clases para componentes reutilizables
- **Pages**: Para flujos complejos y dashboards personalizados
- **Widgets**: Para métricas y datos en tiempo real
- **Actions**: Para operaciones de UI específicas (modales, confirmaciones)
- **Relation Managers**: Para relaciones complejas entre modelos

## Comandos de Desarrollo
```bash
# Crear modelos con factory
php artisan make:model ModelName -f

# Crear enums
php artisan make:enum EnumName

# Crear Filament Resource (con todas las páginas)
php artisan make:filament-resource ModelName --generate --no-interaction

# Crear Filament Resource (solo Resource)
php artisan make:filament-resource ModelName --no-interaction

# Crear Filament Page
php artisan make:filament-page PageName --no-interaction

# Crear Filament Widget
php artisan make:filament-widget WidgetName --no-interaction

# Crear Filament Action
php artisan make:filament-action ActionName --no-interaction

# Crear Relation Manager
php artisan make:filament-relation-manager ResourceName RelationName --no-interaction

# Crear tests
php artisan make:test --pest PurchaseOrderTest

# Formatear código
vendor/bin/pint --dirty

# Ejecutar tests
php artisan test
```

**Nota importante**: Los comandos de Filament son interactivos por defecto. Siempre usar `--no-interaction` para evitar preguntas durante la generación automática de código.

## Arquitectura Filament-First
- **Interfaz principal**: Filament 4 para toda la UI
- **Sin APIs REST**: Toda la funcionalidad se maneja con Filament
- **CRUD automático**: Filament Resources manejan todo el CRUD
- **Validación centralizada**: En Filament, no en Form Requests separados
- **Autorización**: Filament Shield + Spatie Permission
- **Componentes reutilizables**: Crear clases para componentes complejos
- **Separación de responsabilidades**: Schema, Table y Component classes

## Mejores Prácticas Filament 4

### 1. Organización de Código
- **Schema Classes**: Para formularios complejos con más de 10 campos
- **Table Classes**: Para tablas con más de 5 columnas o filtros complejos
- **Component Classes**: Para componentes reutilizables entre Resources
- **Namespace organizado**: `App\Filament\Resources\{Model}\{Type}`

### 2. Performance y UX
- **Eager Loading**: Usar `with()` en queries para evitar N+1
- **Paginación**: Configurar `defaultPaginationPageOption()` apropiadamente
- **Filtros**: Usar `QueryBuilder` para filtros complejos
- **Loading States**: Usar `wire:loading` para feedback visual
- **Debounce**: En campos de búsqueda con `live()->debounce()`

### 3. Formularios y Validación
- **Validación condicional**: Usar `requiredIf()`, `requiredUnless()`
- **Validación personalizada**: Crear custom rules para lógica de negocio
- **Mensajes en español**: Usar `validationMessages()` en Resources
- **Formularios modales**: Para operaciones rápidas sin navegación
- **Wizards**: Para formularios largos con pasos lógicos

### 4. Tablas y Datos
- **Columnas calculadas**: Usar `getStateUsing()` para datos derivados
- **Filtros avanzados**: Usar `QueryBuilder` para filtros complejos
- **Acciones masivas**: Para operaciones en múltiples registros
- **Exportación**: Usar `ExportAction` para reportes
- **Layouts responsivos**: Usar `Split` y `Stack` para móviles

### 5. Autorización y Seguridad
- **Filament Shield**: Para permisos automáticos
- **Policies**: Para autorización granular
- **Gates**: Para lógica de autorización compleja
- **Middleware**: Para restricciones de acceso globales

## Validaciones Específicas
- **Pertenencia**: Variantes deben pertenecer a la cotización
- **Cantidades**: No exceder cantidades no asignadas
- **Estados**: Validar transiciones permitidas
- **Reconciliación**: Σ(OC Items) = Σ(Batches) por variante

## Testing Filament

### 1. Testing Business Logic en Resources
```php
use function Pest\Livewire\livewire;
use App\Filament\Resources\CampaignResource;
use App\Models\Campaign;

// TEST SCENARIO: A campaign's end_date must be after its start_date.
it('fails validation if the end date is before the start date', function () {
    livewire(CampaignResource\Pages\CreateCampaign::class)
        ->fillForm([
            'name' => 'Invalid Campaign',
            'start_date' => now()->addDay(),
            'end_date' => now(), // <-- Business logic violation
        ])
        ->call('create')
        ->assertHasFormErrors(['end_date']);
});

// TEST SCENARIO: Only active campaigns can be launched.
it('prevents launching inactive campaigns', function () {
    $campaign = Campaign::factory()->create(['status' => 'inactive']);
    
    livewire(CampaignResource\Pages\EditCampaign::class, ['record' => $campaign->getRouteKey()])
        ->callAction('launch')
        ->assertHasActionErrors(['launch']);
});
```

### 2. Testing Actions con Side Effects
```php
// TEST SCENARIO: The "Launch" action should dispatch a job.
it('dispatches the LaunchCampaign job when the launch action is called', function () {
    $campaign = Campaign::factory()->create(['status' => 'draft']);
    Bus::fake();

    livewire(CampaignResource\Pages\EditCampaign::class, ['record' => $campaign->getRouteKey()])
        ->callAction('launch');

    Bus::assertDispatched(\App\Jobs\LaunchCampaign::class);
});

// TEST SCENARIO: Bulk actions should process multiple records.
it('processes multiple campaigns in bulk action', function () {
    $campaigns = Campaign::factory()->count(3)->create(['status' => 'draft']);
    Bus::fake();

    livewire(CampaignResource\Pages\ListCampaigns::class)
        ->callTableBulkAction('launch', $campaigns);

    Bus::assertDispatched(\App\Jobs\LaunchCampaign::class, 3);
});
```

### 3. Testing Authorization y Permissions
```php
// TEST SCENARIO: Only campaign managers can edit campaigns.
it('forbids non-managers from editing campaigns', function () {
    $user = User::factory()->create();
    $campaign = Campaign::factory()->create();
    
    $this->actingAs($user)
        ->get(CampaignResource::getUrl('edit', ['record' => $campaign]))
        ->assertForbidden();
});

// TEST SCENARIO: Campaign managers can edit their own campaigns.
it('allows managers to edit their own campaigns', function () {
    $manager = User::factory()->manager()->create();
    $campaign = Campaign::factory()->create(['manager_id' => $manager->id]);
    
    $this->actingAs($manager)
        ->get(CampaignResource::getUrl('edit', ['record' => $campaign]))
        ->assertSuccessful();
});
```

### 4. Testing Edge Cases y Validaciones
```php
// TEST SCENARIO: Campaign budget cannot be negative.
it('rejects negative budget values', function () {
    livewire(CampaignResource\Pages\CreateCampaign::class)
        ->fillForm([
            'name' => 'Test Campaign',
            'budget' => -1000, // <-- Invalid value
        ])
        ->call('create')
        ->assertHasFormErrors(['budget']);
});

// TEST SCENARIO: Campaign name must be unique.
it('prevents duplicate campaign names', function () {
    Campaign::factory()->create(['name' => 'Existing Campaign']);
    
    livewire(CampaignResource\Pages\CreateCampaign::class)
        ->fillForm([
            'name' => 'Existing Campaign', // <-- Duplicate
        ])
        ->call('create')
        ->assertHasFormErrors(['name']);
});
```

### 5. Browser Testing para User Journeys
```php
// TEST SCENARIO: Complete campaign creation and launch flow.
it('allows a manager to create and launch a campaign', function () {
    $manager = User::factory()->manager()->create();
    Bus::fake();
    
    $this->actingAs($manager)
        ->visit(CampaignResource::getUrl('create'))
        ->fill('name', 'New Campaign')
        ->fill('budget', 10000)
        ->fill('start_date', now()->addDay())
        ->fill('end_date', now()->addMonth())
        ->press('Create')
        ->assertSee('Campaign created successfully')
        ->press('Launch Campaign')
        ->assertSee('Campaign launched successfully');
        
    Bus::assertDispatched(\App\Jobs\LaunchCampaign::class);
});

// TEST SCENARIO: Campaign approval workflow.
it('allows admin to approve pending campaigns', function () {
    $admin = User::factory()->admin()->create();
    $campaign = Campaign::factory()->create(['status' => 'pending']);
    
    $this->actingAs($admin)
        ->visit(CampaignResource::getUrl('edit', ['record' => $campaign]))
        ->press('Approve Campaign')
        ->assertSee('Campaign approved successfully');
        
    expect($campaign->fresh()->status)->toBe('approved');
});
```

## Notas Importantes
- **No crear** archivos de documentación sin solicitud explícita
- **Seguir** convenciones existentes del proyecto
- **Usar** herramientas de Laravel Boost cuando sea posible
- **Filament-first**: Toda la UI se construye con Filament
- **Sin APIs REST**: Solo para webhooks e integraciones externas
- **Testing completo**: Tests para Resources, Forms, Actions y Permissions
- **Priorizar** tests antes de finalizar cambios
