<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class ShieldPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Genera permisos y policies según los recursos/páginas/widgets actuales
        $this->command?->info('Iniciando generación de permisos con Shield (shield:generate --all)...');

        try {
            $exitCode = Artisan::call('shield:generate', [
                '--all' => true,
                '--panel' => 'admin',
                '--option' => 'policies_and_permissions',
                '--no-interaction' => true,
            ]);

            $this->command?->info('Comando shield:generate finalizado con código: ' . $exitCode);

            $output = Artisan::output();
            if (! empty($output)) {
                $this->command?->line($output);
            }
        } catch (\Throwable $e) {
            $this->command?->error('Error ejecutando shield:generate: ' . $e->getMessage());
            throw $e;
        }
    }
}
