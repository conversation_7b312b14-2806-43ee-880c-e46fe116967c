<?php

namespace Database\Seeders;

use App\Models\Attribute;
use App\Models\AttributeGroup;
use App\Models\Dimension;
use App\Models\ProductTemplate;
use App\Models\ProductTemplateAttribute;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ProductAttributesSchemaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $schema = $this->loadJsonSchema();
        if ($schema === null) {
            $this->command?->warn('No se pudo cargar el JSON Schema desde _docus/_clean/detalle_especificacion.schema.json.');

            return;
        }

        $this->seedAttributesFromSchema($schema);

        // Asociar por mapeos por tipo/subcategoría
        $this->attachAttributesToTemplates();
    }

    /**
     * Lee y decodifica el archivo JSON Schema.
     *
     * @return array<string, mixed>|null
     */
    private function loadJsonSchema(): ?array
    {
        $jsonSchemaPath = base_path('_docus/_clean/detalle_especificacion.schema.json');
        if (! File::exists($jsonSchemaPath)) {
            return null;
        }

        $raw = File::get($jsonSchemaPath);
        // Remover BOM si aplica
        $raw = preg_replace('/^\xEF\xBB\xBF/', '', (string) $raw) ?? $raw;
        try {
            $schema = json_decode($raw, true, 512, JSON_THROW_ON_ERROR);

            return is_array($schema) ? $schema : null;
        } catch (\JsonException $e) {
            $this->command?->warn('Error decodificando detalle_especificacion.schema.json: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Recorre el schema y crea los grupos y atributos.
     *
     * @param array<string, mixed> $schema
     */
    private function seedAttributesFromSchema(array $schema): void
    {
        $defs = $schema['$defs'] ?? [];
        $rootProps = $schema['properties'] ?? [];

        // Cache all dimensions by their key for efficient lookup
        $dimensionsCache = Dimension::all()->keyBy('key');

        foreach ($rootProps as $sectionKey => $sectionSchema) {
            $dimension = $dimensionsCache->get($sectionKey);

            if (! $dimension) {
                $this->command?->warn("Skipping section. No dimension found with key: {$sectionKey}");
                continue;
            }

            if (! isset($sectionSchema['properties']) || ! is_array($sectionSchema['properties'])) {
                continue;
            }

            foreach ($sectionSchema['properties'] as $groupKey => $groupSchema) {
                // Validar que el key sea único
                $finalGroupKey = $this->ensureUniqueKey($groupKey, 'attribute_groups', 'key');

                $group = AttributeGroup::query()->updateOrCreate(
                    ['key' => $finalGroupKey],
                    [
                        'dimension_id' => $dimension->id,
                        'name' => Str::of($groupKey)->replace('_', ' ')->title(),
                        'description' => $groupSchema['description'] ?? null,
                        'sort_order' => $dimension->sort_order, // Inherit sort order from dimension
                        'is_required' => false,
                        'status' => 'active',
                    ]
                );

                if (! isset($groupSchema['properties']) || ! is_array($groupSchema['properties'])) {
                    continue;
                }

                foreach ($groupSchema['properties'] as $attrKey => $attrSchema) {
                    [$type, $unit, $rules, $description, $defaultPlaceholder] = $this->mapSchemaNodeToAttributeProperties($attrSchema, $defs);

                    // Validar que el key del atributo sea único
                    $finalAttrKey = $this->ensureUniqueKey($attrKey, 'attributes', 'key');

                    Attribute::query()->updateOrCreate(
                        ['key' => $finalAttrKey], // La clave del atributo es única globalmente
                        [
                            'attribute_group_id' => $group->id,
                            'name' => Str::of($attrKey)->replace('_', ' ')->title(),
                            'description' => $description,
                            'type' => $type,
                            'unit' => $unit,
                            'validation_rules' => $rules,
                            'options' => null,
                            'default_value' => null,
                            'default_placeholder' => $defaultPlaceholder,
                            'is_required' => in_array($attrKey, $groupSchema['required'] ?? [], true),
                            'sort_order' => 0,
                            'status' => 'active',
                        ]
                    );
                }
            }
        }
    }

    /**
     * Determina tipo, unidad y reglas Laravel a partir de un nodo del JSON Schema.
     *
     * @param array<string, mixed> $node
     * @param array<string, mixed> $defs
     * @return array{0:string,1:?string,2:array<string,mixed>|array<int,string>|null,3:?string,4:?string}
     */
    private function mapSchemaNodeToAttributeProperties(array $node, array $defs): array
    {
        $desc = $node['description'] ?? null;
        $defaultPlaceholder = $node['defaultPlaceholder'] ?? null;

        // $ref
        if (isset($node['$ref']) && is_string($node['$ref'])) {
            $refName = trim((string) Str::afterLast($node['$ref'], '/'));

            return $this->mapDefRef($refName, $defs, $desc, $defaultPlaceholder);
        }

        // type directo
        $type = $node['type'] ?? null;
        if (is_array($type)) {
            // Ej: ["string","null"]
            $type = array_values(array_filter($type, fn ($t) => $t !== 'null'))[0] ?? 'string';
        }

        switch ($type) {
            case 'boolean':
                return ['boolean', null, null, $desc, $defaultPlaceholder];
            case 'number':
                return ['number', null, ['numeric', 'min:0'], $desc, $defaultPlaceholder];
            case 'string':
                return ['string', null, null, $desc, $defaultPlaceholder];
            case 'array':
                return ['array', null, ['array'], $desc, $defaultPlaceholder];
            case 'object':
                return ['json', null, $this->mapObjectRules($node, $defs), $desc, $defaultPlaceholder];
            default:
                return ['string', null, null, $desc, $defaultPlaceholder];
        }
    }

    /**
     * @param array<string, mixed> $defs
     * @return array{0:string,1:?string,2:array<string,mixed>|array<int,string>|null,3:?string,4:?string}
     */
    private function mapDefRef(string $refName, array $defs, ?string $desc, ?string $defaultPlaceholder): array
    {
        // Unidades y atajos
        if (in_array($refName, ['mm', 'ml', 'kg'], true)) {
            $unit = $refName;

            return ['number', $unit, ['numeric', 'min:0'], $desc, $defaultPlaceholder];
        }

        if ($refName === 'percentage') {
            return ['number', '%', ['numeric', 'min:0', 'max:100'], $desc, $defaultPlaceholder];
        }

        if ($refName === 'dimensions2Dmm') {
            return ['json', 'mm', [
                'type' => 'object',
                'required' => ['width_mm', 'height_mm'],
                'properties' => [
                    'width_mm' => ['numeric', 'min:0'],
                    'height_mm' => ['numeric', 'min:0'],
                ],
            ], $desc, $defaultPlaceholder];
        }

        if ($refName === 'dimensions3Dmm') {
            return ['json', 'mm', [
                'type' => 'object',
                'required' => ['width_mm', 'height_mm', 'depth_mm'],
                'properties' => [
                    'width_mm' => ['numeric', 'min:0'],
                    'height_mm' => ['numeric', 'min:0'],
                    'depth_mm' => ['numeric', 'min:0'],
                ],
            ], $desc, $defaultPlaceholder];
        }

        if ($refName === 'tolerancesDims') {
            return ['json', 'mm', [
                'type' => 'object',
                'properties' => [
                    'width_mm' => ['numeric', 'min:0'],
                    'height_mm' => ['numeric', 'min:0'],
                    'depth_mm' => ['numeric', 'min:0'],
                    'diameter_mm' => ['numeric', 'min:0'],
                    'percent' => ['numeric', 'min:0', 'max:100'],
                ],
            ], $desc, $defaultPlaceholder];
        }

        if ($refName === 'specificPartDimensions') {
            return ['json', 'mm', [
                'type' => 'array',
                'items' => [
                    'type' => 'object',
                    'required' => ['part'],
                    'properties' => [
                        'part' => ['string', 'min:1'],
                        'width_mm' => ['numeric', 'min:0'],
                        'height_mm' => ['numeric', 'min:0'],
                        'depth_mm' => ['numeric', 'min:0'],
                        'diameter_mm' => ['numeric', 'min:0'],
                    ],
                ],
            ], $desc, $defaultPlaceholder];
        }

        // Otros $defs (colorPantone, fileFormat, nonEmptyString, etc.) -> string/array
        $defNode = $defs[$refName] ?? null;
        if ($defNode) {
            if (($defNode['type'] ?? null) === 'string' && isset($defNode['enum'])) {
                return ['string', null, ['in:'.implode(',', $defNode['enum'])], $desc, $defaultPlaceholder];
            }
            if (($defNode['type'] ?? null) === 'string') {
                return ['string', null, null, $desc, $defaultPlaceholder];
            }
        }

        // fallback si no mapeamos explícitamente
        return ['string', null, null, $desc, $defaultPlaceholder];
    }

    /**
     * Convierte un objeto JSON Schema a reglas Laravel anidadas sencillas.
     *
     * @param array<string, mixed> $node
     * @param array<string, mixed> $defs
     * @return array<string, mixed>
     */
    private function mapObjectRules(array $node, array $defs): array
    {
        $rules = [
            'type' => 'object',
        ];

        if (! empty($node['required']) && is_array($node['required'])) {
            $rules['required'] = array_values(array_map('strval', $node['required']));
        }

        if (! empty($node['properties']) && is_array($node['properties'])) {
            $props = [];
            foreach ($node['properties'] as $propKey => $propSchema) {
                if (isset($propSchema['$ref'])) {
                    [, , $subRules] = $this->mapSchemaNodeToAttributeProperties($propSchema, $defs);
                    if (is_array($subRules)) {
                        $props[$propKey] = $subRules;
                    }
                } elseif (($propSchema['type'] ?? null) === 'number') {
                    $props[$propKey] = ['numeric', 'min:0'];
                }
            }

            if (! empty($props)) {
                $rules['properties'] = $props;
            }
        }

        return $rules;
    }

    private function attachAttributesToTemplates(): void
    {
        $templates = ProductTemplate::query()->with(['attributeGroups.attributes', 'productType', 'productSubcategory'])->get();
        if ($templates->isEmpty()) {
            return;
        }

        $typeMap = $this->readJson(base_path('_docus/_clean/atributos_por_tipo.json'));
        $subMap = $this->readJson(base_path('_docus/_clean/atributos_por_subcategoria.json'));

        foreach ($templates as $template) {
            $position = 0;

            $desiredGroupKeys = $this->resolveDesiredGroupKeys($template, $typeMap, $subMap);
            if (empty($desiredGroupKeys)) {
                continue;
            }

            // Obtener solo los grupos deseados
            $groups = AttributeGroup::query()
                ->with('attributes')
                ->whereIn('key', $desiredGroupKeys)
                ->orderBy('sort_order')
                ->get();

            foreach ($groups as $group) {
                foreach ($group->attributes as $attribute) {
                    $position++;
                    $ui = $this->inferUiForAttribute($attribute->key, $attribute->type, $attribute->unit);

                    ProductTemplateAttribute::query()->updateOrCreate(
                        [
                            'product_template_id' => $template->id,
                            'attribute_id' => $attribute->id,
                        ],
                        [
                            'attribute_group_id' => $group->id,
                            'is_required' => false,
                            'visible' => true,
                            'position' => $position,
                            'ui_component' => $ui['component'],
                            'ui_props' => $ui['props'],
                            'validation_rules' => $attribute->validation_rules,
                            'placeholder' => null,
                            'column_span' => 1,
                        ]
                    );
                }
            }
        }
    }

    /**
     * @return array{component:string,props:array<string,mixed>}
     */
    private function inferUiForAttribute(string $key, string $type, ?string $unit): array
    {
        // Mapear por claves conocidas
        if (in_array($key, ['overall_external_dimensions', 'internal_dimensions', 'folded_collapsed_dimensions'], true)) {
            return ['component' => 'dimensions3d', 'props' => []];
        }
        if ($key === 'specific_part_dimensions') {
            return ['component' => 'repeater_parts', 'props' => []];
        }
        if ($key === 'dimensional_tolerances') {
            return ['component' => 'json', 'props' => ['rows' => 3]];
        }

        // Por tipo/unidad
        if ($type === 'number') {
            if ($unit === 'mm') {
                return ['component' => 'number', 'props' => ['step' => 0.01, 'suffix' => ' mm']];
            }
            if ($unit === 'ml') {
                return ['component' => 'number', 'props' => ['step' => 1, 'suffix' => ' ml']];
            }
            if ($unit === 'kg') {
                return ['component' => 'number', 'props' => ['step' => 0.01, 'suffix' => ' kg']];
            }
            if ($unit === '%') {
                return ['component' => 'number', 'props' => ['step' => 0.01, 'suffix' => ' %', 'minValue' => 0, 'maxValue' => 100]];
            }

            return ['component' => 'number', 'props' => ['step' => 0.01]];
        }

        if ($type === 'array') {
            return ['component' => 'textarea', 'props' => ['rows' => 3]];
        }

        if ($type === 'json') {
            return ['component' => 'textarea', 'props' => ['rows' => 3]];
        }

        return ['component' => 'text', 'props' => []];
    }

    /**
     * Lee un JSON desde ruta, retorna array (o array vacío si no existe/parsea).
     *
     * @return array<string, mixed>
     */
    private function readJson(string $path): array
    {
        if (! File::exists($path)) {
            return [];
        }
        $raw = File::get($path);
        $data = json_decode($raw, true);

        return is_array($data) ? $data : [];
    }

    /**
     * Resuelve los group keys deseados usando prioridad: tipo -> subcategoría -> default.
     *
     * @param array<string, mixed> $typeMap
     * @param array<string, mixed> $subMap
     * @return list<string>
     */
    private function resolveDesiredGroupKeys(ProductTemplate $template, array $typeMap, array $subMap): array
    {
        $keys = [];

        $typeName = Str::of((string) ($template->productType->name ?? ''))
            ->ascii()
            ->trim()
            ->value();
        $typeKeyed = $typeMap['by_type'] ?? [];
        if ($typeName !== '' && isset($typeKeyed[$typeName]) && is_array($typeKeyed[$typeName])) {
            $keys = array_merge($keys, $typeKeyed[$typeName]);
        }

        if (empty($keys)) {
            $subName = Str::of((string) ($template->productSubcategory->name ?? ''))
                ->ascii()
                ->trim()
                ->value();
            $subKeyed = $subMap['by_subcategory'] ?? [];
            if ($subName !== '' && isset($subKeyed[$subName]) && is_array($subKeyed[$subName])) {
                $keys = array_merge($keys, $subKeyed[$subName]);
            }
        }

        if (empty($keys)) {
            $defType = $typeMap['default'] ?? [];
            $defSub = $subMap['default'] ?? [];
            if (is_array($defType)) {
                $keys = array_merge($keys, $defType);
            }
            if (is_array($defSub)) {
                $keys = array_merge($keys, $defSub);
            }
        }

        // Normalizar y de-duplicar
        $keys = array_values(array_unique(array_map(function ($k) {
            return (string) Str::of((string) $k)->ascii()->lower()->squish()->snake();
        }, $keys)));

        return $keys;
    }

    /**
     * Ensure a key is unique by appending a counter if necessary.
     *
     * @param string $key The original key
     * @param string $table The table to check for uniqueness
     * @param string $column The column to check for uniqueness
     * @return string The unique key
     */
    private function ensureUniqueKey(string $key, string $table, string $column): string
    {
        $finalKey = $key;
        $counter = 1;

        while (\DB::table($table)->where($column, $finalKey)->exists()) {
            $finalKey = $key . '_' . $counter;
            $counter++;
        }

        if ($finalKey !== $key) {
            $this->command?->warn("Key '{$key}' already exists in {$table}.{$column}, using '{$finalKey}' instead.");
        }

        return $finalKey;
    }
}
