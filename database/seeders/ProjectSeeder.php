<?php

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\Project;
use App\Models\ProductProject;
use App\Models\ProductTemplate;
use App\Models\ProductVariant;
use App\Models\Supplier;
use App\Models\ProcurementLot;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedTechGlobalProject();
        $this->seedUrbanaProject();
        $this->seedInnovatechProject();
    }

    /**
     * Seed TechGlobal Inc. project - Conferencia Anual TechGlobal 2025
     */
    private function seedTechGlobalProject(): void
    {
        $customer = Customer::where('name', 'TechGlobal Inc.')->first();
        
        $project = Project::create([
            'customer_id' => $customer->id,
            'name' => 'Conferencia Anual TechGlobal 2025',
            'status' => 'active',
            'starts_at' => Carbon::create(2025, 3, 15),
            'ends_at' => Carbon::create(2025, 3, 18),
            'currency' => 'USD',
        ]);

        // Producto 1: Tazón Corporativo
        $tazonTemplate = ProductTemplate::where('name', 'Tazón Cerámica 11oz')->first();
        $tazonProject = ProductProject::create([
            'project_id' => $project->id,
            'product_template_id' => $tazonTemplate->id,
            'supplier_id' => Supplier::where('name', 'CeramiKup Sublimación')->first()->id,
            'total_quantity' => 500,
            'avg_unit_price' => 8.50,
            'currency' => 'USD',
            'status' => 'approved',
            'notes' => 'Tazones corporativos para conferencia con logo TechGlobal 2025',
        ]);

        // Variantes de Tazón
        ProductVariant::create([
            'product_project_id' => $tazonProject->id,
            'name' => 'Tazón Blanco con Logo Centrado',
            'quantity' => 300,
            'configuration' => [
                'color_base' => 'blanco',
                'color_print' => '1_color',
                'tecnica_marcaje' => 'sublimación',
                'area_impresion' => 'centro',
            ],
            'generated_sku' => 'TCZ-BLANCO-300',
            'estimated_unit_price' => 8.00,
            'suggested_supplier_id' => Supplier::where('name', 'CeramiKup Sublimación')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 15),
            'delivery_window_end' => Carbon::create(2025, 3, 1),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $tazonProject->id,
            'name' => 'Tazón Negro con Logo en Negativo',
            'quantity' => 150,
            'configuration' => [
                'color_base' => 'negro',
                'color_print' => 'negativo',
                'tecnica_marcaje' => 'sublimación',
                'area_impresion' => 'centro',
            ],
            'generated_sku' => 'TCZ-NEGRO-150',
            'estimated_unit_price' => 8.50,
            'suggested_supplier_id' => Supplier::where('name', 'CeramiKup Sublimación')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 15),
            'delivery_window_end' => Carbon::create(2025, 3, 1),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $tazonProject->id,
            'name' => 'Tazón Blanco Full-Color con Partners',
            'quantity' => 50,
            'configuration' => [
                'color_base' => 'blanco',
                'color_print' => 'full_color',
                'tecnica_marcaje' => 'sublimación',
                'area_impresion' => 'centro',
            ],
            'generated_sku' => 'TCZ-FULL-50',
            'estimated_unit_price' => 10.00,
            'suggested_supplier_id' => Supplier::where('name', 'CeramiKup Sublimación')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 15),
            'delivery_window_end' => Carbon::create(2025, 3, 1),
            'variant_status' => 'configured',
        ]);

        // Producto 2: Polera Oficial del Evento
        $poleraTemplate = ProductTemplate::where('name', 'Polera Algodón Cuello Redondo')->first();
        $poleraProject = ProductProject::create([
            'project_id' => $project->id,
            'product_template_id' => $poleraTemplate->id,
            'supplier_id' => Supplier::where('name', 'TextilCorp Bordados')->first()->id,
            'total_quantity' => 600,
            'avg_unit_price' => 12.00,
            'currency' => 'USD',
            'status' => 'approved',
            'notes' => 'Poleras oficiales del evento para staff y asistentes',
        ]);

        // Variantes de Polera
        ProductVariant::create([
            'product_project_id' => $poleraProject->id,
            'name' => 'Polera Azul Marino Talla M',
            'quantity' => 250,
            'configuration' => [
                'talla' => 'M',
                'color_base' => 'azul_marino',
                'tecnica_marcaje' => 'serigrafía',
                'area_impresion' => 'pecho_izquierdo',
            ],
            'generated_sku' => 'POL-AM-M-250',
            'estimated_unit_price' => 11.50,
            'suggested_supplier_id' => Supplier::where('name', 'TextilCorp Bordados')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 20),
            'delivery_window_end' => Carbon::create(2025, 3, 5),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $poleraProject->id,
            'name' => 'Polera Azul Marino Talla L',
            'quantity' => 250,
            'configuration' => [
                'talla' => 'L',
                'color_base' => 'azul_marino',
                'tecnica_marcaje' => 'serigrafía',
                'area_impresion' => 'pecho_izquierdo',
            ],
            'generated_sku' => 'POL-AM-L-250',
            'estimated_unit_price' => 11.50,
            'suggested_supplier_id' => Supplier::where('name', 'TextilCorp Bordados')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 20),
            'delivery_window_end' => Carbon::create(2025, 3, 5),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $poleraProject->id,
            'name' => 'Polera Gris Jaspeado Talla M STAFF',
            'quantity' => 100,
            'configuration' => [
                'talla' => 'M',
                'color_base' => 'gris_jaspeado',
                'tecnica_marcaje' => 'serigrafía',
                'area_impresion' => 'espalda_grande',
            ],
            'generated_sku' => 'POL-GJ-M-STAFF-100',
            'estimated_unit_price' => 13.00,
            'suggested_supplier_id' => Supplier::where('name', 'TextilCorp Bordados')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 20),
            'delivery_window_end' => Carbon::create(2025, 3, 5),
            'variant_status' => 'configured',
        ]);

        // Producto 3: Cuaderno para Asistentes
        $cuadernoTemplate = ProductTemplate::where('name', 'Cuaderno Corporativo Tapa Dura')->first();
        $cuadernoProject = ProductProject::create([
            'project_id' => $project->id,
            'product_template_id' => $cuadernoTemplate->id,
            'supplier_id' => Supplier::where('name', 'Office Essentials S.A.')->first()->id,
            'total_quantity' => 700,
            'avg_unit_price' => 15.00,
            'currency' => 'USD',
            'status' => 'approved',
            'notes' => 'Cuadernos corporativos para asistentes de la conferencia',
        ]);

        // Variantes de Cuaderno
        ProductVariant::create([
            'product_project_id' => $cuadernoProject->id,
            'name' => 'Cuaderno Tapa Azul con Debossing',
            'quantity' => 500,
            'configuration' => [
                'color_base' => 'azul',
                'tecnica_marcaje' => 'debossing',
                'area_impresion' => 'tapa_centro',
            ],
            'generated_sku' => 'CUA-AZ-DEB-500',
            'estimated_unit_price' => 14.50,
            'suggested_supplier_id' => Supplier::where('name', 'Office Essentials S.A.')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 10),
            'delivery_window_end' => Carbon::create(2025, 2, 25),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $cuadernoProject->id,
            'name' => 'Cuaderno Tapa Negra con Folia Plateada',
            'quantity' => 150,
            'configuration' => [
                'color_base' => 'negro',
                'tecnica_marcaje' => 'folia_plateada',
                'area_impresion' => 'tapa_centro',
            ],
            'generated_sku' => 'CUA-NE-FOL-150',
            'estimated_unit_price' => 16.00,
            'suggested_supplier_id' => Supplier::where('name', 'Office Essentials S.A.')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 10),
            'delivery_window_end' => Carbon::create(2025, 2, 25),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $cuadernoProject->id,
            'name' => 'Cuaderno Tapa Azul Personalizado VIP',
            'quantity' => 50,
            'configuration' => [
                'color_base' => 'azul',
                'tecnica_marcaje' => 'debossing',
                'area_impresion' => 'tapa_centro',
                'personalizacion' => 'nombre_asistente',
            ],
            'generated_sku' => 'CUA-AZ-VIP-50',
            'estimated_unit_price' => 18.00,
            'suggested_supplier_id' => Supplier::where('name', 'Office Essentials S.A.')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 10),
            'delivery_window_end' => Carbon::create(2025, 2, 25),
            'variant_status' => 'configured',
        ]);

        // Producto 4: Señalética del Evento
        $pendonTemplate = ProductTemplate::where('name', 'Pendón Roller Aluminio 80x200cm')->first();
        $pendonProject = ProductProject::create([
            'project_id' => $project->id,
            'product_template_id' => $pendonTemplate->id,
            'supplier_id' => Supplier::where('name', 'ArteImpreso Gráfica')->first()->id,
            'total_quantity' => 45,
            'avg_unit_price' => 85.00,
            'currency' => 'USD',
            'status' => 'approved',
            'notes' => 'Señalética y banners para el evento TechGlobal 2025',
        ]);

        // Variantes de Pendón
        ProductVariant::create([
            'product_project_id' => $pendonProject->id,
            'name' => 'Pendón Bienvenidos TechGlobal 2025',
            'quantity' => 10,
            'configuration' => [
                'color_base' => 'blanco',
                'color_print' => 'full_color',
                'tecnica_marcaje' => 'impresion_digital',
                'area_impresion' => 'completo',
                'diseno' => 'bienvenidos_entrada',
            ],
            'generated_sku' => 'PEN-BIEN-10',
            'estimated_unit_price' => 80.00,
            'suggested_supplier_id' => Supplier::where('name', 'ArteImpreso Gráfica')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 20),
            'delivery_window_end' => Carbon::create(2025, 3, 5),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $pendonProject->id,
            'name' => 'Pendón Agenda de Charlas',
            'quantity' => 15,
            'configuration' => [
                'color_base' => 'blanco',
                'color_print' => 'full_color',
                'tecnica_marcaje' => 'impresion_digital',
                'area_impresion' => 'completo',
                'diseno' => 'agenda_horarios',
            ],
            'generated_sku' => 'PEN-AGENDA-15',
            'estimated_unit_price' => 85.00,
            'suggested_supplier_id' => Supplier::where('name', 'ArteImpreso Gráfica')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 20),
            'delivery_window_end' => Carbon::create(2025, 3, 5),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $pendonProject->id,
            'name' => 'Pendón Auspiciadores y Partners',
            'quantity' => 20,
            'configuration' => [
                'color_base' => 'blanco',
                'color_print' => 'full_color',
                'tecnica_marcaje' => 'impresion_digital',
                'area_impresion' => 'completo',
                'diseno' => 'logos_partners',
            ],
            'generated_sku' => 'PEN-PARTNERS-20',
            'estimated_unit_price' => 90.00,
            'suggested_supplier_id' => Supplier::where('name', 'ArteImpreso Gráfica')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 20),
            'delivery_window_end' => Carbon::create(2025, 3, 5),
            'variant_status' => 'configured',
        ]);

        // Producto 5: Mochila para Speakers y VIPs
        $mochilaTemplate = ProductTemplate::where('name', 'Mochila Corporativa para Laptop')->first();
        $mochilaProject = ProductProject::create([
            'project_id' => $project->id,
            'product_template_id' => $mochilaTemplate->id,
            'supplier_id' => Supplier::where('name', 'Bags & Gear Pro')->first()->id,
            'total_quantity' => 200,
            'avg_unit_price' => 45.00,
            'currency' => 'USD',
            'status' => 'approved',
            'notes' => 'Mochilas corporativas para speakers y asistentes VIP',
        ]);

        // Variantes de Mochila
        ProductVariant::create([
            'product_project_id' => $mochilaProject->id,
            'name' => 'Mochila Gris Oscuro con Bordado',
            'quantity' => 100,
            'configuration' => [
                'color_base' => 'gris_oscuro',
                'tecnica_marcaje' => 'bordado',
                'area_impresion' => 'frente_centro',
                'color_bordado' => 'blanco',
            ],
            'generated_sku' => 'MOC-GR-BORD-100',
            'estimated_unit_price' => 42.00,
            'suggested_supplier_id' => Supplier::where('name', 'Bags & Gear Pro')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 25),
            'delivery_window_end' => Carbon::create(2025, 3, 10),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $mochilaProject->id,
            'name' => 'Mochila Negra con Bordado Gris',
            'quantity' => 50,
            'configuration' => [
                'color_base' => 'negro',
                'tecnica_marcaje' => 'bordado',
                'area_impresion' => 'frente_centro',
                'color_bordado' => 'gris',
            ],
            'generated_sku' => 'MOC-NE-BORD-50',
            'estimated_unit_price' => 44.00,
            'suggested_supplier_id' => Supplier::where('name', 'Bags & Gear Pro')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 25),
            'delivery_window_end' => Carbon::create(2025, 3, 10),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $mochilaProject->id,
            'name' => 'Mochila Gris Oscuro Personalizada Speaker',
            'quantity' => 50,
            'configuration' => [
                'color_base' => 'gris_oscuro',
                'tecnica_marcaje' => 'bordado',
                'area_impresion' => 'frente_centro',
                'color_bordado' => 'blanco',
                'personalizacion' => 'nombre_speaker',
            ],
            'generated_sku' => 'MOC-GR-SPEAKER-50',
            'estimated_unit_price' => 48.00,
            'suggested_supplier_id' => Supplier::where('name', 'Bags & Gear Pro')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 2, 25),
            'delivery_window_end' => Carbon::create(2025, 3, 10),
            'variant_status' => 'configured',
        ]);

        // Crear lotes de abastecimiento para cada variante
        $this->createProcurementLots($project);
    }

    /**
     * Seed Comercializadora Urbana S.A. project - Campaña Retail "Vive tu Ciudad"
     */
    private function seedUrbanaProject(): void
    {
        $customer = Customer::where('name', 'Comercializadora Urbana S.A.')->first();
        
        $project = Project::create([
            'customer_id' => $customer->id,
            'name' => 'Campaña Retail "Vive tu Ciudad"',
            'status' => 'active',
            'starts_at' => Carbon::create(2025, 4, 1),
            'ends_at' => Carbon::create(2025, 6, 30),
            'currency' => 'CLP',
        ]);

        // Producto 1: Lápices Promocionales
        $lapizTemplate = ProductTemplate::where('name', 'Lápiz Pasta Plástico Retráctil')->first();
        $lapizProject = ProductProject::create([
            'project_id' => $project->id,
            'product_template_id' => $lapizTemplate->id,
            'supplier_id' => Supplier::where('name', 'PromoFactory Global')->first()->id,
            'total_quantity' => 12500,
            'avg_unit_price' => 150,
            'currency' => 'CLP',
            'status' => 'approved',
            'notes' => 'Lápices promocionales para campaña masiva en puntos de venta',
        ]);

        // Variantes de Lápiz
        ProductVariant::create([
            'product_project_id' => $lapizProject->id,
            'name' => 'Lápiz Azul con Tinta Negra',
            'quantity' => 5000,
            'configuration' => [
                'color_base' => 'azul',
                'color_print' => '1_color',
                'tecnica_marcaje' => 'serigrafía',
                'area_impresion' => 'cuerpo',
            ],
            'generated_sku' => 'LAP-AZ-N-5K',
            'estimated_unit_price' => 140,
            'suggested_supplier_id' => Supplier::where('name', 'PromoFactory Global')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 3, 15),
            'delivery_window_end' => Carbon::create(2025, 3, 30),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $lapizProject->id,
            'name' => 'Lápiz Blanco con Tinta Azul',
            'quantity' => 5000,
            'configuration' => [
                'color_base' => 'blanco',
                'color_print' => '1_color',
                'tecnica_marcaje' => 'serigrafía',
                'area_impresion' => 'cuerpo',
            ],
            'generated_sku' => 'LAP-BL-A-5K',
            'estimated_unit_price' => 140,
            'suggested_supplier_id' => Supplier::where('name', 'PromoFactory Global')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 3, 15),
            'delivery_window_end' => Carbon::create(2025, 3, 30),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $lapizProject->id,
            'name' => 'Lápiz Rojo con Logo y Web',
            'quantity' => 2500,
            'configuration' => [
                'color_base' => 'rojo',
                'color_print' => '1_color',
                'tecnica_marcaje' => 'serigrafía',
                'area_impresion' => 'cuerpo',
            ],
            'generated_sku' => 'LAP-RO-LW-2.5K',
            'estimated_unit_price' => 160,
            'suggested_supplier_id' => Supplier::where('name', 'PromoFactory Global')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 3, 15),
            'delivery_window_end' => Carbon::create(2025, 3, 30),
            'variant_status' => 'configured',
        ]);

        $this->createProcurementLots($project);
    }

    /**
     * Seed Innovatech Solutions project - Onboarding Nuevos Talentos 2025
     */
    private function seedInnovatechProject(): void
    {
        $customer = Customer::where('name', 'Innovatech Solutions')->first();
        
        $project = Project::create([
            'customer_id' => $customer->id,
            'name' => 'Onboarding Nuevos Talentos 2025',
            'status' => 'active',
            'starts_at' => Carbon::create(2025, 1, 15),
            'ends_at' => Carbon::create(2025, 12, 31),
            'currency' => 'USD',
        ]);

        // Producto 1: Lanyards de Identificación
        $lanyardTemplate = ProductTemplate::where('name', 'Lanyard Poliéster con Mosquetón')->first();
        $lanyardProject = ProductProject::create([
            'project_id' => $project->id,
            'product_template_id' => $lanyardTemplate->id,
            'supplier_id' => Supplier::where('name', 'Bags & Gear Pro')->first()->id,
            'total_quantity' => 800,
            'avg_unit_price' => 2.50,
            'currency' => 'USD',
            'status' => 'approved',
            'notes' => 'Lanyards para identificación de personal en proceso de onboarding',
        ]);

        // Variantes de Lanyard
        ProductVariant::create([
            'product_project_id' => $lanyardProject->id,
            'name' => 'Lanyard Azul Corporativo',
            'quantity' => 500,
            'configuration' => [
                'color_base' => 'azul_corporativo',
                'color_print' => 'blanco',
                'tecnica_marcaje' => 'serigrafía',
                'area_impresion' => 'repetido',
            ],
            'generated_sku' => 'LAN-AZ-CORP-500',
            'estimated_unit_price' => 2.30,
            'suggested_supplier_id' => Supplier::where('name', 'Bags & Gear Pro')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 1, 1),
            'delivery_window_end' => Carbon::create(2025, 1, 15),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $lanyardProject->id,
            'name' => 'Lanyard Gris STAFF',
            'quantity' => 200,
            'configuration' => [
                'color_base' => 'gris',
                'color_print' => 'blanco',
                'tecnica_marcaje' => 'serigrafía',
                'area_impresion' => 'centro',
            ],
            'generated_sku' => 'LAN-GR-STAFF-200',
            'estimated_unit_price' => 2.40,
            'suggested_supplier_id' => Supplier::where('name', 'Bags & Gear Pro')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 1, 1),
            'delivery_window_end' => Carbon::create(2025, 1, 15),
            'variant_status' => 'configured',
        ]);

        ProductVariant::create([
            'product_project_id' => $lanyardProject->id,
            'name' => 'Lanyard Naranjo VISITANTE',
            'quantity' => 100,
            'configuration' => [
                'color_base' => 'naranjo',
                'color_print' => 'blanco',
                'tecnica_marcaje' => 'serigrafía',
                'area_impresion' => 'centro',
            ],
            'generated_sku' => 'LAN-NA-VISIT-100',
            'estimated_unit_price' => 2.60,
            'suggested_supplier_id' => Supplier::where('name', 'Bags & Gear Pro')->first()->id,
            'delivery_window_start' => Carbon::create(2025, 1, 1),
            'delivery_window_end' => Carbon::create(2025, 1, 15),
            'variant_status' => 'configured',
        ]);

        $this->createProcurementLots($project);
    }

    /**
     * Create procurement lots for project variants
     */
    private function createProcurementLots($project): void
    {
        foreach ($project->productProjects as $productProject) {
            foreach ($productProject->productVariants as $variant) {
                ProcurementLot::create([
                    'product_project_id' => $productProject->id,
                    'product_variant_id' => $variant->id,
                    'supplier_id' => $variant->suggested_supplier_id,
                    'quantity' => $variant->quantity,
                    'status' => 'draft',
                    'incoterm' => 'FOB',
                    'lead_time_days' => 30,
                    'currency' => $productProject->currency,
                ]);
            }
        }
    }
}
