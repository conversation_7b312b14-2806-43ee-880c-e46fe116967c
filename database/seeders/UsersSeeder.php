<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;

class UsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // admin
        $admin = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Dev User',
            'password' => 'password',
            'email_verified_at' => now(),
        ]);
        $admin->syncRoles(['admin']);

        // super admin
        $super = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'SysAdmin',
            'password' => 'password',
            'email_verified_at' => now(),
        ]);
        $super->syncRoles(['super_admin']);

        // panel user
        $panel = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Usuario plano',
            'password' => 'password',
            'email_verified_at' => now(),
        ]);
        $panel->syncRoles(['panel_user']);
    }
}
