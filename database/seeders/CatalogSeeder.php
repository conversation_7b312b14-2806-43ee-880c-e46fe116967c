<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductTemplate;
use App\Models\Attribute;
use App\Models\AttributeGroup;
use App\Models\ProductTemplateAttribute;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CatalogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedCategories();
        $this->seedAttributes();
        $this->seedTemplates();
    }

    /**
     * Seed product categories and subcategories from Gemini data
     */
    private function seedCategories(): void
    {
        // Categoría: Merchandising
        $merchandising = ProductCategory::create([
            'name' => 'Merchandising',
            'description' => 'Productos promocionales y regalos corporativos para aumentar la visibilidad de la marca',
            'status' => 'active',
        ]);

        $subcategories = [
            ['name' => 'Artículos para Beber', 'description' => 'Recipientes para líquidos, de uso personal o de escritorio'],
            ['name' => 'Instrumentos de Escritura', 'description' => 'Artículos para escribir y tomar notas'],
            ['name' => 'Vestuario y Accesorios', 'description' => 'Prendas y complementos de vestir con branding'],
            ['name' => 'Accesorios Tecnológicos', 'description' => 'Gadgets y complementos para dispositivos electrónicos'],
            ['name' => 'Artículos de Oficina y Escritorio', 'description' => 'Útiles para el espacio de trabajo'],
            ['name' => 'Estilo de Vida y Aire Libre', 'description' => 'Artículos prácticos para el día a día o actividades recreativas'],
            ['name' => 'Premios y Reconocimientos', 'description' => 'Artículos para galardonar o conmemorar logros'],
        ];

        foreach ($subcategories as $subcategory) {
            ProductSubcategory::create([
                'product_category_id' => $merchandising->id,
                'name' => $subcategory['name'],
                'description' => $subcategory['description'],
                'status' => 'active',
            ]);
        }

        // Categoría: Material de Punto de Venta (PDV) y Exhibición
        $pdv = ProductCategory::create([
            'name' => 'Material de Punto de Venta (PDV) y Exhibición',
            'description' => 'Artículos para mejorar la presencia de marca en puntos de venta o eventos',
            'status' => 'active',
        ]);

        $pdvSubcategories = [
            ['name' => 'Señalética y Banners', 'description' => 'Elementos gráficos de gran formato para comunicación visual'],
            ['name' => 'Banderas Miniatura y de Sobremesa', 'description' => 'Banderas de pequeño formato para decoración o eventos'],
            ['name' => 'Soportes y Unidades de Exhibición', 'description' => 'Estructuras para destacar productos o entregar información'],
            ['name' => 'Materiales Impresos', 'description' => 'Papelería y otros impresos para comunicación directa'],
            ['name' => 'Mobiliario y Accesorios Promocionales', 'description' => 'Muebles y estructuras brandeadas para eventos'],
        ];

        foreach ($pdvSubcategories as $subcategory) {
            ProductSubcategory::create([
                'product_category_id' => $pdv->id,
                'name' => $subcategory['name'],
                'description' => $subcategory['description'],
                'status' => 'active',
            ]);
        }

        // Categoría: Textiles
        $textiles = ProductCategory::create([
            'name' => 'Textiles',
            'description' => 'Productos donde la tela y su confección son la característica principal',
            'status' => 'active',
        ]);

        $textilesSubcategories = [
            ['name' => 'Vestuario (Prendas)', 'description' => 'Ropa de vestir de calidad superior a la de merchandising'],
            ['name' => 'Textiles para el Hogar', 'description' => 'Artículos de tela para uso doméstico'],
            ['name' => 'Bolsos y Soluciones de Transporte a base de Tela', 'description' => 'Bolsos de alta resistencia y construcción específica'],
        ];

        foreach ($textilesSubcategories as $subcategory) {
            ProductSubcategory::create([
                'product_category_id' => $textiles->id,
                'name' => $subcategory['name'],
                'description' => $subcategory['description'],
                'status' => 'active',
            ]);
        }
    }

    /**
     * Seed attributes and attribute groups
     */
    private function seedAttributes(): void
    {
        // Grupos de atributos
        $groups = [
            ['key' => 'general', 'name_es' => 'General', 'name_en' => 'General', 'description_es' => 'Atributos generales del producto', 'description_en' => 'General product attributes', 'position' => 1],
            ['key' => 'color', 'name_es' => 'Color', 'name_en' => 'Color', 'description_es' => 'Opciones de color', 'description_en' => 'Color options', 'position' => 2],
            ['key' => 'size', 'name_es' => 'Talla', 'name_en' => 'Size', 'description_es' => 'Opciones de talla', 'description_en' => 'Size options', 'position' => 3],
            ['key' => 'material', 'name_es' => 'Material', 'name_en' => 'Material', 'description_es' => 'Tipo de material', 'description_en' => 'Material type', 'position' => 4],
            ['key' => 'printing', 'name_es' => 'Técnica de Marcaje', 'name_en' => 'Printing Technique', 'description_es' => 'Técnicas de marcaje disponibles', 'description_en' => 'Available printing techniques', 'position' => 5],
        ];

        foreach ($groups as $groupData) {
            AttributeGroup::create(array_merge($groupData, ['active' => true]));
        }

        // Atributos comunes
        $attributes = [
            // Atributos de color
            ['key' => 'color_base', 'type' => 'enum', 'options_json' => ['options' => ['blanco', 'negro', 'azul', 'rojo', 'verde', 'amarillo']], 'label_es' => 'Color Base', 'label_en' => 'Base Color'],
            ['key' => 'color_print', 'type' => 'enum', 'options_json' => ['options' => ['1_color', '2_colors', 'full_color', 'negativo']], 'label_es' => 'Color de Impresión', 'label_en' => 'Print Color'],
            
            // Atributos de talla
            ['key' => 'talla', 'type' => 'enum', 'options_json' => ['options' => ['S', 'M', 'L', 'XL', 'XXL']], 'label_es' => 'Talla', 'label_en' => 'Size'],
            
            // Atributos de material
            ['key' => 'material', 'type' => 'enum', 'options_json' => ['options' => ['algodón', 'poliéster', 'nylon', 'acrílico', 'metal', 'cerámica', 'plástico']], 'label_es' => 'Material', 'label_en' => 'Material'],
            ['key' => 'material_composition', 'type' => 'string', 'label_es' => 'Composición Material', 'label_en' => 'Material Composition'],
            
            // Atributos de técnica de marcaje
            ['key' => 'tecnica_marcaje', 'type' => 'enum', 'options_json' => ['options' => ['serigrafía', 'bordado', 'sublimación', 'grabado_laser', 'estampado', 'debossing']], 'label_es' => 'Técnica de Marcaje', 'label_en' => 'Printing Technique'],
            ['key' => 'area_impresion', 'type' => 'string', 'label_es' => 'Área de Impresión', 'label_en' => 'Print Area'],
            
            // Atributos generales
            ['key' => 'peso', 'type' => 'decimal', 'label_es' => 'Peso (g)', 'label_en' => 'Weight (g)'],
            ['key' => 'dimensiones', 'type' => 'string', 'label_es' => 'Dimensiones', 'label_en' => 'Dimensions'],
            ['key' => 'capacidad', 'type' => 'string', 'label_es' => 'Capacidad', 'label_en' => 'Capacity'],
        ];

        foreach ($attributes as $attr) {
            Attribute::create(array_merge($attr, ['active' => true]));
        }
    }

    /**
     * Seed product templates from Gemini data
     */
    private function seedTemplates(): void
    {
        $templates = [
            // Merchandising - Artículos para Beber
            ['subcategory' => 'Artículos para Beber', 'name' => 'Tazón Cerámica 11oz', 'description' => 'Plantilla para tazón estándar de cerámica, ideal para sublimación'],
            ['subcategory' => 'Artículos para Beber', 'name' => 'Tazón Mágico 11oz', 'description' => 'Plantilla para tazón de cerámica termosensible que revela un diseño con calor'],
            ['subcategory' => 'Artículos para Beber', 'name' => 'Botella Deportiva Aluminio 750ml', 'description' => 'Plantilla para botella de aluminio con tapa rosca y mosquetón'],
            ['subcategory' => 'Artículos para Beber', 'name' => 'Vaso Térmico Acero Inoxidable 20oz', 'description' => 'Plantilla para vaso de doble pared con tapa plástica deslizable'],
            ['subcategory' => 'Artículos para Beber', 'name' => 'Vaso Plástico Reutilizable 16oz', 'description' => 'Plantilla para vaso de plástico rígido, tipo "estadio", para eventos'],
            
            // Merchandising - Instrumentos de Escritura
            ['subcategory' => 'Instrumentos de Escritura', 'name' => 'Lápiz Pasta Plástico Retráctil', 'description' => 'Plantilla para lápiz de plástico estándar con mecanismo de clic'],
            ['subcategory' => 'Instrumentos de Escritura', 'name' => 'Lápiz Pasta Metálico Premium', 'description' => 'Plantilla para lápiz metálico de mayor peso y calidad, con grabado láser'],
            ['subcategory' => 'Instrumentos de Escritura', 'name' => 'Portaminas 0.7mm con Goma', 'description' => 'Plantilla para portaminas plástico con goma de borrar incorporada'],
            ['subcategory' => 'Instrumentos de Escritura', 'name' => 'Destacador Fluorescente', 'description' => 'Plantilla para destacador con cuerpo plástico y punta biselada'],
            
            // Merchandising - Vestuario y Accesorios
            ['subcategory' => 'Vestuario y Accesorios', 'name' => 'Polera Algodón Cuello Redondo', 'description' => 'Plantilla base para polera de algodón para serigrafía o DTG'],
            ['subcategory' => 'Vestuario y Accesorios', 'name' => 'Jockey 6 Paneles con Broche Metálico', 'description' => 'Plantilla para gorra de algodón con visera curva y cierre ajustable'],
            ['subcategory' => 'Vestuario y Accesorios', 'name' => 'Lanyard Poliéster con Mosquetón', 'description' => 'Plantilla para cinta de cuello estándar para credenciales'],
            ['subcategory' => 'Vestuario y Accesorios', 'name' => 'Tote Bag de Algodón Crudo', 'description' => 'Plantilla para bolso de tela simple, reutilizable y de alta rotación'],
            
            // Merchandising - Accesorios Tecnológicos
            ['subcategory' => 'Accesorios Tecnológicos', 'name' => 'Pendrive USB 3.0 Carcasa Giratoria', 'description' => 'Plantilla para memoria USB con cuerpo de metal y plástico'],
            ['subcategory' => 'Accesorios Tecnológicos', 'name' => 'Batería Externa Plana 5000mAh', 'description' => 'Plantilla para cargador portátil delgado con puertos USB-A y USB-C'],
            ['subcategory' => 'Accesorios Tecnológicos', 'name' => 'Soporte para Celular Adhesivo', 'description' => 'Plantilla para anillo o pop-socket que se adhiere al reverso del teléfono'],
            
            // Merchandising - Artículos de Oficina y Escritorio
            ['subcategory' => 'Artículos de Oficina y Escritorio', 'name' => 'Cuaderno Corporativo Tapa Dura', 'description' => 'Plantilla para cuaderno tamaño A5 con tapa dura y hojas lineadas'],
            ['subcategory' => 'Artículos de Oficina y Escritorio', 'name' => 'Mousepad Goma y Tela', 'description' => 'Plantilla para alfombrilla de mouse estándar con base de goma antideslizante'],
            ['subcategory' => 'Artículos de Oficina y Escritorio', 'name' => 'Calendario de Escritorio Triangular', 'description' => 'Plantilla para calendario de sobremesa con anillado superior'],
            
            // PDV - Señalética y Banners
            ['subcategory' => 'Señalética y Banners', 'name' => 'Pendón Roller Aluminio 80x200cm', 'description' => 'Plantilla para display retráctil con gráfica en tela PVC'],
            ['subcategory' => 'Señalética y Banners', 'name' => 'Bandera Publicitaria tipo Vela 3m', 'description' => 'Plantilla para bandera de exterior con mástil flexible y estaca'],
            ['subcategory' => 'Señalética y Banners', 'name' => 'Adhesivo de Piso Alto Tráfico', 'description' => 'Plantilla para vinilo adhesivo laminado para suelo'],
            
            // PDV - Materiales Impresos
            ['subcategory' => 'Materiales Impresos', 'name' => 'Volante Publicitario Papel Couché', 'description' => 'Plantilla para flyer tamaño media carta, impreso por ambos lados'],
            ['subcategory' => 'Materiales Impresos', 'name' => 'Hablador de Mesa Acrílico', 'description' => 'Plantilla para soporte de gráfica en mesas de restaurantes o mostradores'],
            
            // PDV - Mobiliario y Accesorios Promocionales
            ['subcategory' => 'Mobiliario y Accesorios Promocionales', 'name' => 'Mesón Promocional Desarmable', 'description' => 'Plantilla para counter de PVC con gráfica frontal y superior'],
            ['subcategory' => 'Mobiliario y Accesorios Promocionales', 'name' => 'Carpa para Eventos 3x3m', 'description' => 'Plantilla para carpa plegable con estructura de aluminio y techo impreso'],
            
            // Textiles - Vestuario
            ['subcategory' => 'Vestuario (Prendas)', 'name' => 'Polera Polo Piqué', 'description' => 'Plantilla para polera tipo polo de tela piqué, ideal para uniformes corporativos'],
            ['subcategory' => 'Vestuario (Prendas)', 'name' => 'Chaqueta Softshell Corporativa', 'description' => 'Plantilla para chaqueta de tres capas resistente al agua y viento, con bordado'],
            ['subcategory' => 'Vestuario (Prendas)', 'name' => 'Uniforme Clínico (Scrub)', 'description' => 'Plantilla para conjunto de dos piezas de tela antifluidos'],
            
            // Textiles - Hogar
            ['subcategory' => 'Textiles para el Hogar', 'name' => 'Toalla de Baño Algodón 500gsm', 'description' => 'Plantilla para toalla de alta absorción con cenefa para bordado'],
            ['subcategory' => 'Textiles para el Hogar', 'name' => 'Manta Polar con Bordado', 'description' => 'Plantilla para manta de polar fleece con terminación de costura reforzada'],
            ['subcategory' => 'Textiles para el Hogar', 'name' => 'Delantal de Cocina Gabardina', 'description' => 'Plantilla para delantal resistente con bolsillos y cinta ajustable'],
            
            // Textiles - Bolsos
            ['subcategory' => 'Bolsos y Soluciones de Transporte a base de Tela', 'name' => 'Mochila Corporativa para Laptop', 'description' => 'Plantilla para mochila de poliéster con compartimento acolchado'],
            ['subcategory' => 'Bolsos y Soluciones de Transporte a base de Tela', 'name' => 'Bolso Deportivo de Lona', 'description' => 'Plantilla para bolso de viaje o gimnasio de alta capacidad y resistencia'],
        ];

        foreach ($templates as $template) {
            $subcategory = ProductSubcategory::where('name', $template['subcategory'])->first();
            if ($subcategory) {
                ProductTemplate::create([
                    'name' => $template['name'],
                    'product_subcategory_id' => $subcategory->id,
                    'status' => 'active',
                    'description' => ['es' => $template['description'], 'en' => $template['description']],
                    'metadata' => ['source' => 'gemini_seed', 'category' => 'catalog'],
                ]);
            }
        }

        // Asociar atributos a plantillas
        $this->associateAttributesToTemplates();
    }

    /**
     * Associate attributes to templates
     */
    private function associateAttributesToTemplates(): void
    {
        $templates = ProductTemplate::all();
        $attributes = Attribute::all();
        $groups = AttributeGroup::all();

        foreach ($templates as $template) {
            // Asociar atributos comunes
            $commonAttributes = [
                'color_base' => ['group' => 'color', 'required' => true, 'position' => 1],
                'color_print' => ['group' => 'color', 'required' => false, 'position' => 2],
                'tecnica_marcaje' => ['group' => 'printing', 'required' => true, 'position' => 1],
                'area_impresion' => ['group' => 'printing', 'required' => false, 'position' => 2],
            ];

            foreach ($commonAttributes as $attrKey => $config) {
                $attribute = $attributes->where('key', $attrKey)->first();
                $group = $groups->where('key', $config['group'])->first();
                
                if ($attribute && $group) {
                    ProductTemplateAttribute::create([
                        'product_template_id' => $template->id,
                        'attribute_id' => $attribute->id,
                        'attribute_group_id' => $group->id,
                        'is_required' => $config['required'],
                        'visible' => true,
                        'position' => $config['position'],
                        'ui_component' => $attribute->type === 'enum' ? 'select' : 'text',
                        'ui_props' => ['placeholder' => 'Seleccione ' . $attribute->label_es],
                        'default_value' => null,
                        'validation_rules' => $config['required'] ? ['required' => true] : null,
                        'placeholder' => 'Ingrese ' . $attribute->label_es,
                        'column_span' => 1,
                    ]);
                }
            }

            // Asociar atributos específicos según la categoría
            $this->associateSpecificAttributes($template, $attributes, $groups);
        }
    }

    /**
     * Associate specific attributes based on template category
     */
    private function associateSpecificAttributes($template, $attributes, $groups): void
    {
        $subcategory = $template->productSubcategory;
        
        // Atributos específicos por tipo de producto
        if (str_contains($subcategory->name, 'Vestuario')) {
            $tallaAttr = $attributes->where('key', 'talla')->first();
            $tallaGroup = $groups->where('key', 'size')->first();
            
            if ($tallaAttr && $tallaGroup) {
                ProductTemplateAttribute::create([
                    'product_template_id' => $template->id,
                    'attribute_id' => $tallaAttr->id,
                    'attribute_group_id' => $tallaGroup->id,
                    'is_required' => true,
                    'visible' => true,
                    'position' => 1,
                    'ui_component' => 'select',
                    'ui_props' => ['placeholder' => 'Seleccione talla'],
                    'default_value' => null,
                    'validation_rules' => ['required' => true],
                    'placeholder' => 'Seleccione talla',
                    'column_span' => 1,
                ]);
            }
        }

        if (str_contains($template->name, 'Tazón') || str_contains($template->name, 'Vaso') || str_contains($template->name, 'Botella')) {
            $capacidadAttr = $attributes->where('key', 'capacidad')->first();
            $generalGroup = $groups->where('key', 'general')->first();
            
            if ($capacidadAttr && $generalGroup) {
                ProductTemplateAttribute::create([
                    'product_template_id' => $template->id,
                    'attribute_id' => $capacidadAttr->id,
                    'attribute_group_id' => $generalGroup->id,
                    'is_required' => false,
                    'visible' => true,
                    'position' => 2,
                    'ui_component' => 'text',
                    'ui_props' => ['placeholder' => 'Ej: 350ml, 500ml'],
                    'default_value' => null,
                    'validation_rules' => null,
                    'placeholder' => 'Ingrese capacidad',
                    'column_span' => 1,
                ]);
            }
        }

        if (str_contains($template->name, 'Polera') || str_contains($template->name, 'Gorra') || str_contains($template->name, 'Tote')) {
            $materialAttr = $attributes->where('key', 'material')->first();
            $materialGroup = $groups->where('key', 'material')->first();
            
            if ($materialAttr && $materialGroup) {
                ProductTemplateAttribute::create([
                    'product_template_id' => $template->id,
                    'attribute_id' => $materialAttr->id,
                    'attribute_group_id' => $materialGroup->id,
                    'is_required' => true,
                    'visible' => true,
                    'position' => 1,
                    'ui_component' => 'select',
                    'ui_props' => ['placeholder' => 'Seleccione material'],
                    'default_value' => null,
                    'validation_rules' => ['required' => true],
                    'placeholder' => 'Seleccione material',
                    'column_span' => 1,
                ]);
            }
        }
    }
}
