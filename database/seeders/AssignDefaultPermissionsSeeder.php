<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class AssignDefaultPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Admin: todos los permisos
        $admin = Role::where('name', 'admin')->first();
        if ($admin) {
            $admin->syncPermissions(Permission::all());
        }

        // Panel user: permisos mínimos de lectura sobre User (ajusta según recursos)
        $panelUser = Role::where('name', 'panel_user')->first();
        if ($panelUser) {
            $readOnly = Permission::whereIn('name', [
                'ViewAny:User',
                'View:User',
            ])->get();
            $panelUser->syncPermissions($readOnly);
        }

        app(PermissionRegistrar::class)->forgetCachedPermissions();
    }
}
