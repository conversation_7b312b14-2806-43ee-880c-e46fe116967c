<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Attribute>
 */
class AttributeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = fake()->randomElement(['string', 'integer', 'decimal', 'boolean', 'enum', 'text', 'date', 'email', 'url']);
        
        return [
            'key' => fake()->unique()->slug(),
            'type' => $type,
            'options_json' => $type === 'enum' ? fake()->randomElement([
                ['options' => ['rojo', 'azul', 'verde']],
                ['options' => ['S', 'M', 'L', 'XL']],
            ]) : null,
            'label_es' => fake()->words(2, true),
            'label_en' => fake()->words(2, true),
            'active' => true,
        ];
    }
}
