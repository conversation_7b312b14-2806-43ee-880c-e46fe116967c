<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AttributeGroup>
 */
class AttributeGroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'key' => fake()->unique()->slug(),
            'name_es' => fake()->words(2, true),
            'name_en' => fake()->words(2, true),
            'description_es' => fake()->optional()->sentence(),
            'description_en' => fake()->optional()->sentence(),
            'icon' => fake()->optional()->randomElement(['heroicon-o-cube', 'heroicon-o-color-swatch', 'heroicon-o-ruler']),
            'position' => fake()->numberBetween(0, 100),
            'active' => true,
        ];
    }
}
