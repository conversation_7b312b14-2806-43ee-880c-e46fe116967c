<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_template_attributes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_template_id')->constrained()->restrictOnDelete();
            $table->foreignId('attribute_id')->constrained()->restrictOnDelete();
            $table->foreignId('attribute_group_id')->constrained()->restrictOnDelete();
            $table->boolean('is_required')->default(false);
            $table->boolean('visible')->default(true);
            $table->integer('position')->default(0);
            $table->string('ui_component')->default('text');
            $table->jsonb('ui_props')->nullable();
            $table->jsonb('default_value')->nullable();
            $table->jsonb('validation_rules')->nullable();
            $table->string('placeholder')->nullable();
            $table->integer('column_span')->nullable();
            $table->timestamps();
            
            $table->unique(['product_template_id', 'attribute_id']);
            $table->index(['product_template_id', 'attribute_group_id', 'position']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_template_attributes');
    }
};
