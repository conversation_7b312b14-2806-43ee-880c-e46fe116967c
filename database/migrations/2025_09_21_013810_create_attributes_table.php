<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attributes', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->string('type');
            $table->jsonb('options_json')->nullable();
            $table->string('label_es');
            $table->string('label_en');
            $table->boolean('active')->default(true);
            $table->timestamps();
        });
        
        // Agregar constraint CHECK con SQL raw
        DB::statement("ALTER TABLE attributes ADD CONSTRAINT check_attribute_type CHECK (type IN ('string', 'integer', 'decimal', 'boolean', 'enum', 'text', 'date', 'email', 'url'))");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attributes');
    }
};
