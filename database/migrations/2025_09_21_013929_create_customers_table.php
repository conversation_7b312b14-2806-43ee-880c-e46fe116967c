<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('tax_id')->nullable()->unique();
            $table->jsonb('contact_info')->nullable();
            $table->string('status')->default('active');
            $table->timestamps();
        });
        
        // Agregar constraint CHECK con SQL raw
        DB::statement("ALTER TABLE customers ADD CONSTRAINT check_customer_status CHECK (status IN ('active', 'inactive'))");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
