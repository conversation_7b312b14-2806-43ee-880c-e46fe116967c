{"permissions": {"allow": ["mcp__laravel-boost__application-info", "mcp__laravel-boost__database-schema", "Bash(find:*)", "Bash(php artisan make:migration:*)", "<PERSON><PERSON>(sed:*)", "Bash(vendor/bin/pint:*)", "Bash(php artisan:*)", "mcp__laravel-boost__tinker", "Bash(git add:*)", "mcp__laravel-boost__search-docs", "<PERSON><PERSON>(git worktree:*)", "Bash(git checkout:*)", "Read(//home/<USER>/work/ps/pss-lw-migration/**)", "Bash(git commit:*)", "Read(//home/<USER>/work/ps/pss-claude-migration/**)", "Read(//home/<USER>/work/ps/**)", "Bash(tree:*)", "Bash(php -l:*)", "mcp__laravel-boost__database-query"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["laravel-boost"]}