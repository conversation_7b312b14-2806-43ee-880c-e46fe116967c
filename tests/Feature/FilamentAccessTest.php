<?php

declare(strict_types=1);

use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function (): void {
    Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
    Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);
});

it('denies access to admin panel for users without a role', function (): void {
    $user = User::factory()->create();

    $this->assertFalse($user->canAccessPanel(Filament::getPanel('admin')));
});

it('allows access to admin panel for users with admin role', function (): void {
    $user = User::factory()->create()->assignRole('admin');

    $this->assertTrue($user->canAccessPanel(Filament::getPanel('admin')));
});

it('allows access to admin panel for users with super_admin role', function (): void {
    $user = User::factory()->create()->assignRole('super_admin');

    $this->assertTrue($user->canAccessPanel(Filament::getPanel('admin')));
});

it('denies access to admin panel for users with panel_user role', function (): void {
    Role::firstOrCreate(['name' => 'panel_user', 'guard_name' => 'web']);
    $user = User::factory()->create()->assignRole('panel_user');

    $this->assertFalse($user->canAccessPanel(Filament::getPanel('admin')));
});

it('denies access to admin panel for users with other roles', function (): void {
    Role::firstOrCreate(['name' => 'editor', 'guard_name' => 'web']);
    $user = User::factory()->create()->assignRole('editor');

    $this->assertFalse($user->canAccessPanel(Filament::getPanel('admin')));
});
