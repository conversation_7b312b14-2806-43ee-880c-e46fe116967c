<?php

declare(strict_types=1);

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function (): void {
    // Define ruta protegida por role:admin
    Route::middleware(['web', 'role:admin'])
        ->get('/__test/admin-only', fn () => 'ok');

    Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
    Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);
});

it('permite acceso a la ruta solo a usuarios con rol admin', function (): void {
    $admin = User::factory()->create()->assignRole('admin');

    $this->actingAs($admin);
    $this->get('/__test/admin-only')->assertOk();
});

it('deniega acceso a usuarios sin rol', function (): void {
    $user = User::factory()->create();

    $this->actingAs($user);
    $this->get('/__test/admin-only')->assertForbidden();
});

it('deniega acceso a usuarios con rol super_admin (middleware no usa Gate)', function (): void {
    $super = User::factory()->create()->assignRole('super_admin');

    $this->actingAs($super);
    $this->get('/__test/admin-only')->assertForbidden();
});



