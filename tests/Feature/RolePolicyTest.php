<?php

declare(strict_types=1);

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function (): void {
    Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
    Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);
});

it('autoriza viewAny(Role) cuando el usuario tiene permiso ViewAny:Role', function (): void {
    $perm = Permission::firstOrCreate(['name' => 'ViewAny:Role', 'guard_name' => 'web']);

    $user = User::factory()->create();
    $user->givePermissionTo($perm);

    expect(Gate::forUser($user)->allows('viewAny', Role::class))->toBeTrue();
});

it('niega viewAny(Role) sin permiso', function (): void {
    $user = User::factory()->create();

    expect(Gate::forUser($user)->allows('viewAny', Role::class))->toBeFalse();
});

it('bypass de super_admin permite delete(Role) sin permisos explícitos', function (): void {
    $super = User::factory()->create()->assignRole('super_admin');

    $targetRole = Role::firstOrCreate(['name' => 'editor', 'guard_name' => 'web']);

    expect(Gate::forUser($super)->allows('delete', $targetRole))->toBeTrue();
});



