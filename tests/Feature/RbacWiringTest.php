<?php
declare(strict_types=1);

use Illuminate\Routing\Router;

it('registra los aliases de middleware de Spatie en el router', function (): void {
    /** @var Router $router */
    $router = app(Router::class);
    $aliases = $router->getMiddleware();

    expect($aliases['role'] ?? null)
        ->toBe(Spatie\Permission\Middleware\RoleMiddleware::class)
        ->and($aliases['permission'] ?? null)
        ->toBe(Spatie\Permission\Middleware\PermissionMiddleware::class)
        ->and($aliases['role_or_permission'] ?? null)
        ->toBe(Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class);
});

it('configura default_guard en web para Spatie Permission', function (): void {
    expect(config('permission.default_guard'))->toBe('web');
});


