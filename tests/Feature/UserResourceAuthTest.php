<?php

declare(strict_types=1);

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function (): void {
    Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
    Role::firstOrCreate(['name' => 'panel_user', 'guard_name' => 'web']);
    Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'web']);
});

it('admin con permisos de User puede viewAny y create', function (): void {
    $perms = Permission::insertOrIgnore([
        ['name' => 'ViewAny:User', 'guard_name' => 'web'],
        ['name' => 'Create:User', 'guard_name' => 'web'],
    ]);

    $admin = User::factory()->create()->assignRole('admin');
    $admin->givePermissionTo(['ViewAny:User', 'Create:User']);

    expect(Gate::forUser($admin)->allows('viewAny', User::class))->toBeTrue();
    expect(Gate::forUser($admin)->allows('create', User::class))->toBeTrue();
});

it('panel_user sin permisos explícitos no puede viewAny ni create', function (): void {
    $panel = User::factory()->create()->assignRole('panel_user');

    expect(Gate::forUser($panel)->allows('viewAny', User::class))->toBeFalse();
    expect(Gate::forUser($panel)->allows('create', User::class))->toBeFalse();
});

it('super_admin bypass puede delete', function (): void {
    $super = User::factory()->create()->assignRole('super_admin');

    $target = User::factory()->create();

    expect(Gate::forUser($super)->allows('delete', $target))->toBeTrue();
});



