# Implementation Plan

[Overview]
Formalizar la jerarquía de catálogo y plantillas mediante migraciones y modelos consistentes que garanticen integridad y estados trazables.

Este plan abarca únicamente la fase de migraciones y modelos, preparando el terreno para futuras iteraciones sobre Filament, factorías y pruebas. Primero unificaremos el esquema relacional para que categorías, subcategorías, tipos y plantillas mantengan vínculos rígidos, incorporando dimensiones, grupos de atributos y atributos con referencias explícitas. Posteriormente ajustaremos los modelos de Eloquent para reflejar las nuevas restricciones, casts, soft deletes y relaciones recíprocas, asegurando que el dominio exponga métodos claros y libres de ambigüedades. La intención es dejar la capa de datos lista para que la UI, las factories y las suites de test se construyan sobre cimientos confiables en etapas posteriores.

[Types]
Introducir enums y casts consistentes para estados y llaves foráneas en modelos del catálogo.

- `App\Enums\CommonStatus` y `App\Enums\ProductTemplateStatus` seguirán siendo los estados vigentes; se documenta que todos los modelos con campo `status` deben castear a estos enums mediante `casts()` actualizado.
- Nueva enum `App\Enums\AttributeDataType` (si no existe) para acotar `Attribute::$type`, valores permitidos: `string`, `integer`, `decimal`, `boolean`, `select`, `multiselect`, `json`, `date`, `datetime`. (Creación diferida para fase posterior, pero el modelo debe validar contra esta lista).
- Nueva relación pivot `product_template_dimension` no es necesaria al consolidar `dimensions` dentro de plantillas específicas; se utilizará FK directa `dimensions.product_template_id` (BIGINT unsigned, required).
- `attribute_groups` recibirá `product_template_id` (BIGINT unsigned) además de `dimension_id`, imponiendo integridad jerárquica (ambos NOT NULL).
- `attributes` mantendrá `attribute_group_id` y se añadirá `product_template_id` derivado (opcional) para simplificar consultas; se castea `validation_rules`, `options` y `default_value` a array.

[Files]
Modificar migraciones y modelos para reflejar la nueva jerarquía y reglas de integridad.

- Nuevas migraciones (fase actual): ninguna, se editarán migraciones existentes de catálogo (se permite modificación por instrucción “refundacional”).
- Migraciones a editar:
  - `database/migrations/2025_09_21_013804_create_product_categories_table.php`: añadir índices (`status`), `softDeletes`, y timestamp de estado.
  - `database/migrations/2025_09_21_013807_create_product_subcategories_table.php`: reforzar FK, `softDeletes`, índices combinados.
  - `database/migrations/2025_09_23_103157_create_product_types_table.php`: mantener `softDeletes`, añadir FK a plantilla si procede (no en esta fase), CHECK actualizado.
  - `database/migrations/2025_09_21_013815_create_product_templates_table.php`: agregar `product_type_id` NOT NULL (elim. migración incremental), integrar `softDeletes`, `status` enum CHECK extendida y metadata default.
  - `database/migrations/2025_09_23_215859_create_dimensions_table.php`: convertir dimensiones en dependientes de plantilla (`product_template_id` NOT NULL), eliminar `unique` globales, añadir `softDeletes`.
  - `database/migrations/2025_09_21_013816_create_attribute_groups_table.php` + `2025_09_23_215920_modify_dimension_in_attribute_groups_table.php`: consolidar en un único bloque para incluir `product_template_id`, `dimension_id` NOT NULL, `softDeletes`, índices, y remover migración incremental innecesaria.
  - `database/migrations/2025_09_21_013817_create_attributes_table.php` + posteriores alter: integrar campos `key`, `unit`, `default_placeholder`, `softDeletes` en migración base; añadir `product_template_id` y constraints compuestas.
  - `database/migrations/2025_09_21_013818_create_product_template_attributes_table.php`: validar `restrictOnDelete`→`cascadeOnDelete` según nueva jerarquía, añadir Unique triples (`template`, `group`, `attribute`) y CHECKs para booleanos.
- Modelos a editar:
  - `app/Models/ProductCategory.php`, `ProductSubcategory.php`, `ProductType.php`, `ProductTemplate.php`, `Dimension.php`, `AttributeGroup.php`, `Attribute.php`, `ProductTemplateAttribute.php`.
- Configuración relacionada:
  - Actualizar `AppServiceProvider` si requiere “configureUsing” para `SoftDeletes` y orden.
- Eliminar migraciones incrementales redundantes (`add_product_type_id_to_product_templates`, `alter_attribute_groups_add_key_and_dimension`, `alter_attributes_add_key_unit_and_indexes`, `modify_dimension_in_attribute_groups_table`) tras integrar cambios en migraciones base.

[Functions]
Actualizar métodos de relación y casts en modelos.

- No se crearán nuevas funciones globales, pero se modificarán métodos existentes:
  - `ProductCategory::productSubcategories()` forzar `orderBy` y `withTrashed` según necesidad.
  - `ProductSubcategory::productTypes()`, `productTemplates()` adaptados a `cascade`.
  - `ProductType::productTemplates()` asegurar `hasMany`.
  - `ProductTemplate`: actualizar `productSubcategory()`, `productType()`, añadir `dimensions()`, `attributeGroups()`, `attributes()` coherent.
  - `Dimension`: reemplazar `attributeGroups()` con `->where('product_template_id', $this->product_template_id)`.
  - `AttributeGroup`: asegurar `dimension()` y `productTemplate()` FKs.
  - `Attribute`: añadir `productTemplate()` y `attributeGroup()` integrados.
  - `ProductTemplateAttribute`: ajustar `attribute()`, `attributeGroup()`, `productTemplate()`.

[Classes]
Reforzar modelos existentes sin añadir nuevas clases.

- Nuevas clases: ninguna en esta fase (enums se postergan).
- Modificaciones:
  - `App\Models\ProductCategory`: incorporar `SoftDeletes`, `casts()` con `deleted_at`, scopes locales (`scopeActive`).
  - `App\Models\ProductSubcategory`: idem, plus `belongsTo` con `withDefault`.
  - `App\Models\ProductType`: `SoftDeletes`, slug mutator opcional, `casts()`.
  - `App\Models\ProductTemplate`: `SoftDeletes`, `casts()` para metadata, `booted()` para enforzar consistencia (p.ej. validar type-subcategory).
  - `App\Models\Dimension`: `SoftDeletes`, `belongsTo ProductTemplate`.
  - `App\Models\AttributeGroup`: `SoftDeletes`, `belongsTo ProductTemplate`, `belongsTo Dimension`.
  - `App\Models\Attribute`: `SoftDeletes`, `belongsTo AttributeGroup`, `belongsTo ProductTemplate`.
  - `App\Models\ProductTemplateAttribute`: cast booleans y arrays, `belongsTo` actualizados.
- Eliminaciones: Ninguna.

[Dependencies]
No se agregan dependencias externas; ajustes internos solamente.

[Testing]
Se planifica definir escenarios unitarios en fase posterior; aquí solo se documenta necesidad.

- Registrar que tras migraciones/modelos se deberán actualizar factories y tests (Pest) en iteraciones siguientes.
- Incluir nota para ejecutar `php artisan migrate:fresh` en entorno local para validar supuestos.

[Implementation Order]
Iterar sobre migraciones y modelos de manera incremental para preservar integridad.

1. Consolidar migraciones (editar archivos base y eliminar incrementales), asegurando constraints y soft deletes.
2. Ajustar modelos Eloquent para reflejar nuevas columnas, casts y relaciones.
3. Revisar dependencias cruzadas (p. ej. remover imports de migraciones eliminadas) y ejecutar linting/format (`pint`).
4. Documentar pasos pendientes para fases Filament/factories/tests.
