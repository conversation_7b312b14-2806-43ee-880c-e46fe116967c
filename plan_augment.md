# Plan de Implementación Detallado - Features de Catálogo y Plantilla de Producto

## Estado Actual de la Implementación

### Arquitectura Implementada (Fuente de Verdad)
```
ProductCategory (1:N) → ProductSubcategory (1:N) → ProductType (1:N) → ProductTemplate
Dimension (1:N) → AttributeGroup (1:N) → Attribute
ProductTemplate (N:M) → Attribute (via ProductTemplateAttribute)
```

### Modelos Implementados
- ✅ `ProductCategory` - Con SoftDeletes, CommonStatus enum
- ✅ `ProductSubcategory` - Sin SoftDeletes, CommonStatus enum  
- ✅ `ProductType` - Nuevo nivel jerárquico, CommonStatus enum
- ✅ `ProductTemplate` - Con product_type_id nullable, ProductTemplateStatus enum
- ✅ `Dimension` - Entidad organizacional para AttributeGroups
- ✅ `AttributeGroup` - Con dimension_id, key único, status
- ✅ `Attribute` - Con key único, type, unit, validation_rules, options
- ✅ `ProductTemplateAttribute` - Pivot table con configuración UI

### Migraciones Aplicadas
- ✅ Base tables creadas con constraints CHECK para status
- ✅ Campos adicionales: key, unit, description, default_placeholder
- ✅ Foreign keys con restrict/cascade según corresponde
- ✅ Índices básicos implementados

## Problemas Identificados y Plan de Corrección

### FASE 1: Corrección de Inconsistencias Críticas

#### 1.1 Arreglar Relación AttributeGroup → ProductTemplate
**Problema:** El modelo `AttributeGroup` tiene relación `productTemplate()` que no existe en DB.

**Solución:**
```php
// Eliminar de AttributeGroup.php líneas 69-74
public function productTemplate(): BelongsTo
{
    return $this->belongsTo(ProductTemplate::class);
}
```

**Archivos a modificar:**
- `app/Models/AttributeGroup.php`
- `database/factories/AttributeGroupFactory.php` (remover product_template_id)
- `app/Filament/Resources/AttributeGroups/Schemas/AttributeGroupForm.php` (remover campo)

#### 1.2 Agregar SoftDeletes a ProductSubcategory
**Problema:** Documentación especifica SoftDeletes pero no está implementado.

**Solución:**
```php
// Nueva migración
Schema::table('product_subcategories', function (Blueprint $table) {
    $table->softDeletes();
});

// Actualizar modelo
use SoftDeletes;
```

#### 1.3 Agregar Campo status_changed_at a ProductCategory
**Problema:** Campo en fillable pero no existe en DB.

**Solución:**
```php
// Nueva migración
Schema::table('product_categories', function (Blueprint $table) {
    $table->timestamp('status_changed_at')->nullable();
});
```

### FASE 2: Mejoras de Validación y Constraints

#### 2.1 Agregar CHECK Constraints Faltantes
**Problema:** Faltan constraints en attribute_groups y attributes.

**Solución:**
```sql
ALTER TABLE attribute_groups ADD CONSTRAINT check_attribute_group_status 
CHECK (status IN ('active', 'inactive'));

ALTER TABLE attributes ADD CONSTRAINT check_attribute_status 
CHECK (status IN ('active', 'inactive'));

ALTER TABLE attributes ADD CONSTRAINT check_attribute_type 
CHECK (type IN ('string', 'number', 'boolean', 'select', 'multiselect', 'date', 'array', 'json'));
```

#### 2.2 Implementar Validación en Modelos
**Problema:** Solo AttributeGroup y Attribute tienen métodos rules().

**Solución:**
- Agregar métodos `rules()` y `updateRules()` a ProductCategory, ProductSubcategory, ProductTemplate
- Crear Form Requests para validación compleja
- Implementar model events para validación automática

### FASE 3: Completar Funcionalidad Filament

#### 3.1 Implementar Relationship Managers
**Archivos a crear:**
```
app/Filament/Resources/ProductCategories/RelationManagers/ProductSubcategoriesRelationManager.php
app/Filament/Resources/ProductSubcategories/RelationManagers/ProductTypesRelationManager.php
app/Filament/Resources/ProductSubcategories/RelationManagers/ProductTemplatesRelationManager.php
app/Filament/Resources/AttributeGroups/RelationManagers/AttributesRelationManager.php
app/Filament/Resources/Dimensions/RelationManagers/AttributeGroupsRelationManager.php
```

#### 3.2 Mejorar Formularios y Tablas
**Mejoras necesarias:**
- Formularios jerárquicos con dependencias dinámicas
- Filtros avanzados por status, categoría, tipo
- Bulk actions para cambios de estado
- Search functionality mejorada
- Ordenamiento drag & drop para sort_order

#### 3.3 Organizar Navegación
**Problema:** Grupos inconsistentes entre recursos.

**Solución:**
```php
// Unificar en dos grupos principales:
'Catálogo de Productos' => [ProductCategory, ProductSubcategory, ProductType]
'Especificación de Productos' => [Dimension, AttributeGroup, Attribute, ProductTemplate]
```

### FASE 4: Optimización y Características Avanzadas

#### 4.1 Implementar Scopes Útiles
```php
// En todos los modelos con status
public function scopeActive($query) {
    return $query->where('status', 'active');
}

// En ProductTemplate
public function scopeBySubcategory($query, $subcategoryId) {
    return $query->where('product_subcategory_id', $subcategoryId);
}
```

#### 4.2 Mejorar Indexación
```php
// Índices compuestos necesarios
$table->index(['status', 'sort_order']); // attribute_groups, attributes
$table->index(['product_category_id', 'status']); // product_subcategories  
$table->index(['product_subcategory_id', 'status']); // product_types
$table->index(['name']); // Para búsquedas en todos los modelos
```

#### 4.3 Implementar Eager Loading
```php
// En relaciones frecuentes
public function productSubcategories(): HasMany
{
    return $this->hasMany(ProductSubcategory::class)
        ->with(['productTypes', 'productTemplates'])
        ->withTrashed()
        ->orderBy('name');
}
```

### FASE 5: Seguridad y Autorización

#### 5.1 Crear Policies
**Archivos a crear:**
```
app/Policies/ProductCategoryPolicy.php
app/Policies/ProductSubcategoryPolicy.php
app/Policies/ProductTypePolicy.php
app/Policies/ProductTemplatePolicy.php
app/Policies/AttributeGroupPolicy.php
app/Policies/AttributePolicy.php
app/Policies/DimensionPolicy.php
```

#### 5.2 Integrar con Filament Shield
```php
// Generar permisos automáticamente
php artisan shield:generate --all

// Configurar en cada Resource
protected static bool $shouldRegisterNavigation = true;
```

## Cronograma de Implementación

### Semana 1: Correcciones Críticas (Fase 1)
- [ ] Arreglar relación AttributeGroup → ProductTemplate
- [ ] Agregar SoftDeletes a ProductSubcategory  
- [ ] Agregar status_changed_at a ProductCategory
- [ ] Actualizar factories inconsistentes

### Semana 2: Validación y Constraints (Fase 2)
- [ ] Agregar CHECK constraints faltantes
- [ ] Implementar métodos rules() en todos los modelos
- [ ] Crear Form Requests
- [ ] Agregar model events para validación

### Semana 3: Funcionalidad Filament (Fase 3)
- [ ] Crear Relationship Managers
- [ ] Mejorar formularios con dependencias
- [ ] Implementar filtros y búsqueda avanzada
- [ ] Organizar navegación

### Semana 4: Optimización (Fase 4)
- [ ] Implementar scopes útiles
- [ ] Optimizar indexación
- [ ] Configurar eager loading
- [ ] Testing exhaustivo

### Semana 5: Seguridad (Fase 5)
- [ ] Crear policies completas
- [ ] Integrar Filament Shield
- [ ] Configurar permisos granulares
- [ ] Audit trail para cambios críticos

## Archivos Prioritarios a Modificar

### Modelos (Alta Prioridad)
1. `app/Models/AttributeGroup.php` - Remover relación incorrecta
2. `app/Models/ProductSubcategory.php` - Agregar SoftDeletes
3. `app/Models/ProductCategory.php` - Agregar validación
4. `app/Models/ProductTemplate.php` - Mejorar relaciones

### Migraciones (Alta Prioridad)  
1. Nueva: `add_soft_deletes_to_product_subcategories`
2. Nueva: `add_status_changed_at_to_product_categories`
3. Nueva: `add_missing_check_constraints`

### Factories (Media Prioridad)
1. `database/factories/AttributeGroupFactory.php` - Remover product_template_id
2. `database/factories/ProductTemplateFactory.php` - Agregar product_type_id

### Filament Resources (Media Prioridad)
1. Todos los Form schemas - Mejorar validación
2. Todos los Table configs - Agregar filtros
3. Crear Relationship Managers

## Criterios de Éxito

### Funcionalidad
- ✅ Todas las relaciones funcionan correctamente
- ✅ Formularios jerárquicos operativos
- ✅ Validación completa en frontend y backend
- ✅ Búsqueda y filtrado eficiente

### Calidad de Código
- ✅ 100% compatibilidad con Laravel 12
- ✅ Uso idiomático de Filament 4
- ✅ Cobertura de tests > 80%
- ✅ Sin warnings en análisis estático

### Performance
- ✅ Queries optimizadas con eager loading
- ✅ Índices apropiados para consultas frecuentes
- ✅ Tiempo de respuesta < 200ms en operaciones CRUD

### Seguridad
- ✅ Autorización granular implementada
- ✅ Validación de entrada robusta
- ✅ Audit trail para cambios críticos

## Detalles de Implementación Específicos

### Corrección de AttributeGroup Model
```php
// REMOVER estas líneas de app/Models/AttributeGroup.php (69-74):
/**
 * Get the product template that owns the attribute group.
 */
public function productTemplate(): BelongsTo
{
    return $this->belongsTo(ProductTemplate::class);
}
```

### Nueva Migración para SoftDeletes
```php
// database/migrations/YYYY_MM_DD_add_soft_deletes_to_product_subcategories.php
public function up(): void
{
    Schema::table('product_subcategories', function (Blueprint $table) {
        $table->softDeletes();
    });
}
```

### Actualización de Factory
```php
// database/factories/AttributeGroupFactory.php - REMOVER línea 20:
'product_template_id' => \App\Models\ProductTemplate::factory(),

// REEMPLAZAR con:
'dimension_id' => \App\Models\Dimension::factory(),
```

### Ejemplo de Relationship Manager
```php
// app/Filament/Resources/ProductCategories/RelationManagers/ProductSubcategoriesRelationManager.php
class ProductSubcategoriesRelationManager extends RelationManager
{
    protected static string $relationship = 'productSubcategories';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->searchable()->sortable(),
                TextColumn::make('status')->badge(),
                TextColumn::make('productTypes_count')->counts('productTypes'),
            ])
            ->filters([
                SelectFilter::make('status')->options(CommonStatus::class),
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ]);
    }
}
```

### Mejoras de Formulario Jerárquico
```php
// En ProductTemplateForm.php
Select::make('product_subcategory_id')
    ->relationship('productSubcategory', 'name')
    ->live()
    ->afterStateUpdated(function ($state, callable $set) {
        $set('product_type_id', null); // Reset dependent field
    }),

Select::make('product_type_id')
    ->relationship(
        'productType',
        'name',
        fn ($query, $get) => $query->where('product_subcategory_id', $get('product_subcategory_id'))
    )
    ->disabled(fn ($get) => !$get('product_subcategory_id'))
```

## Testing Strategy

### Unit Tests Necesarios
```php
// tests/Unit/Models/ProductCategoryTest.php
test('product category has many subcategories')
test('product category soft deletes correctly')
test('product category status validation works')

// tests/Unit/Models/AttributeGroupTest.php
test('attribute group belongs to dimension')
test('attribute group has many attributes')
test('attribute group does not belong to product template') // Verificar corrección
```

### Feature Tests Necesarios
```php
// tests/Feature/Filament/ProductCatalogTest.php
test('can create product category through filament')
test('can create subcategory with parent category')
test('hierarchical forms work correctly')
test('relationship managers display correctly')
```

## Comandos de Verificación

### Verificar Estado Actual
```bash
# Verificar migraciones aplicadas
php artisan migrate:status

# Verificar que seeders funcionan
php artisan db:seed --class=ProductTaxonomySeeder

# Verificar relaciones en tinker
php artisan tinker
>>> App\Models\AttributeGroup::with('dimension', 'attributes')->first()
>>> App\Models\ProductCategory::with('productSubcategories.productTypes')->first()
```

### Verificar Correcciones
```bash
# Después de aplicar correcciones
php artisan test --filter=ProductCatalog
vendor/bin/pint --test
php artisan route:list --name=filament
```

## Notas de Implementación

1. **Prioridad Alta**: Corregir la relación AttributeGroup → ProductTemplate antes que nada
2. **Cuidado**: Al agregar SoftDeletes, verificar que no rompa seeders existentes
3. **Testing**: Cada corrección debe tener su test correspondiente
4. **Performance**: Implementar eager loading después de corregir relaciones
5. **Documentación**: Actualizar README con nueva arquitectura final

Este plan asume que la implementación actual es la fuente de verdad y se enfoca en completar y corregir lo que ya existe, manteniendo compatibilidad total con Laravel 12 y Filament 4.
