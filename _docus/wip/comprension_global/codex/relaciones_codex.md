# Relaciones entre Entidades (Codex)

## Catálogo de Producto
- product_categories 1 → N product_subcategories
  - Clave: product_subcategories.product_category_id (FK, restrict delete si hay subcategorías).
- product_subcategories 1 → N product_templates
  - Clave: product_templates.product_subcategory_id (FK, restrict delete si hay plantillas).
- attributes N ↔ N product_templates (vía product_template_attributes)
  - Pivot: product_template_attributes(product_template_id, attribute_id, attribute_group_id, …).
  - Unicidad por plantilla/atributo: UNIQUE(product_template_id, attribute_id).
  - Orden y visibilidad por grupo: position y visible (puede haber índice único parcial por position cuando visible=TRUE).
- attribute_groups 1 → N product_template_attributes
  - Las secciones de UI se determinan por attribute_group_id en el pivot.
- product_templates 1 → N template_versions (opcional)
  - Clave: template_versions.product_template_id; UNIQUE(product_template_id, number).
- template_versions 1 → N template_version_attributes (opcional)
  - PK compuesta: (template_version_id, attribute_id); referencia a attribute_group_id.

## Capa Comercial de Proyecto
- customers 1 → N projects
  - Clave: projects.customer_id.
- projects 1 → N product_projects
  - Clave: product_projects.project_id.
- product_templates 1 → N product_projects
  - Clave: product_projects.product_template_id (define el tipo base de producto).
- product_projects 1 → N product_variants
  - Clave: product_variants.product_project_id.
- suppliers 1 → N product_projects (opcional)
  - Clave: product_projects.supplier_id (pre‑selección sugerida para negociación consolidada).

## Abastecimiento y Cotizaciones
- product_variants 1 → N procurement_lots
  - Claves: procurement_lots.product_variant_id (NOT NULL) y procurement_lots.product_project_id.
  - Regla: un solo lote abierto por variante → UNIQUE parcial en product_variant_id WHERE status IN ('draft','rfq_open','quoted').
- suppliers 1 → N supplier_quotes
  - supplier_quotes.supplier_id (FK).
- procurement_lots 1 → N supplier_quotes
  - supplier_quotes.procurement_lot_id (FK).
  - Regla: una cotización activa por lote/proveedor → UNIQUE parcial en (procurement_lot_id, supplier_id) WHERE status IN ('draft','sent','received','shortlisted').
- procurement_lots N → 1 suppliers (al adjudicar)
  - Campos en el lote: awarded_quote_id (FK a supplier_quotes.id), awarded_at, agreed_unit_price, terms.
- procurement_groups 1 → N procurement_group_items
  - procurement_group_items.procurement_group_id (FK).
- procurement_group_items N → 1 procurement_lots
  - procurement_group_items.procurement_lot_id (FK) (agrega demanda de un lote mono‑variante al grupo).
- procurement_groups 1 → N pricing_agreements
  - pricing_agreements.procurement_group_id (FK); además referencia a suppliers.id.
- pricing_agreements 1 → N pricing_agreement_tiers
  - pricing_agreement_tiers.pricing_agreement_id (FK) con rangos (min_qty, max_qty).
- group_supplier_quotes (opcional) N → 1 procurement_groups y N → 1 suppliers
  - Alternativa a cotización por lote; se usa a nivel de grupo.

## Órdenes y Cumplimiento
- suppliers 1 → N purchase_orders
  - purchase_orders.supplier_id (FK).
- projects 1 → N purchase_orders (opcional)
  - purchase_orders.project_id (FK) cuando corresponde consolidar por proyecto.
- purchase_orders 1 → N purchase_order_items
  - purchase_order_items.purchase_order_id (FK).
- purchase_order_items N → 1 procurement_lots (o product_variants)
  - Se recomienda referenciar procurement_lot_id para mantener trazabilidad con la negociación adjudicada.
  - Snapshots: pricing_agreement_tier_id (nullable), unit_price, currency, incoterm, terms.
- purchase_order_items 1 → N production_lots
  - production_lots.purchase_order_item_id (FK) para dividir fabricación/entregas.
- shipments 1 → N shipment_items
  - shipment_items.shipment_id (FK).
- shipment_items N → 1 production_lots (o purchase_order_items)
  - shipment_items.production_lot_id (FK) preferido; alternativamente purchase_order_item_id.
- local_distributions 1 → N local_distribution_items
  - local_distribution_items.local_distribution_id (FK).
- local_distribution_items N → 1 production_lots (o purchase_order_items)
  - Similar a shipment_items, para la etapa local.

## Costos y Auditoría
- cost_items N → 1 entidad objetivo según nivel
  - Modelado como relación polimórfica o con `level` + `reference_id` a: procurement_groups, purchase_orders, product_projects, procurement_lots.
  - Se prorratean a variantes/ítems según `allocation_method` (por cantidad, valor, fijo, etc.).

## Reglas de Integridad y Cascadas (sugeridas)
- Eliminar product_templates → CASCADE en product_template_attributes y template_versions (si existen); RESTRICT si hay product_projects vinculados.
- Eliminar attributes o attribute_groups → RESTRICT si están referenciados por pivots/versiones.
- Eliminar projects/customers/suppliers → RESTRICT si hay dependencias activas (PO, lots, quotes) o usar soft‑deletes.
- Eliminar procurement_lots → RESTRICT si hay supplier_quotes adjudicadas o purchase_order_items creados.
- Eliminar purchase_orders → CASCADE en purchase_order_items; RESTRICT si existen shipments/local_distributions asociados.

## Índices y Unicidades Clave
- product_template_attributes: UNIQUE(product_template_id, attribute_id); índices por (product_template_id, attribute_group_id, position) y parcial por position cuando visible=TRUE.
- product_variants: UNIQUE(generated_sku); GIN en configuration (JSONB).
- procurement_lots: UNIQUE parcial de lote abierto por variante.
- supplier_quotes: UNIQUE parcial por (procurement_lot_id, supplier_id) en estados abiertos.
- pricing_agreements: activo por grupo/proveedor (valid_from/valid_to) — validar solapes por aplicación.

## Notas de Consistencia de Nombres
- Subcategorías: usar product_subcategory_id en todas las referencias.
- Configuración de variantes: campo configuration (JSONB) y generated_sku en product_variants.
- Referencias de ítems de OC: preferir procurement_lot_id para mantener el hilo negociación → orden → entrega.
