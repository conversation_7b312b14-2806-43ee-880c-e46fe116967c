# Entidades del Sistema (Codex)

## Catálogo de Producto
- `product_categories`
  - Propósito: agrupar familias de productos (p. ej., Textiles, Electrónicos).
  - Campos clave: `id`, `name`, `description`, `status`.
- `product_subcategories`
  - Propósito: subfamilias dentro de una categoría.
  - Campos: `id`, `product_category_id` (FK), `name`, `description`, `status`.
- `attributes`
  - Propósito: catálogo global de atributos reutilizables para plantillas.
  - Campos: `id`, `key` (único), `type` (string, integer, decimal, boolean, enum, text, date, email, url), `options_json` (JSONB para enums), `label_es`, `label_en`, `active`.
- `attribute_groups`
  - Propósito: organizar atributos en secciones de UI/negocio.
  - Campos: `id`, `key`, `name_es`, `name_en`, `description_es`, `description_en`, `icon`, `position`, `active`.
- `product_templates`
  - Propósito: plantilla base desde la que se configuran variantes.
  - Campos: `id`, `name`, `product_subcategory_id` (FK), `status` (draft, active, obsolete), `description`/`metadata` (JSONB opcional).
- `product_template_attributes`
  - Propósito: pivot “rico” que define cómo usa la plantilla cada atributo.
  - Campos: `id`, `product_template_id` (FK), `attribute_id` (FK), `attribute_group_id` (FK), `is_required`, `visible`, `position`, `ui_component`, `ui_props` (JSONB), `default_value` (JSONB), `validation_rules` (JSONB), `placeholder`, `column_span`.
- `template_versions` (opcional)
  - Propósito: versionado de plantillas para trazabilidad.
  - Campos: `id`, `product_template_id` (FK), `number` (único por plantilla), `published_at`, `active`.
- `template_version_attributes` (opcional)
  - Propósito: snapshot de la configuración de atributos por versión.
  - Campos: `template_version_id` (FK), `attribute_id` (FK), `attribute_group_id` (FK), mismos flags/config del pivot; PK compuesta (`template_version_id`,`attribute_id`).

## Capa Comercial de Proyecto
- `customers`
  - Propósito: clientes que contratan los proyectos comerciales.
  - Campos: `id`, `name`, `tax_id`, `contact_info` (JSONB), `status`.
- `projects`
  - Propósito: contexto del proyecto (cliente, fechas, estado).
  - Campos: `id`, `name`, `customer_id` (FK), `status`, `starts_at`, `ends_at`.
- `product_projects`
  - Propósito: contenedor comercial consolidado dentro de un proyecto.
  - Campos: `id`, `project_id` (FK), `product_template_id` (FK), `supplier_id` (nullable), `total_quantity`, `avg_unit_price` (nullable), `currency`, `status` (draft, quoted, approved, po_issued, in_production, received, closed), `notes`.
- `product_variants`
  - Propósito: instancias específicas con configuración y cantidad dentro del `product_project`.
  - Campos: `id`, `product_project_id` (FK), `name` (nullable), `quantity`, `configuration` (JSONB), `generated_sku` (UNIQUE), `estimated_unit_price` (nullable), `suggested_supplier_id` (nullable), `delivery_window` (nullable), `variant_status`.

## Abastecimiento y Cotizaciones
- `suppliers`
  - Propósito: maestros de proveedores.
  - Campos: `id`, `name`, `tax_id`, `contact_info` (JSONB), `status`.
- `procurement_lots`
  - Propósito: lote de abastecimiento mono‑variante; negocia una `product_variant`.
  - Campos: `id`, `product_project_id` (FK), `product_variant_id` (FK, NOT NULL), `supplier_id` (nullable hasta adjudicar), `awarded_quote_id` (nullable), `awarded_at` (nullable), `quantity`, `agreed_unit_price` (nullable), `incoterm` (nullable), `lead_time_days` (nullable), `currency` (nullable), `terms` (nullable), `status` (draft, rfq_open, quoted, awarded, closed).
  - Índices: único lote abierto por variante (parcial) sobre `product_variant_id` cuando `status IN ('draft','rfq_open','quoted')`.
- `supplier_quotes`
  - Propósito: cotizaciones por proveedor para un lote.
  - Campos: `id`, `procurement_lot_id` (FK), `supplier_id` (FK), `status` (draft, sent, received, shortlisted, awarded, rejected, expired), `unit_price`, `currency`, `incoterm`, `lead_time_days`, `valid_until` (nullable), `attachments` (JSONB), `notes`.
  - Índices: unicidad parcial por (`procurement_lot_id`,`supplier_id`) en estados abiertos (draft/sent/received/shortlisted).
- `procurement_groups`
  - Propósito: consolidación multi‑proyecto para apalancar precio por volumen.
  - Campos: `id`, `name`, `supplier_id` (FK), `currency`, `status` (draft, rfq_sent, quoted, awarded, closed), `valid_from`, `valid_to`, `notes`.
- `procurement_group_items`
  - Propósito: ítems agregados al grupo (demanda concreta por lote).
  - Campos: `id`, `procurement_group_id` (FK), `procurement_lot_id` (FK), `requested_quantity`.
- `pricing_agreements`
  - Propósito: acuerdo de precio adjudicado con tramos.
  - Campos: `id`, `procurement_group_id` (FK), `supplier_id` (FK), `status` (active, expired), `valid_from`, `valid_to`, `notes`.
- `pricing_agreement_tiers`
  - Propósito: tramos de precio por volumen.
  - Campos: `id`, `pricing_agreement_id` (FK), `min_qty`, `max_qty` (nullable), `unit_price`.
- `group_supplier_quotes` (opcional)
  - Propósito: cotizaciones por grupo como alternativa a `supplier_quotes` por lote.
  - Campos: similar a `supplier_quotes` pero referenciando `procurement_group_id`.

## Órdenes y Cumplimiento
- `purchase_orders`
  - Propósito: órdenes de compra emitidas al proveedor.
  - Campos: `id`, `supplier_id` (FK), `project_id` (FK, opcional), `status`, `currency`, `incoterm` (nullable), `issued_at`, `expected_at` (nullable), `notes`.
- `purchase_order_items`
  - Propósito: ítems de la OC con snapshot de precio/condiciones.
  - Campos: `id`, `purchase_order_id` (FK), `procurement_lot_id` (FK) o `product_variant_id` (según implementación), `quantity`, `unit_price`, `currency`, snapshots (`pricing_agreement_tier_id` nullable, `terms`, `incoterm`).
- `production_lots`
  - Propósito: dividir ítems de OC en lotes de producción/entrega.
  - Campos: `id`, `purchase_order_item_id` (FK), `quantity`, `status`, `notes`.
- `shipments`
  - Propósito: embarques internacionales (u otra logística de salida del proveedor).
  - Campos: `id`, `carrier`, `reference`/`tracking`, `shipped_at`, `arrived_at` (nullable), `status`, `incoterm` (opcional), `notes`.
- `shipment_items`
  - Propósito: cantidades por embarque.
  - Campos: `id`, `shipment_id` (FK), `production_lot_id` (FK) o `purchase_order_item_id`, `quantity`.
- `local_distributions`
  - Propósito: distribución/local fulfillment tras arribo.
  - Campos: `id`, `destination`, `reference`, `status`, `notes`.
- `local_distribution_items`
  - Propósito: cantidades por distribución local.
  - Campos: `id`, `local_distribution_id` (FK), `production_lot_id` (FK) o `purchase_order_item_id`, `quantity`.

## Costos y Auditoría
- `cost_items`
  - Propósito: costos adicionales prorrateables a distintos niveles (grupo, OC, proyecto, lote).
  - Campos: `id`, `level` (enum app: group/po/project/lot), `reference_id`, `type`, `amount`, `currency`, `allocation_method`, `notes`.

Notas de consistencia
- Se usa `product_subcategory_id` (no `subcategory_id`) para mantener el prefijo consistente.
- Para variantes, se adopta `configuration` (JSONB) y `generated_sku` según el modelo Codex.
- En abastecimiento, se adopta `procurement_lots` (no `variant_procurements`), con unicidad parcial de lote abierto por variante.
