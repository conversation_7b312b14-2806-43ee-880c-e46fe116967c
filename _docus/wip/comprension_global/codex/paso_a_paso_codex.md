# Paso a Paso de Planificación (Codex)

- Define el cliente y crea el proyecto con su vigencia y moneda.
- Selecciona la plantilla de producto adecuada y crea el `product_project`.
- Desglosa la demanda en `product_variants` con cantidades y configuración validada por la plantilla.
- Genera SKUs, valida reglas (requeridos, tipos, enums) y estima precios unitarios preliminares.
- Crea `procurement_lots` (uno por variante) y fija cantidades y objetivos (Incoterm, moneda y lead time en días).
- Envía RFQs a proveedores como `supplier_quotes` por lote y, si aplica, crea un `procurement_group` para consolidar volumen.
- Recibe y normaliza cotizaciones, compara precio/plazo/condiciones y shortlistea proveedores.
- Adjudica por lote (`awarded_quote`) o por grupo (`pricing_agreements` con tiers) y fija snapshots de condiciones.
- Calcula costo “fully loaded” con `cost_items` prorrateados y define margen para la cotización al cliente.
- Emite la cotización al cliente, registra vigencia y cambia el `product_project` a estado quoted.
- Tras aprobación del cliente, cambia a approved y emite `purchase_orders` con sus `purchase_order_items` vinculados a lotes.
- Planifica producción y entregas creando `production_lots` y fechas objetivo por ítem.
- Ejecuta logística registrando `shipments` y `local_distributions` con sus ítems y cantidades recibidas.
- Monitorea estados por entidad (rfq_open/quoted/awarded/po_issued/in_production/received) y bloquea ediciones en fases comprometidas.
- Consolida costos reales (goods, flete, setups, aranceles) y calcula `avg_unit_price` del proyecto.
- Cierra el proyecto al completar entregas y facturación, archivando acuerdos y documentos para auditoría.

## Anotaciones por paso (Expandido)

1) Define el cliente y crea el proyecto con su vigencia y moneda.
   - Entidades: customers, projects; fija `customer_id`, fechas (`starts_at`, `ends_at`) y moneda de referencia.
   - Estado inicial: `projects.status = draft`; documenta notas/objetivos comerciales.

2) Selecciona la plantilla de producto adecuada y crea el `product_project`.
   - Entidades: product_templates → product_projects (`product_template_id`, `project_id`).
   - Opcional: preselecciona `supplier_id` si hay proveedor preferente; define `total_quantity` objetivo.

3) Desglosa la demanda en `product_variants` con cantidades y configuración validada por la plantilla.
   - `product_variants.configuration` (JSONB) valida contra el esquema de la plantilla (pivot rico).
   - Usa atributos requeridos, tipos y enums; define `quantity` por variante.

4) Genera SKUs, valida reglas (requeridos, tipos, enums) y estima precios unitarios preliminares.
   - Genera `generated_sku` (UNIQUE) y completa `estimated_unit_price` si aún no hay costos adjudicados.
   - Corre validaciones combinadas: tipo + reglas del pivot + “required”.

5) Crea `procurement_lots` (uno por variante) y fija cantidades y objetivos (Incoterm, moneda y lead time en días).
   - Unicidad parcial: un solo lote abierto por `product_variant_id` (status IN draft/rfq_open/quoted).
   - Ajusta `quantity` del lote (puede ser igual a la variante o dividir en fases).

6) Envía RFQs a proveedores como `supplier_quotes` por lote y, si aplica, crea un `procurement_group` para consolidar volumen.
   - Define alcance: `currency`, `incoterm`, `lead_time_days`, `valid_until`; adjunta especificaciones/arte (attachments JSONB).
   - En grupos, agrega demanda vía `procurement_group_items` para sumar cantidades multi‑proyecto.

7) Recibe y normaliza cotizaciones, compara precio/plazo/condiciones y shortlistea proveedores.
   - Normaliza `unit_price`, `currency`, `incoterm`, plazos; evalúa técnica y comercial.
   - Mantén estados de la quote (sent/received/shortlisted) y deja trazas en notas.

8) Adjudica por lote (`awarded_quote`) o por grupo (`pricing_agreements` con tiers) y fija snapshots de condiciones.
   - En lote: set `awarded_quote_id`, `agreed_unit_price`, `supplier_id`, `awarded_at`.
   - En grupo: crea `pricing_agreements` + `pricing_agreement_tiers` y marca el acuerdo activo.

9) Calcula costo “fully loaded” con `cost_items` prorrateados y define margen para la cotización al cliente.
   - Niveles de costo: group/po/project/lot con `allocation_method` (cantidad, valor, fijo).
   - Documenta supuestos, tipo de cambio y reglas de redondeo.

10) Emite la cotización al cliente, registra vigencia y cambia el `product_project` a estado quoted.
   - Si se requiere auditoría, persiste snapshot en tablas `customer_quotes`/`customer_quote_items` (opcional).
   - Incluye configuraciones resumidas por variante y condiciones comerciales.

11) Tras aprobación del cliente, cambia a approved y emite `purchase_orders` con sus `purchase_order_items` vinculados a lotes.
   - Los ítems referencian `procurement_lot_id` y guardan snapshots: `unit_price`, `currency`, `incoterm`, `terms` y (si aplica) `pricing_agreement_tier_id`.
   - Estado del proyecto: `po_issued` una vez emitidas las OCs relevantes.

12) Planifica producción y entregas creando `production_lots` y fechas objetivo por ítem.
   - Divide por hitos/entregas parciales; asocia cantidades a cada `production_lot`.
   - Coordina con proveedor ventanas de entrega y QA si aplica.

13) Ejecuta logística registrando `shipments` y `local_distributions` con sus ítems y cantidades recibidas.
   - `shipment_items` preferentemente referencian `production_lot_id`; registra `tracking`, fechas y recepción.
   - Tras arribo, planifica distribución local con `local_distributions` e ítems asociados.

14) Monitorea estados por entidad (rfq_open/quoted/awarded/po_issued/in_production/received) y bloquea ediciones en fases comprometidas.
   - Implementa pequeñas state machines y reglas de “no edición” tras adjudicar/emitir OC.
   - Alertas por desvíos de plazo, cantidad o costo.

15) Consolida costos reales (goods, flete, setups, aranceles) y calcula `avg_unit_price` del proyecto.
   - Actualiza `cost_items` con montos finales; recalcula prorrateos y compara vs estimado.
   - Reporta “goods only” vs “fully loaded” y variaciones.

16) Cierra el proyecto al completar entregas y facturación, archivando acuerdos y documentos para auditoría.
   - Criterio: cantidades recibidas, facturas conciliadas y costos cerrados.
   - Archiva acuerdos (`pricing_agreements`), OCs, embarques y cotizaciones para trazabilidad.
