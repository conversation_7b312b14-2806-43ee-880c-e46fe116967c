# Resumen de Arquitectura (Codex)

## Visión General
- Núcleo en PostgreSQL + Laravel con Filament 4 para administración.
- Modelo flexible: plantillas de producto + variantes con configuración en JSONB.
- Consolidación comercial a nivel “producto del proyecto”, manteniendo detalle por variante.
- Trazabilidad completa: cotizaciones, acuerdos, órdenes de compra, entregas y costos.

## Datos y Modelo de Producto
- Catálogo global de atributos reutilizables y grupos; “pivot rico” por plantilla define visibilidad, orden, validaciones y componente UI.
- Variantes guardan especificaciones en `JSONB` con índices GIN para búsquedas por clave/rango.
- Versionado opcional de plantillas; `generated_sku` y reglas de validación combinadas (tipo + pivot + requeridos).
- Sin ENUMs nativos: `VARCHAR` + `CHECK`; enums en PHP para tipado.

## Comercial y Abastecimiento
- `product_projects` consolidan negociación; cada variante se negocia en un `procurement_lot` mono‑variante.
- RFQ y `supplier_quotes` por lote; consolidación multi‑proyecto vía `procurement_groups` + `pricing_agreements` con tiers.
- Emisión de `purchase_orders` con snapshot de precio/condiciones; registro de entregas y estado logístico.
- `cost_items` en varios niveles (grupo/OC/proyecto/lote) con prorrateo para costo “fully loaded”.

## UI y Validación (Filament 4)
- Formularios dinámicos generados desde la plantilla (secciones, orden, componentes especializados).
- API v4: columnas con badges, selectores, validaciones contextuales y cache de esquema.
- Recursos para construir/editar plantillas y variantes, con vista previa y conteos.

## Performance, Reglas y Auditoría
- Índices: GIN en JSONB, BTREE en claves, unicidades parciales (p. ej., un lote abierto por variante).
- State machine ligera para estados (plantillas/variantes/RFQ/PO) y bloqueo de ediciones.
- Snapshots de tiers, precios y términos para auditoría; consistencia y mantenibilidad enfocadas.

