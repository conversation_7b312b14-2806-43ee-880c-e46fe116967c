# Relaciones entre Entidades del Sistema

Este documento describe las principales relaciones y flujos de datos entre las entidades del sistema, siguiendo una progresión lógica desde la definición del producto hasta su entrega y costeo.

### 1. Relaciones en el Catálogo y Definición de Productos

El núcleo del sistema se basa en una jerarquía clara para definir productos.

-   **`Product_Category` → `Product_Subcategory` (Uno a Muchos)**: Una categoría (ej: "Textiles") agrupa múltiples subcategorías (ej: "Gorras", "Camisetas").
-   **`Product_Subcategory` → `Product_Template` (Uno a Muchos)**: Una subcategoría contiene varias plantillas de producto.
-   **`Product_Template` ↔ `Attribute` (Muchos a Muchos)**: Se materializa a través de la tabla pivote **`product_template_attributes`**. Una plantilla puede tener múltiples atributos, y un mismo atributo (como "Color") puede ser usado en múltiples plantillas.
-   **`Product_Template` → `Template_Version` (Uno a Muchos)**: Para habilitar la trazabilidad, una plantilla puede tener múltiples versiones. Esto permite actualizar el diseño de una plantilla sin alterar los productos ya creados con versiones anteriores.
-   **`Template_Version` ↔ `Attribute` (Muchos a Muchos)**: Similar a la relación principal, pero específica a una versión. Se materializa a través de **`template_version_attributes`**.
-   **`Product_Template` → `Product_Variant` (Uno a Muchos)**: Una plantilla actúa como un molde para crear múltiples variantes de producto. Cada variante se basa en la configuración de la plantilla (o una versión específica de ella).

### 2. Flujo desde el Proyecto hasta la Negociación

Este flujo conecta el contexto comercial con la necesidad de compra.

-   **`Customer` → `Project` (Uno a Muchos)**: Un cliente puede tener múltiples proyectos.
-   **`Project` → `Product_Project` (Uno a Muchos)**: Un proyecto agrupa varios "productos de proyecto", que son los contenedores comerciales para lo que se va a comprar.
-   **`Product_Project` → `Product_Variant` (Uno a Muchos)**: Dentro de un producto de proyecto, se definen las distintas variantes necesarias.
-   **`Product_Variant` → `Procurement_Lot` (Uno a Muchos)**: Cada variante que necesita ser comprada genera un "lote de abastecimiento". Esta entidad es la unidad fundamental para la negociación.
-   **`Procurement_Lot` → `RFQ` (Uno a Muchos)**: Desde un lote se pueden generar varias solicitudes de cotización a lo largo del tiempo.
-   **`RFQ` → `Supplier_Quote` (Uno a Muchos)**: Una RFQ enviada a varios proveedores recibirá múltiples cotizaciones de vuelta.

### 3. Flujo de Consolidación y Adjudicación

Aquí se gestiona la negociación por volumen y la selección de ofertas.

-   **`Procurement_Group` → `Procurement_Group_Item` (Uno a Muchos)**: Un grupo de consolidación contiene múltiples ítems, donde cada ítem es un **`Procurement_Lot`** de un proyecto.
-   **`Procurement_Group` → `Group_Supplier_Quote` (Uno a Muchos)**: Un grupo de consolidación puede recibir múltiples cotizaciones de proveedores, que incluyen precios por tramos.
-   **`Group_Supplier_Quote` → `Group_Supplier_Quote_Tier` (Uno a Muchos)**: Cada cotización de grupo detalla sus tramos de precios.
-   **`Group_Supplier_Quote` → `Pricing_Agreement` (Uno a Uno)**: La cotización de grupo que es adjudicada se convierte en un acuerdo de precios formal.
-   **`Pricing_Agreement` → `Pricing_Agreement_Tier` (Uno a Muchos)**: El acuerdo de precios formaliza los tramos de precios que se aplicarán.
-   **`Supplier_Quote` → `Purchase_Conditions` (Uno a Uno)**: Para negociaciones individuales (no en grupo), la cotización ganadora se registra como las "condiciones de compra" oficiales.

### 4. Flujo de Orden de Compra y Producción

El acuerdo se formaliza en una OC y comienza la producción.

-   **`Procurement_Lot` → `Purchase_Order_Item` (Uno a Uno)**: El lote de abastecimiento, una vez adjudicado (ya sea individualmente o por un acuerdo de grupo), se convierte en una línea de una orden de compra.
-   **`Purchase_Order` → `Purchase_Order_Item` (Uno a Muchos)**: Una orden de compra, emitida a un **`Supplier`**, consolida múltiples líneas de ítems.
-   **`Purchase_Order_Item` → `Production_Lot` (Uno a Muchos)**: Una línea de la OC puede dividirse en varios lotes de producción para facilitar la planificación y el seguimiento.

### 5. Flujo de Logística y Entrega

Se gestiona el transporte y la entrega final.

-   **`Production_Lot` ↔ `Shipment` (Muchos a Muchos)**: La relación se materializa a través de la tabla **`shipment_items`**. Un lote de producción puede ser dividido en varios embarques, y un embarque puede contener múltiples lotes de producción.
-   **`Shipment_Item` → `Local_Distribution_Item` (Uno a Muchos)**: Asumiendo que todo producto pasa por un embarque, los ítems de un embarque que llegan al almacén local pueden ser distribuidos en varias entregas de última milla.

### 6. Relaciones de Costos y Finanzas

Los costos se asocian de manera flexible a diferentes niveles del proceso.

-   **`Cost_Item` (Relación Polimórfica)**: Esta entidad es clave para el costeo. Puede vincularse a un **`Product_Project`**, un **`Purchase_Order`**, un **`Shipment`**, etc., permitiendo asignar costos al nivel que corresponda.
-   **`Cost_Item` ↔ `Payment` (Muchos a Muchos)**: La relación se gestiona a través de la tabla **`payment_allocations`**. Un pago puede cubrir varias obligaciones de costo (facturas), y una obligación de costo puede ser pagada en varios plazos.