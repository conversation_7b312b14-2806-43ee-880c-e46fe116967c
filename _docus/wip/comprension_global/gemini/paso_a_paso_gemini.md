# Paso a Paso del Flujo de Planificación y Ejecución

Este documento describe el flujo de trabajo completo del sistema, desde la creación de un proyecto hasta su ejecución, incorporando la lógica de negocio implícita en el modelo de datos.

### Fase 1: Definición y Configuración del Proyecto

1.  **Crear el Proyecto**: Se inicia un `Project` para un `Customer` específico, definiendo el marco general del trabajo.

2.  **Definir Productos del Proyecto**: Dentro del `Project`, se crea un `Product_Project` por cada tipo de producto requerido, actuando como un contenedor para la demanda de ese producto.

3.  **Configurar Variantes**: Se desglosa la demanda de cada `Product_Project` en `Product_Variants` específicas, detallando cantidades, atributos (color, talla), y cualquier otra configuración.

### Fase 2: Planificación de Abastecimiento y Cálculo de Costos

4.  **Iniciar la Compra**: Cada `Product_Variant` genera un `Procurement_Lot` (lote de abastecimiento), que es la unidad principal para negociar su compra.

5.  **Consolidar Demanda (Opcional)**: Si es estratégico, varios `Procurement_Lots` (incluso de distintos proyectos) se pueden agrupar en un `Procurement_Group` para negociar con proveedores por volumen.

6.  **Obtener Ofertas**: Se emiten `RFQs` (Solicitudes de Cotización) y se reciben `Supplier_Quotes` (cotizaciones de proveedores) para los lotes o grupos.

7.  **Adjudicar y Fijar Condiciones**: Se selecciona la mejor oferta. Esto formaliza los términos de compra, ya sea en `Purchase_Conditions` (para una oferta individual) o creando un `Pricing_Agreement` (para una oferta de grupo con tramos de precios).

8.  **Calcular Costo Interno**: Con el precio de compra ya fijado, el sistema calcula el costo total y unitario (`fully loaded cost`) sumando todos los `Cost_Items` asociados (flete, aduanas, moldes, etc.).

### Fase 3: Cotización y Aprobación del Cliente (Paso Clave)

9.  **Generar Cotización al Cliente**: Usando el costo interno total como base, la empresa añade su margen de beneficio y presenta una cotización formal al `Customer`.

10. **Marcar como Cotizado**: El estado del `Product_Project` se actualiza a `quoted` para reflejar que la oferta ha sido enviada y se está a la espera de una respuesta.

11. **Recibir Aprobación del Cliente**: Una vez que el cliente acepta la cotización, el estado del `Product_Project` cambia a `approved`. Esta es la luz verde para comprometer el gasto con los proveedores.

### Fase 4: Ejecución y Logística

12. **Emitir Órdenes de Compra**: Con la aprobación del cliente ya asegurada, se emiten las `Purchase_Orders` formales a los proveedores para iniciar la producción.

13. **Seguimiento de Producción**: La fabricación se planifica y monitorea dividiendo los ítems de la orden de compra en `Production_Lots`.

14. **Gestión de Transporte**: El movimiento de la mercancía se gestiona a través de `Shipments` (embarques internacionales) y `Local_Distributions` (entregas de última milla).

### Fase 5: Cierre y Análisis

15. **Cierre de Ciclo**: Se monitorean los estados para asegurar que todos los productos han sido entregados y los costos reales han sido consolidados, permitiendo el cierre administrativo del proyecto y el análisis de rentabilidad.
