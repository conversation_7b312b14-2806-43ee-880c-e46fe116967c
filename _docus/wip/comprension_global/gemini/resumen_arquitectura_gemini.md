# Resumen de la Arquitectura del Sistema

La arquitectura del sistema se divide en dos componentes principales que trabajan juntos: un motor de **definición de productos** y un motor de **ciclo de vida comercial y logístico**.

## 1. Motor de Definición de Productos (El "Qué")

-   **Núcleo Flexible**: El corazón es un sistema de plantillas dinámicas. En lugar de tener tablas rígidas para cada tipo de producto, existe un **catálogo global de atributos** (ej: color, material, tamaño).
-   **Plantillas y Variantes**: Los administradores crean **Plantillas de Producto** (ej: "Gorra Bordada") y les asignan atributos de ese catálogo. Para cada atributo, definen reglas específicas como el tipo de campo en la interfaz (texto, selector, etc.), si es obligatorio o las reglas de validación.
-   **Instancias con JSONB**: Un producto real, o **Variante de Producto** (ej: "Gorra de algodón, 6 paneles, color azul"), almacena sus especificaciones concretas en una única columna de base de datos de tipo `JSONB`. Esto proporciona máxima flexibilidad para añadir o cambiar productos sin alterar la estructura de la base de datos.
-   **UI Dinámica**: La interfaz de administración (usando Filament) lee la configuración de la plantilla y construye dinámicamente los formularios para crear o editar productos, ofreciendo una experiencia de usuario a medida.

## 2. Motor Comercial y Logístico (El "Cómo")

-   **De la Necesidad a la Compra**: Una vez definida una "Variante de Producto", el sistema gestiona su ciclo de compra. Esto comienza con un **Lote de Abastecimiento** (`procurement_lot`), que representa la necesidad de comprar una cantidad específica de esa variante.
-   **Negociación y Consolidación**: Se gestionan las solicitudes de cotización (RFQ) y las ofertas de los proveedores (`supplier_quotes`). El sistema permite agrupar la demanda de múltiples proyectos en **Grupos de Consolidación** para negociar mejores precios por volumen, registrando los acuerdos en tablas de precios (`pricing_agreements`).
-   **Trazabilidad End-to-End**: Una vez adjudicada una cotización, se genera una **Orden de Compra** (`purchase_order`). A partir de ahí, el sistema rastrea la producción en **Lotes de Producción** y el transporte en **Embarques** (`shipments`), proporcionando visibilidad completa desde la fábrica hasta el almacén.
-   **Costeo Detallado**: El modelo incluye una entidad de **Costos Adicionales** (`cost_items`) que permite registrar y prorratear gastos como fletes, moldes o aranceles, asociándolos a nivel de proyecto, orden de compra o producto para obtener un costo unitario final y preciso.

## Conclusión

En resumen, es una arquitectura de dos capas que separa la **definición flexible del producto** de la **gestión estructurada de la cadena de suministro**. Esto la hace muy potente, escalable y trazable, combinando la flexibilidad del `JSONB` para el catálogo con la robustez de las tablas relacionales para los procesos transaccionales.
