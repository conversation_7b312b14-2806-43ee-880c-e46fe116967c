# Listado de Entidades del Sistema

A continuación se presenta un listado de las entidades principales del sistema, agrupadas por su función lógica.

### 1. Configuración del Catálogo y Productos
-   `attributes`: Catálogo global de atributos reutilizables para todos los productos (ej: color, material).
-   `attribute_groups`: Agrupaciones lógicas de atributos para la UI (ej: "Información Básica", "Dimensiones").
-   `product_categories`: Categorías principales de productos (ej: "Textiles", "Promocionales").
-   `product_subcategories`: Subcategorías dentro de una categoría principal (ej: "Gorras", "Camisetas").
-   `product_templates`: Plantillas que definen la estructura, atributos y reglas de un tipo de producto.
-   `product_template_attributes`: Tabla pivote que conecta plantillas con atributos, definiendo la configuración específica (UI, validación, etc.).
-   `template_versions`: Registra las versiones históricas de una plantilla para mantener la trazabilidad.
-   `template_version_attributes`: Pivote que guarda la configuración de atributos para una versión específica de una plantilla.
-   `product_variants`: Una instancia específica y configurable de un producto, con sus valores concretos (ej: camiseta roja, talla M).

### 2. Contexto Comercial y de Proyectos
-   `customers`: Clientes que solicitan los proyectos.
-   `projects`: Proyectos comerciales para un cliente, que agrupan productos y servicios.
-   `product_projects`: Contenedor que representa un producto consolidado dentro de un proyecto específico.

### 3. Gestión de Proveedores
-   `suppliers`: Catálogo maestro de proveedores.

### 4. Abastecimiento y Negociación (Por Variante)
-   `variant_procurements` / `procurement_lots`: Representa la necesidad de compra para una variante de producto específica. Es la unidad central para la negociación.
-   `rfqs`: Solicitudes de Cotización (Request for Quote) enviadas a proveedores para un lote de abastecimiento.
-   `rfq_suppliers`: Registra qué proveedores fueron invitados a una RFQ.
-   `supplier_quotes`: Las cotizaciones (ofertas) que envía cada proveedor en respuesta a una RFQ.
-   `purchase_conditions`: Almacena los términos y condiciones finales acordados con un proveedor para una compra, creando un "snapshot" del acuerdo.

### 5. Consolidación y Acuerdos de Precios (Multi-Proyecto)
-   `procurement_groups`: Agrupa la demanda de múltiples proyectos para negociar precios por volumen con un proveedor.
-   `procurement_group_items`: Detalla los lotes de abastecimiento que participan en un grupo de consolidación.
-   `group_supplier_quotes`: Gestiona las cotizaciones con tramos por volumen recibidas para un grupo de consolidación.
-   `group_supplier_quote_tiers`: Define los tramos de precios dentro de una cotización de grupo.
-   `pricing_agreements`: Contiene los acuerdos de precios por volumen (tramos) ya adjudicados a un proveedor.
-   `pricing_agreement_tiers`: Define los diferentes tramos de precios (ej: 1-100 uds, 101-500 uds) de un acuerdo formal.

### 6. Órdenes de Compra
-   `purchase_orders`: La Orden de Compra formal emitida a un proveedor, que puede consolidar varios productos.
-   `purchase_order_items`: Cada línea de la orden de compra, vinculada a una variante de producto y su precio acordado.

### 7. Producción, Logística y Trazabilidad
-   `production_lots`: Sub-lotes de producción para planificar y rastrear la fabricación de un ítem de la orden de compra.
-   `shipments`: Representa un embarque o envío (aéreo, marítimo), con su información de tracking.
-   `shipment_items`: Vincula los lotes de producción con un embarque específico.
-   `local_distributions`: Gestiona la entrega de última milla desde el almacén local al destino final.
-   `local_distribution_items`: Detalla los productos incluidos en una entrega local.

### 8. Costos y Finanzas
-   `cost_obligations` / `cost_items`: Registra todos los costos asociados (FOB, fletes, moldes, etc.) y a qué nivel aplican (proyecto, OC, producto).
-   `payments`: Registra los pagos realizados.
-   `payment_allocations`: Asigna los pagos a las diferentes obligaciones de costo.
-   `exception_costs`: Registra costos no planificados o pérdidas.