# Plan de Implementación - Modelo Codex: Consolidación comercial con variantes configurables

## ETAPA 1: Migraciones, Modelos y Enums

### 1.1 Entidades Base del Sistema

#### 1.1.1 Customers
- [ ] Crear migración para tabla `customers`
- [ ] Crear modelo `Customer` con relaciones y casts
- [ ] Crear enum `CustomerStatus`

#### 1.1.2 Projects
- [ ] Crear migración para tabla `projects`
- [ ] Crear modelo `Project` con relaciones y casts
- [ ] Crear enum `ProjectStatus`

#### 1.1.3 Suppliers
- [ ] Crear migración para tabla `suppliers`
- [ ] Crear modelo `Supplier` con casts JSONB para contact_info

#### 1.1.4 Product Categories
- [ ] Crear migración para tabla `product_categories`
- [ ] Crear modelo `ProductCategory` con relaciones
- [ ] Crear enum `ProductCategoryStatus`

#### 1.1.5 Product Subcategories
- [ ] Crear migración para tabla `product_subcategories`
- [ ] Crear modelo `ProductSubcategory` con relaciones
- [ ] Crear enum `ProductSubcategoryStatus`

#### 1.1.6 Product Templates
- [ ] Crear migración para tabla `product_templates`
- [ ] Crear modelo `ProductTemplate` con casts JSONB para metadata
- [ ] Crear enum `ProductTemplateStatus`

### 1.2 Entidades de Productos y Proyectos

#### 1.2.1 Product Projects
- [ ] Crear migración para tabla `product_projects`
- [ ] Crear modelo `ProductProject` con relaciones y enums de estado
- [ ] Crear enum `ProductProjectStatus`

#### 1.2.2 Product Variants
- [ ] Crear migración para tabla `product_variants` con índice GIN
- [ ] Crear modelo `ProductVariant` con casts JSONB para configuration
- [ ] Crear enum `ProductVariantStatus` (para campo `variant_status`)

### 1.3 Entidades de Abastecimiento

#### 1.3.1 Procurement Lots
- [ ] Crear migración para tabla `procurement_lots` con índices únicos
- [ ] Crear modelo `ProcurementLot` con relaciones y enums
- [ ] Crear enum `ProcurementLotStatus`

#### 1.3.2 Procurement Groups
- [ ] Crear migración para tabla `procurement_groups`
- [ ] Crear modelo `ProcurementGroup` con relaciones
- [ ] Crear enum `ProcurementGroupStatus`

#### 1.3.3 Procurement Group Items
- [ ] Crear migración para tabla `procurement_group_items`
- [ ] Crear modelo `ProcurementGroupItem` con relaciones

### 1.4 Entidades de Cotizaciones y Precios

#### 1.4.1 Supplier Quotes
- [ ] Crear migración para tabla `supplier_quotes` con índices únicos
- [ ] Crear modelo `SupplierQuote` con casts JSONB para attachments
- [ ] Crear enum `SupplierQuoteStatus`

#### 1.4.2 Group Supplier Quotes
- [ ] Crear migración para tabla `group_supplier_quotes`
- [ ] Crear modelo `GroupSupplierQuote` con relaciones
- [ ] Crear enum `GroupSupplierQuoteStatus`

#### 1.4.3 Group Supplier Quote Tiers
- [ ] Crear migración para tabla `group_supplier_quote_tiers`
- [ ] Crear modelo `GroupSupplierQuoteTier` con relaciones

#### 1.4.4 Pricing Agreements
- [ ] Crear migración para tabla `pricing_agreements`
- [ ] Crear modelo `PricingAgreement` con relaciones
- [ ] Crear enum `PricingAgreementStatus`

#### 1.4.5 Pricing Agreement Tiers
- [ ] Crear migración para tabla `pricing_agreement_tiers`
- [ ] Crear modelo `PricingAgreementTier` con relaciones

### 1.5 Entidades de Órdenes de Compra

#### 1.5.1 Purchase Orders
- [ ] Crear migración para tabla `purchase_orders`
- [ ] Crear modelo `PurchaseOrder` con relaciones y enums
- [ ] Crear enum `PurchaseOrderStatus`

#### 1.5.2 Purchase Order Items
- [ ] Crear migración para tabla `purchase_order_items`
- [ ] Crear modelo `PurchaseOrderItem` con relaciones

### 1.6 Entidades de Producción

#### 1.6.1 Production Lots
- [ ] Crear migración para tabla `production_lots`
- [ ] Crear modelo `ProductionLot` con relaciones y enums
- [ ] Crear enum `ProductionLotStatus`

### 1.7 Entidades de Logística Internacional

#### 1.7.1 Shipments
- [ ] Crear migración para tabla `shipments` con casts JSON para docs
- [ ] Crear modelo `Shipment` con relaciones y enums
- [ ] Crear enum `ShipmentStatus`

#### 1.7.2 Shipment Items
- [ ] Crear migración para tabla `shipment_items`
- [ ] Crear modelo `ShipmentItem` con relaciones

### 1.8 Entidades de Distribución Local

#### 1.8.1 Local Distributions
- [ ] Crear migración para tabla `local_distributions` con casts JSON para docs
- [ ] Crear modelo `LocalDistribution` con relaciones y enums
- [ ] Crear enum `LocalDistributionStatus`

#### 1.8.2 Local Distribution Items
- [ ] Crear migración para tabla `local_distribution_items`
- [ ] Crear modelo `LocalDistributionItem` con relaciones

### 1.9 Entidades de Costos

#### 1.9.1 Cost Items
- [ ] Crear migración para tabla `cost_items` con relación polimórfica
- [ ] Crear modelo `CostItem` con relación polimórfica y enums

## ETAPA 2: Factories

### 2.1 Factories para Entidades Base
- [ ] Crear factory para `Customer`
- [ ] Crear factory para `Project`
- [ ] Crear factory para `Supplier`
- [ ] Crear factory para `ProductCategory`
- [ ] Crear factory para `ProductSubcategory`
- [ ] Crear factory para `ProductTemplate`

### 2.2 Factories para Entidades de Productos
- [ ] Crear factory para `ProductProject`
- [ ] Crear factory para `ProductVariant`

### 2.3 Factories para Entidades de Abastecimiento
- [ ] Crear factory para `ProcurementLot`
- [ ] Crear factory para `ProcurementGroup`
- [ ] Crear factory para `ProcurementGroupItem`

### 2.4 Factories para Entidades de Cotizaciones
- [ ] Crear factory para `SupplierQuote`
- [ ] Crear factory para `GroupSupplierQuote`
- [ ] Crear factory para `GroupSupplierQuoteTier`
- [ ] Crear factory para `PricingAgreement`
- [ ] Crear factory para `PricingAgreementTier`

### 2.5 Factories para Entidades de Órdenes de Compra
- [ ] Crear factory para `PurchaseOrder`
- [ ] Crear factory para `PurchaseOrderItem`

### 2.6 Factories para Entidades de Producción
- [ ] Crear factory para `ProductionLot`

### 2.7 Factories para Entidades de Logística
- [ ] Crear factory para `Shipment`
- [ ] Crear factory para `ShipmentItem`
- [ ] Crear factory para `LocalDistribution`
- [ ] Crear factory para `LocalDistributionItem`

### 2.8 Factories para Entidades de Costos
- [ ] Crear factory para `CostItem`

## ETAPA 3: Filament Resources (CRUD Básico)

### 3.1 Resources para Entidades Base
- [ ] Crear Filament Resource para `Customer`
- [ ] Crear Filament Resource para `Project`
- [ ] Crear Filament Resource para `Supplier`
- [ ] Crear Filament Resource para `ProductCategory`
- [ ] Crear Filament Resource para `ProductSubcategory`
- [ ] Crear Filament Resource para `ProductTemplate`

### 3.2 Resources para Entidades de Productos
- [ ] Crear Filament Resource para `ProductProject`
- [ ] Crear Filament Resource para `ProductVariant`

### 3.3 Resources para Entidades de Abastecimiento
- [ ] Crear Filament Resource para `ProcurementLot`
- [ ] Crear Filament Resource para `ProcurementGroup`
- [ ] Crear Filament Resource para `ProcurementGroupItem`

### 3.4 Resources para Entidades de Cotizaciones
- [ ] Crear Filament Resource para `SupplierQuote`
- [ ] Crear Filament Resource para `GroupSupplierQuote`
- [ ] Crear Filament Resource para `GroupSupplierQuoteTier`
- [ ] Crear Filament Resource para `PricingAgreement`
- [ ] Crear Filament Resource para `PricingAgreementTier`

### 3.5 Resources para Entidades de Órdenes de Compra
- [ ] Crear Filament Resource para `PurchaseOrder`
- [ ] Crear Filament Resource para `PurchaseOrderItem`

### 3.6 Resources para Entidades de Producción
- [ ] Crear Filament Resource para `ProductionLot`

### 3.7 Resources para Entidades de Logística
- [ ] Crear Filament Resource para `Shipment`
- [ ] Crear Filament Resource para `ShipmentItem`
- [ ] Crear Filament Resource para `LocalDistribution`
- [ ] Crear Filament Resource para `LocalDistributionItem`

### 3.8 Resources para Entidades de Costos
- [ ] Crear Filament Resource para `CostItem`

---

## NOTA: Funcionalidades Futuras

Las siguientes funcionalidades se implementarán en etapas posteriores:
- Validaciones de negocio complejas
- State machines para transiciones de estado
- Servicios de cálculo y generación automática
- Tests completos
- Reportes y analytics
- Optimizaciones de performance
- Documentación técnica y de usuario
