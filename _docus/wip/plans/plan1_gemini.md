# Plan de Implementación Definitivo (v4)

**Objetivo:** Crear la estructura completa y robusta de la base de datos para soportar todo el ciclo de vida del negocio, detallando explícitamente cada campo, relación y estrategia de integridad, adhiriéndose a las convenciones del proyecto.

---

### Estrategia de Borrado Lógico (Soft Deletes)

Se aplicará `softDeletes()` a las entidades maestras para preservar el histórico. En tablas transaccionales o de detalle, se usará `cascadeOnDelete()` o `restrictOnDelete()` para mantener la integridad referencial.

- **Con SoftDeletes:** `Customers`, `Suppliers`, `Projects`, `ProductTemplates`, `ProductVariants`, `PurchaseOrders`.
- **Sin SoftDeletes:** Todas las demás tablas (pivotes, ítems, etc.).

---

### Roadmap de Implementación

#### 1. Creación de Enums de Dominio
**Acción:** Crear los siguientes Enums en `app/Enums/` usando `php artisan make:enum EnumName`.
**Convención:** Los nombres de los casos deben ser `TitleCase` (ej: `case Draft = 'draft';`).

1.  **`AttributeType.php`**: Define los tipos de atributos (`String`, `Integer`, `Json`, `Text`, etc.).
2.  **`TemplateStatus.php`**: Define los estados de una plantilla (`Draft`, `Active`, `Obsolete`).
3.  **`ProjectStatus.php`**: Define los estados de un proyecto (`Draft`, `Quoted`, `Approved`, `InProgress`, `Completed`).
4.  **`VariantStatus.php`**: Define los estados de una variante (`Draft`, `Finalized`, `Obsolete`).
- `CommonStatus`, `TemplateStatus`, `ProjectStatus`, `ProductProjectStatus`, `ProcurementLotStatus`, `SupplierQuoteStatus`, `PurchaseOrderStatus`, `PricingAgreementStatus`, `AttributeType`.

#### 2. Migraciones de Catálogo y Plantillas

- **`create_product_categories_table`**: `id`, `name`, `description`, `status` (usa `CommonStatus`), `timestamps`.
- **`create_product_subcategories_table`**: `id`, `name`, `description`, `status`, `foreignId('product_category_id')->constrained()`.
- **`create_attributes_table`**: `id`, `key` (unique), `label_es`, `type` (usa `AttributeType`), `options_json` (jsonb, nullable).
- **`create_attribute_groups_table`**: `id`, `key` (unique), `name_es`.
- **`create_product_templates_table`**: `id`, `name`, `description`, `status` (usa `TemplateStatus`), `foreignId('product_subcategory_id')`, `softDeletes`, `timestamps`.
- **`create_product_template_attributes_table`**: `id`, `foreignId` para `product_template`, `attribute`, `attribute_group`. Campos: `is_required`, `position`, `column_span`, `section`, `ui_component`, `placeholder`, `help_text`, `default_value` (jsonb), `ui_props` (jsonb), `validation_rules` (jsonb).

#### 3. Migraciones de Entidades Comerciales

- **`create_customers_table`**: `id`, `name`, `tax_id`, `contact_info` (jsonb), `status`, `softDeletes`, `timestamps`.
- **`create_projects_table`**: `id`, `name`, `status` (usa `ProjectStatus`), `foreignId('customer_id')`, `softDeletes`, `timestamps`.
- **`create_product_projects_table`**: `id`, `name`, `foreignId` para `project` y `template`. Campos: `status` (usa `ProductProjectStatus`), `total_quantity`, `avg_unit_price` (decimal).
- **`create_product_variants_table`**: `id`, `name`, `quantity`, `status`, `generated_sku` (unique), `foreignId('product_project_id')`, `jsonb('configuration')`, `softDeletes`, `timestamps`. **Nota:** Índice GIN en `configuration`.

#### 4. Migraciones de Abastecimiento y Consolidación

- **`create_suppliers_table`**: `id`, `name`, `tax_id`, `contact_info` (jsonb), `status`, `softDeletes`, `timestamps`.
- **`create_procurement_lots_table`**: `id`, `foreignId('product_variant_id')`, `foreignId('awarded_quote_id')` (nullable). Campos: `quantity`, `status` (usa `ProcurementLotStatus`), `agreed_unit_price` (decimal), `incoterm`, `lead_time_days`.
- **`create_supplier_quotes_table`**: `id`, `foreignId` para `procurement_lot` y `supplier`. Campos: `status` (usa `SupplierQuoteStatus`), `unit_price`, `lead_time_days`, `valid_until`, `notes` (text).
- **`create_procurement_groups_table`**: `id`, `name`, `foreignId('supplier_id')`, `status`, `currency`, `valid_from`, `valid_to`.
- **`create_procurement_group_items_table`**: `id`, `foreignId` para `procurement_group` y `procurement_lot`, `requested_quantity`.
- **`create_pricing_agreements_table`**: `id`, `foreignId` para `procurement_group` y `supplier`, `status` (usa `PricingAgreementStatus`), `valid_from`, `valid_to`.
- **`create_pricing_agreement_tiers_table`**: `id`, `foreignId('pricing_agreement_id')`, `min_qty`, `max_qty`, `unit_price`.

#### 5. Migraciones de Órdenes, Logística y Finanzas

- **`create_purchase_orders_table`**: `id`, `foreignId` para `supplier` y `project`. Campos: `status` (usa `PurchaseOrderStatus`), `currency`, `issued_at`, `softDeletes`.
- **`create_purchase_order_items_table`**: `id`, `foreignId` para `purchase_order` y `procurement_lot`. Campos: `quantity`, `unit_price`.
- **`create_cost_items_table`**: `id`, `level` (enum), `reference_id` (polimórfico o `bigint`), `type`, `amount`, `currency`.
- **`create_payments_table`**: `id`, `paid_at`, `method`, `reference`, `amount`, `currency`.
- **`create_payment_allocations_table`**: `foreignId` para `payment` y `cost_item`, `amount`.

#### 6. Creación de Modelos y Factories

- **Acción:** Crear modelos y factories para **todas** las tablas, aplicando `casts()`, return types explícitos y la estrategia de `SoftDeletes`.

- **`Customer`**: `hasMany(Project)`.
- **`Project`**: `belongsTo(Customer)`, `hasMany(ProductProject)`.
- **`ProductTemplate`**: `belongsTo(ProductSubcategory)`, `hasMany(ProductProject)`, `belongsToMany(Attribute)`.
- **`ProductProject`**: `belongsTo(Project)`, `belongsTo(ProductTemplate)`, `hasMany(ProductVariant)`.
- **`ProductVariant`**: `belongsTo(ProductProject)`, `hasMany(ProcurementLot)`.
- **`Supplier`**: `hasMany(SupplierQuote)`, `hasMany(PurchaseOrder)`, `hasMany(ProcurementGroup)`.
- **`ProcurementLot`**: `belongsTo(ProductVariant)`, `hasMany(SupplierQuote)`, `hasOne(PurchaseOrderItem)`.
- **`ProcurementGroup`**: `belongsTo(Supplier)`, `hasMany(PricingAgreement)`, `belongsToMany(ProcurementLot)`.
- **`PricingAgreement`**: `belongsTo(ProcurementGroup)`, `hasMany(PricingAgreementTier)`.
- **`PurchaseOrder`**: `belongsTo(Supplier)`, `belongsTo(Project)`, `hasMany(PurchaseOrderItem)`.
- **`PurchaseOrderItem`**: `belongsTo(PurchaseOrder)`, `belongsTo(ProcurementLot)`.
- **`Payment`**: `belongsToMany(CostItem)`.
- **`CostItem`**: `belongsToMany(Payment)`.

#### 7. Creación de Seeders

- **`CatalogSeeder`**: Poblar `ProductCategory`, `Subcategory`, `Attribute`, `Group`, `Template`.
- **`CustomerSupplierSeeder`**: Poblar `Customer` y `Supplier`.
- **`ProjectSeeder`**: Crear los 3 proyectos, sus `ProductProject`, `ProductVariant` y `ProcurementLot` en estado `draft`.