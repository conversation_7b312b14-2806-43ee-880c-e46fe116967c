# Plan de implementación Fase 1 (Seeds) – PostgreSQL 17

## Alcance
- Catálogo: categorías, subcategorías, plantillas, atributos, grupos, pivot.
- Clientes/Proyectos: customers, projects, product_projects.
- Variantes: product_variants (config JSONB + SKU).
- Proveedores: suppliers.
- Abastecimiento mínimo (Fase 1.5): procurement_lots (draft) y supplier_quotes preparado.
- Fuera de alcance ahora: RFQs activos, OCs, logística y costos (Fase 2).

## Enums (PHP)
- CommonStatus: active | inactive.
- ProductTemplateStatus: draft | active | obsolete.
- ProductProjectStatus: draft | quoted | approved | po_issued | in_production | received | closed.
- ProcurementLotStatus: draft | rfq_open | quoted | awarded | closed.
- ProcurementGroupStatus: draft | rfq_sent | quoted | awarded | closed.
- SupplierQuoteStatus: draft | sent | received | shortlisted | awarded | rejected | expired.
- PurchaseOrderStatus: draft | issued | in_production | partially_received | received | closed | cancelled.
- CostLevel: group | po | project | lot.

## Migraciones (PostgreSQL 17)
- product_categories: id, name (unique), description, status (CHECK), timestamps.
- product_subcategories: id, product_category_id FK(restrict), name (unique por categoría), description, status (CHECK), timestamps.
- product_templates: id, product_subcategory_id FK(restrict), name (unique por subcategoría), status (CHECK), description JSONB (nullable), metadata JSONB (nullable), timestamps.
- attributes: id, key (unique), type (CHECK: string, integer, decimal, boolean, enum, text, date, email, url), options_json JSONB (nullable), label_es, label_en, active (boolean), timestamps.
- attribute_groups: id, key, name_es, name_en, description_es, description_en, icon (nullable), position (int), active (boolean), timestamps.
- product_template_attributes: id, product_template_id FK(restrict), attribute_id FK(restrict), attribute_group_id FK(restrict), is_required (boolean), visible (boolean), position (int), ui_component (string), ui_props JSONB (nullable), default_value JSONB (nullable), validation_rules JSONB (nullable), placeholder (nullable), column_span (int nullable), timestamps.
  - UNIQUE(product_template_id, attribute_id)
  - Índice parcial UNIQUE (product_template_id, attribute_group_id, position) WHERE visible = true
- template_versions (opcional): id, product_template_id FK(restrict), number (int), published_at (nullable), active (boolean), timestamps.
  - UNIQUE(product_template_id, number)
- template_version_attributes (opcional): template_version_id FK, attribute_id FK, attribute_group_id FK, misma config que pivot; PK compuesta (template_version_id, attribute_id).
- customers: id, name (unique), tax_id (nullable, unique), contact_info JSONB (nullable), status (CHECK), timestamps.
- suppliers: id, name (unique), tax_id (nullable, unique), contact_info JSONB (nullable), status (CHECK), timestamps.
- projects: id, customer_id FK(restrict), name, status (CHECK), starts_at (date), ends_at (nullable, date), currency CHAR(3) (CHECK length=3), timestamps.
- product_projects: id, project_id FK(restrict), product_template_id FK(restrict), supplier_id FK(nullable, restrict), total_quantity (int, CHECK >=0), avg_unit_price (nullable, numeric(12,2)), currency CHAR(3) (CHECK length=3), status (CHECK), notes (nullable, text), timestamps.
- product_variants: id, product_project_id FK(restrict), name (nullable), quantity (int, CHECK >0), configuration JSONB, generated_sku (unique), estimated_unit_price (nullable, numeric(12,2)), suggested_supplier_id FK(nullable, restrict), delivery_window_start (nullable, date), delivery_window_end (nullable, date), variant_status (CHECK), timestamps.
  - Índice GIN en configuration
  - CHECK delivery_window_start <= delivery_window_end
- procurement_lots (Fase 1.5): id, product_project_id FK(restrict), product_variant_id FK(not null), supplier_id FK(nullable, restrict), awarded_quote_id FK(nullable, restrict), awarded_at (nullable, timestamp), quantity (int, CHECK >0), agreed_unit_price (nullable, numeric(12,2)), incoterm (nullable, string), lead_time_days (nullable, int), currency (nullable, CHAR(3)), terms (nullable, text), status (CHECK), timestamps.
  - UNIQUE parcial (product_variant_id) WHERE status IN ('draft','rfq_open','quoted')
- supplier_quotes (preparado): id, procurement_lot_id FK(restrict), supplier_id FK(restrict), status (CHECK), unit_price (numeric(12,2)), currency (CHAR(3)), incoterm (nullable, string), lead_time_days (nullable, int), valid_until (nullable, date), attachments JSONB (nullable), notes (nullable, text), timestamps.
  - UNIQUE parcial (procurement_lot_id, supplier_id) WHERE status IN ('draft','sent','received','shortlisted')

## Modelos (relaciones + casts)
- ProductCategory: hasMany ProductSubcategory.
- ProductSubcategory: belongsTo ProductCategory; hasMany ProductTemplate.
- ProductTemplate: belongsTo ProductSubcategory; hasMany ProductProject; hasMany ProductTemplateAttribute; hasMany TemplateVersion (opcional).
- Attribute: hasMany ProductTemplateAttribute.
- AttributeGroup: hasMany ProductTemplateAttribute.
- ProductTemplateAttribute: belongsTo ProductTemplate, Attribute, AttributeGroup.
- TemplateVersion (opcional): belongsTo ProductTemplate; hasMany TemplateVersionAttribute.
- TemplateVersionAttribute (opcional): belongsTo TemplateVersion, Attribute, AttributeGroup.
- Customer: hasMany Project.
- Supplier: hasMany ProductProject; hasMany ProductVariant (sugerido).
- Project: belongsTo Customer; hasMany ProductProject.
- ProductProject: belongsTo Project, ProductTemplate, Supplier; hasMany ProductVariant; hasMany ProcurementLot.
- ProductVariant: belongsTo ProductProject; belongsTo Supplier (sugerido); hasOne ProcurementLot.
- ProcurementLot: belongsTo ProductProject, ProductVariant, Supplier; hasMany SupplierQuote; belongsTo SupplierQuote (awarded).
- SupplierQuote: belongsTo ProcurementLot, Supplier.

casts():
- JSONB → array: options_json, ui_props, default_value, validation_rules, description, metadata, contact_info, configuration, attachments.
- status/variant_status → enums PHP.
- fechas → immutable_datetime/date según campo.
- precios → decimal:2.

## Factories
- ProductCategoryFactory, ProductSubcategoryFactory, ProductTemplateFactory, AttributeFactory, AttributeGroupFactory, ProductTemplateAttributeFactory.
- CustomerFactory, ProjectFactory.
- SupplierFactory.
- ProductProjectFactory.
- ProductVariantFactory.
- ProcurementLotFactory (estado draft).
- Notas: generar SKUs únicos; configuration faker coherente; respetar FKs encadenadas.

## Seeders (alineados a Gemini)
- Catálogo: crear categorías, subcategorías, plantillas listadas en `listado_seed_catalogo_gemini.md` (atributos mínimos: color, talla, material, técnicas de marcaje, etc. con options cuando aplique).
- Clientes y Proveedores: desde `listado_seed_clientes_gemini.md` y `listado_seed_proveedores_gemini.md`.
- Proyectos y Demandas: crear 3 proyectos y sus `product_projects`; instanciar `product_variants` con `configuration` según descripciones de `listado_seed_proyecto*.md`.
- Lotes de abastecimiento: `procurement_lots` uno por variante (estado `draft`); sin RFQ aún.

## Roadmap de Entregas
1. Crear Enums (PHP + checks en DB).
2. Migraciones de catálogo (categorías, subcategorías, atributos, grupos, plantillas, pivot, versiones opcionales).
3. Migraciones comerciales (customers, projects, product_projects, product_variants + índices).
4. Migraciones de proveedores/abastecimiento (suppliers, procurement_lots, supplier_quotes + reglas parciales).
5. Modelos Eloquent (con casts y relaciones tipadas).
6. Factories encadenadas.
7. Seeders mínimos para los listados Gemini.

## Consideraciones PostgreSQL 17
- Usar índices GIN en JSONB (`configuration`, `options_json`, etc.).
- Índices parciales (UNIQUE WHERE) para lotes abiertos y cotizaciones activas.
- CHECK constraints para enums y validaciones de negocio.
- FKs con `restrictOnDelete()` crítico en catálogo/relaciones clave y `cascadeOnUpdate()` en todas.
- Timestamps con zona horaria por defecto.

## Convenciones
- Clases `StudlyCase`, métodos/vars `camelCase`, constants `UPPER_SNAKE_CASE`.
- Migrations: timestamps obligatorios; nombres de tablas en plural snake_case.
- Factories: respetar unicidades y generar datos coherentes.
- Testing: Pest + RefreshDatabase; enfocarse en lógica de negocio, no en framework.