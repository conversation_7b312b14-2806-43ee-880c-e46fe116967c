# Plan de Implementación: Modelos, Migraciones y Enums (Soporte para Seeds Gemini)

Objetivo: preparar el modelo de datos mínimo (pero correcto) para representar y poblar los seeds definidos en `_docus/wip/comprension_global/gemini/seed`, alineado con la arquitectura Codex y las convenciones del proyecto.

## 1) Enums de Dominio

- ProductStatus: draft, active, obsolete
- CommonStatus: active, inactive
- ProjectStatus: draft, active, closed
- ProductProjectStatus: draft, quoted, approved, po_issued, in_production, received, closed
- ProcurementLotStatus: draft, rfq_open, quoted, awarded, closed
- ProcurementGroupStatus: draft, rfq_sent, quoted, awarded, closed
- SupplierQuoteStatus: draft, sent, received, shortlisted, awarded, rejected, expired
- PricingAgreementStatus: active, expired
- PurchaseOrderStatus: draft, issued, in_production, partially_received, received, closed, cancelled
- CostLevel: group, po, project, lot

Usos sugeridos
- product_templates.status → ProductStatus
- product_categories.status, product_subcategories.status, suppliers.status, customers.status → CommonStatus
- product_projects.status → ProductProjectStatus
- procurement_lots.status → ProcurementLotStatus
- procurement_groups.status → ProcurementGroupStatus
- supplier_quotes.status → SupplierQuoteStatus
- purchase_orders.status → PurchaseOrderStatus

Notas:
- Implementar como PHP Enums (Backed Enum `string`).
- Validar con `CHECK` en migraciones (VARCHAR + CHECK) para robustez a nivel DB.

## 2) Esquema de Catálogo y Plantillas

Tablas
- product_categories (id, name, description, status, timestamps)
- product_subcategories (id, product_category_id, name, description, status, timestamps)
- attributes (id, key UNIQUE, type, options_json json nullable, label_es, label_en, active boolean, timestamps)
- attribute_groups (id, key, name_es, name_en, description_es, description_en, icon nullable, position int, active boolean, timestamps)
- product_templates (id, name, product_subcategory_id FK, status, description json nullable, metadata json nullable, timestamps)
- product_template_attributes (id, product_template_id FK, attribute_id FK, attribute_group_id FK, is_required bool, visible bool, position int, ui_component string, ui_props json, default_value json, validation_rules json, placeholder string nullable, column_span int nullable, timestamps)
- template_versions (id, product_template_id FK, number int, published_at timestamp nullable, active bool, timestamps) [opcional]
- template_version_attributes (template_version_id FK, attribute_id FK, attribute_group_id FK, ... misma config que pivot; PK compuesta) [opcional]

Índices y reglas
- product_template_attributes: UNIQUE(product_template_id, attribute_id)
- product_template_attributes: índice (product_template_id, attribute_group_id, position); índice parcial UNIQUE en (product_template_id, attribute_group_id, position) WHERE visible = true
- template_versions: UNIQUE(product_template_id, number)
- attributes: `type` restringido a: string, integer, decimal, boolean, enum, text, date, email, url
- Notas: índices parciales solo PostgreSQL; en SQLite omitir (enforce en aplicación).

## 3) Entidades Comerciales y Variantes

Tablas
- customers (id, name, tax_id nullable, contact_info json, status, timestamps)
- projects (id, customer_id FK, name, status, starts_at date, ends_at date nullable, currency string, timestamps)
- product_projects (id, project_id FK, product_template_id FK, supplier_id FK nullable, total_quantity int, avg_unit_price decimal(12,2) nullable, currency string, status, notes text nullable, timestamps)
- product_variants (id, product_project_id FK, name nullable, quantity int, configuration json, generated_sku string UNIQUE, estimated_unit_price decimal(12,2) nullable, suggested_supplier_id FK nullable, delivery_window_start date nullable, delivery_window_end date nullable, variant_status string nullable, timestamps)

Índices
- product_variants: índice GIN en configuration (PostgreSQL); en SQLite omitir

## 4) Abastecimiento y Cotizaciones

Tablas
- suppliers (id, name, tax_id nullable, contact_info json, status, timestamps)
- procurement_lots (id, product_project_id FK, product_variant_id FK not null, supplier_id FK nullable, awarded_quote_id FK nullable, awarded_at timestamp nullable, quantity int, agreed_unit_price decimal(12,2) nullable, incoterm string nullable, lead_time_days int nullable, currency string nullable, terms text nullable, status, timestamps)
- supplier_quotes (id, procurement_lot_id FK, supplier_id FK, status, unit_price decimal(12,2), currency string, incoterm string nullable, lead_time_days int nullable, valid_until date nullable, attachments json nullable, notes text nullable, timestamps)
- procurement_groups (id, name, supplier_id FK, currency string, status, valid_from date, valid_to date, notes text nullable, timestamps)
- procurement_group_items (id, procurement_group_id FK, procurement_lot_id FK, requested_quantity int, timestamps)
- group_supplier_quotes [opcional] (id, procurement_group_id FK, supplier_id FK, status, currency string, incoterm string nullable, valid_until date nullable, notes text nullable, timestamps)
- group_supplier_quote_tiers [opcional] (id, group_supplier_quote_id FK, min_qty int, max_qty int nullable, unit_price decimal(12,2), timestamps)
- pricing_agreements (id, procurement_group_id FK, supplier_id FK, status, valid_from date, valid_to date, notes text nullable, timestamps)
- pricing_agreement_tiers (id, pricing_agreement_id FK, min_qty int, max_qty int nullable, unit_price decimal(12,2), timestamps)

Reglas/Índices
- procurement_lots: UNIQUE parcial en (product_variant_id) WHERE status IN ('draft','rfq_open','quoted')
- supplier_quotes: UNIQUE parcial en (procurement_lot_id, supplier_id) WHERE status IN ('draft','sent','received','shortlisted')

## 5) Órdenes, Producción y Logística

Tablas
- purchase_orders (id, supplier_id FK, project_id FK nullable, status, currency, incoterm string nullable, issued_at timestamp, expected_at timestamp nullable, notes text nullable, timestamps)
- purchase_order_items (id, purchase_order_id FK, procurement_lot_id FK (o product_variant_id), quantity int, unit_price decimal(12,2), currency string, pricing_agreement_tier_id FK nullable, terms text nullable, incoterm string nullable, timestamps)
- production_lots (id, purchase_order_item_id FK, quantity int, status string, notes text nullable, timestamps)
- shipments (id, carrier string, reference string, shipped_at timestamp, arrived_at timestamp nullable, status string, incoterm string nullable, notes text nullable, timestamps)
- shipment_items (id, shipment_id FK, production_lot_id FK (o purchase_order_item_id), quantity int, timestamps)
- local_distributions (id, destination string, reference string, status string, notes text nullable, timestamps)
- local_distribution_items (id, local_distribution_id FK, production_lot_id FK (o purchase_order_item_id), quantity int, timestamps)

## 6) Costos

Tablas
- cost_items (id, level enum, reference_id bigint, type string, amount decimal(12,2), currency string, allocation_method string, notes text nullable, timestamps)

Notas
- `level` modelado como enum + CHECK; se puede migrar a relación polimórfica más adelante.

## 7) Modelos Eloquent y Casts

- Implementar `casts()` en lugar de $casts para columnas json: options_json, ui_props, default_value, validation_rules, configuration, attachments, description, metadata, contact_info.
- Soft deletes: evaluar según necesidad (no imprescindible para seeds iniciales).
- Relaciones con return types explícitos (BelongsTo, HasMany, etc.).

## 8) Migrations (convenciones y constraints)

- Todas con timestamps; FKs con `constrained()` y `cascadeOnUpdate()`; usar `restrictOnDelete()` donde indique el diseño.
- CHECKs para enums/estados y para valores no negativos en cantidades/precios.
- Índices: BTREE en FKs y unicidades especificadas; GIN para json (PostgreSQL). En SQLite: sin GIN y checks adaptados si fuese necesario.

## 9) Seeders (alineados a Gemini)

- Catálogo: crear categorías, subcategorías y plantillas listadas en `listado_seed_catalogo_gemini.md`. (Atributos mínimos: color, talla, material, técnicas de marcaje, etc. con options cuando aplique.)
- Clientes y Proveedores: desde `listado_seed_clientes_gemini.md` y `listado_seed_proveedores_gemini.md`.
- Proyectos y Demandas: crear 3 proyectos y sus `product_projects`; instanciar `product_variants` con `configuration` según descripciones de `listado_seed_proyecto*.md`.
- Lotes de abastecimiento: `procurement_lots` uno por variante (sin RFQ aún); estados `draft`.

## 10) Roadmap de Entregas

1. Crear Enums (PHP + checks en DB).
2. Migraciones de catálogo (categorías, subcategorías, atributos, grupos, plantillas, pivot, versiones opcionales).
3. Migraciones comerciales (customers, projects, product_projects, product_variants + índices).
4. Migraciones de proveedores/abastecimiento (suppliers, procurement_lots, supplier_quotes + reglas parciales).
5. Migraciones de consolidación y acuerdos (procurement_groups, items, pricing_agreements, tiers).
6. Migraciones de OC/producción/logística (POs, PO items, production_lots, shipments y distribuciones locales).
7. Migraciones de costos (cost_items).
8. Modelos Eloquent (con casts y relaciones tipadas).
9. Seeders mínimos para los listados Gemini.

## 11) Consideraciones de Filament (posterior)

- Resources para CRUD de catálogo y proyectos.
- Formularios dinámicos basados en `product_template_attributes`.

