# Especificación de Arquitectura Definitiva: Planificador de Proyectos PromoSmart

## 1. Arquitectura y Filosofía de Diseño

### 1.1. <PERSON>los<PERSON><PERSON> Principal: "Plan-to-Cost"

La arquitectura de este sistema se basa en el principio de **"Planificar para Costear" (Plan-to-Cost)**. El presupuesto no es una estimación inicial, sino el resultado financiero de un plan operativo completo. El sistema se centra en la planificación detallada, y el costo es la consecuencia de dicho plan.

### 1.2. Jerarquía Conceptual de Producto

El modelo de datos se articula sobre una jerarquía clara que descompone un requerimiento comercial en sus partes fabricables:

1.  **`Kit`**: Una entidad de catálogo que define una agrupación de uno o más `Productos`.
2.  **`Producto`**: La categoría general de un artículo (ej. "Toalla de Playa").
3.  **`Variante de Diseño`**: La especificación concreta y fabricable de un `Producto` (ej. "Toalla Azul, Bordada").
4.  **`Lote de Producción`**: Un grupo de unidades de una misma `Variante` que se fabrican juntas.

## 2. El Flujo de Planificación y sus Estructuras de Datos

### 2.1. Estructura del Catálogo y la Cotización

Para permitir la máxima flexibilidad, el sistema se basa en una estructura polimórfica que se simplifica para el usuario mediante el principio "Todo es un Kit".

#### 2.1.1. La Estructura del Catálogo (Las Entidades Maestras)

La jerarquía se define con total claridad:

*   **`Kit`**: Una entidad cuyo único propósito es agrupar `Productos`. Es la "caja" o el "paquete".
    *   **`kit_components` (Tabla de definición)**: Define qué `Productos` y en qué cantidad componen un `Kit`.
        *   *Ejemplo: El `Kit` "Kit Verano" se define aquí como 1 unidad del `Producto` "Toalla" + 1 unidad del `Producto` "Gorra".*

*   **`Producto`**: La categoría general del artículo.
    *   *Ejemplo: "Toalla", "Gorra".*

*   **`Variante de Diseño`**: La especificación fabricable de un `Producto`.
    *   *Ejemplo: El `Producto` "Gorra" tiene las `Variantes` "Gorra Azul" y "Gorra Roja".*

#### 2.1.2. La Estructura de la Cotización a Cliente (La Transacción)

Aquí aplicamos el principio de "Todo es un Kit" para la consistencia, pero con la jerarquía correcta.

1.  Un **`Item de Cotización`** siempre apunta a un `Kit`.

2.  Se necesita una nueva tabla para registrar las decisiones que se toman al cotizar: **`client_quote_item_configurations`** (Configuraciones del Item de Cotización).
    *   **Propósito**: Cuando un vendedor añade un Kit a una cotización, debe especificar qué `Variante` se usará para cada `Producto` dentro de ese Kit. Esta tabla guarda esas decisiones.
    *   **Columnas**: `quote_item_id`, `product_id`, `variant_id_seleccionada`.

### 2.2. El Flujo de Planificación en Tres Etapas

El flujo de trabajo es una secuencia de tres etapas de planificación. Al final de cada etapa, se pueden ejecutar procesos que generan los documentos transaccionales clave.

*   **Etapa 1: Planificación de Sourcing y Producción**: Se define el plan completo de abastecimiento y fabricación para satisfacer el requerimiento de la cotización. Al cerrar esta etapa, se ejecuta el proceso de **Generación de `Órdenes de Compra a Proveedor`**. El resultado de la etapa es un "pool" de `Lotes de Producción` planificados.

*   **Etapa 2: Planificación de Importación y Logística**: Se define el plan de transporte, consumiendo los lotes del "pool" para crear `Registros de Embarque`. Al cerrar esta etapa, los **`Registros de Embarque`** quedan formalizados.

*   **Etapa 3: Cálculo de Costos y Generación de la Cotización Final**: Con todos los planes cerrados, el sistema calcula el costo final y preciso. Al finalizar, se ejecuta el proceso de **Generación de la `Cotización a Cliente`** como documento final.

## 3. Diagrama de Arquitectura

```mermaid
graph TD
    subgraph "Etapa 1: Planificación de Sourcing"
        A(Item de Cotización) --> B{Proceso de Desglose<br/>(Kit/Producto a Variante)}
        B --> C(Rama de Sourcing)
        C --> D(Orden de Compra)
        D --> E(Item de OC)
        E --> F(Lote de Producción)
    end

    subgraph "Pool de Producción"
        F --"genera"--> POOL((Pool de Lotes Disponibles))
    end

    subgraph "Etapa 2: Planificación de Logística"
        POOL --"consume"--> G(Item de Embarque)
        G --> H(Registro de Embarque)
        G --> I(Lote de Distribución)
    end

    subgraph "Etapa 3: Costos y Cotización"
        J(Planes Cerrados) --> K{Cálculo de<br/>Costo Final} --> L(Cotización a Cliente<br/>*Documento Final*)
    end
```

## 4. Veredicto Final del Diseño

La arquitectura final, basada en una jerarquía conceptual clara (`Kit` → `Producto` → `Variante`) y un proceso de planificación por etapas bien definido, es excepcionalmente potente. El costo de esta potencia es una mayor complejidad, pero es un compromiso justificado por la precisión y el control que se obtienen.
