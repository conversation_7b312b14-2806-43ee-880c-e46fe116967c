# Sección 5: Mapeo con `taxonomia.md`

## Prop<PERSON>ito

Esta sección sirve como un "checklist" para garantizar que la especificación técnica detallada en los documentos anteriores cubre completamente los requerimientos de negocio y la estructura de información descrita en `_docs/plans/epica1/taxonomia.md`.

El documento `taxonomia.md` define el **"qué"** desde una perspectiva de negocio; esta especificación define el **"cómo"** se implementa en el sistema.

---

### Mapeo de Conceptos Clave

**Requerimiento en `taxonomia.md`:**
> "La taxonomía de productos proporciona una clasificación jerárquica... Consiste en: 1. Categorías de Productos, 2. Subcategorías de Productos..."

**Solución en este Modelo:**
*   Se implementa a través de las entidades `CategoriaDeProducto` y `SubcategoriaDeProducto`, que están relacionadas jerárquicamente. Esto proporciona la estructura de clasificación formal requerida.

---

**Requerimiento en `taxonomia.md`:**
> "...se define un conjunto de **Dimensiones de Especificación**... estas dimensiones... se agrupan en clasificaciones de nivel superior como: 'Información Básica del Producto', 'Atributos Centrales del Producto', etc."

**Solución en este Modelo:**
*   Se implementa a través de la entidad `GrupoDeAtributos`. Cada uno de los 7 grupos listados en `taxonomia.md` corresponde a un registro en esta entidad.
*   Cada `Atributo` en el sistema está asociado a uno de estos grupos, permitiendo la organización y presentación de la información tal como se solicita.

---

**Requerimiento en `taxonomia.md`:**
> "Esta sección describe los **atributos específicos** que se incluyen en cada dimensión... `official_product_name`, `primary_material`, `net_weight`, etc."

**Solución en este Modelo:**
*   Se implementa a través de la entidad `Atributo`. Cada uno de los cientos de atributos potenciales listados en `taxonomia.md` se define como un registro de `Atributo` en el sistema.
*   La `key` canónica del `Atributo` (ej. `net_weight`) corresponde directamente al identificador en `taxonomia.md`.
*   Las propiedades del `Atributo` (`tipo`, `opciones`, `es_requerido`) permiten al sistema validar los valores, cumpliendo con el objetivo de `taxonomia.md` de "asegurar consistencia y completitud".

---

**Requerimiento en `taxonomia.md`:**
> "Estructura de Especificación de Producto Específica por Subcategoría: Cada `Subcategoría de Producto`... está asociada con una `estructura de especificación de producto` única."

**Solución en este Modelo:**
*   Se implementa mediante la relación entre `SubcategoriaDeProducto` -> `PlantillaDeProducto` -> `Atributo`.
*   Al asociar una `Plantilla` a una `Subcategoría`, y luego los `Atributos` a esa `Plantilla`, se crea efectivamente una "estructura de especificación" única y aplicable a todos los productos que se creen dentro de esa subcategoría.

## Conclusión

La especificación detallada propuesta implementa todos los conceptos estructurales y de contenido requeridos por `taxonomia.md`. Proporciona las entidades, relaciones y flujos de trabajo necesarios para dar vida al vocabulario y la jerarquía de negocio definidos en dicho documento, al tiempo que se alinea con el modelo operativo "Made to Order" que hemos refinado.
