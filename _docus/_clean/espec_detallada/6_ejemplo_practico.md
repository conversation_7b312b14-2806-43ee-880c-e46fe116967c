
# Sección 6: Ejemplo Práctico Detallado (Versión Mejorada)

## Propósito

Este documento ilustra, con un nivel de detalle superior, cómo las entidades del modelo interactúan para representar un escenario de negocio completo. Se responde a las preguntas sobre dónde se almacena cada tipo de información y se utiliza un ejemplo más rico en atributos para demostrar la robustez del modelo.

---

## Modelo de Datos Detallado

Antes del ejemplo, clarificamos la estructura de las entidades transaccionales clave:

#### `VarianteDeProducto`
Esta entidad es la **ficha técnica** o "receta" única. Contiene toda la información sobre *qué* es el producto.
*   `id`: Identificador único.
*   `plantilla_id`: El "molde" del catálogo en el que se basa.
*   `sku_interno`: Un SKU generado internamente para esta configuración única.
*   `estado`: El estado de la especificación (ej. `<PERSON>rrador`, `Finalizada`).
*   `version`: Un número para versionar los cambios sobre la especificación.
*   `especificaciones`: El campo JSON con todos los valores de los atributos técnicos y descriptivos heredados de la plantilla.

#### `ProductoDeProyecto`
Esta entidad es la **orden de trabajo** dentro de un proyecto. Responde al *cuántos* y *cómo se compra*.
*   `id`: Identificador único.
*   `proyecto_id`: El proyecto al que pertenece.
*   `variante_id`: La "receta" que se va a producir.
*   `cantidad`: El número de unidades requeridas.
*   `condiciones_de_compra_id`: Vínculo a las condiciones comerciales finales acordadas con el proveedor.

#### `CondicionesDeCompra` (Nueva Entidad)
Esta entidad almacena el **acuerdo comercial** con el proveedor. Se crea después de finalizar el sourcing.
*   `id`: Identificador único.
*   `proveedor_id`: El proveedor elegido.
*   `costo_unitario`: El costo por unidad en la moneda acordada.
*   `moneda`: (ej. `USD`, `EUR`).
*   `incoterm`: El término de comercio internacional (ej. `FOB`, `EXW`).
*   `puerto_de_entrega`: (si aplica) El puerto acordado.
*   `lead_time_produccion`: Días de producción.

--- 

## Ejemplo Práctico con Modelo Detallado

### Paso 1: Configuración del Catálogo (Rol: Administrador)

Se expande la plantilla para incluir atributos de diferentes grupos, como `atributos_centrales` y `embalaje_logistica`.

*Tabla: Atributo* (Adiciones al ejemplo anterior)
```json
[
  {
    "id": 105,
    "plantilla_id": 1000,
    "grupo_id": 200, // ID de 'atributos_centrales'
    "key": "gsm_value",
    "label_es": "Gramaje de Tela (GSM)",
    "label_en": "Fabric Weight (GSM)",
    "tipo": "integer"
  },
  {
    "id": 106,
    "plantilla_id": 1000,
    "grupo_id": 300, // ID de 'embalaje_logistica'
    "key": "unit_packaging_description",
    "label_es": "Descripción Empaque Unitario",
    "label_en": "Unit Packaging Description",
    "tipo": "string"
  }
]
```

### Paso 2: Creación del Proyecto y la Variante (Rol: Analista)

Se crea una única `VarianteDeProducto` para la gorra roja, ahora con una especificación más rica y un modelo de entidad más completo.

*Tabla: Proyecto*
```json
{ "id": 50, "nombre": "Campaña Verano Cliente X", "cliente_id": 123 }
```

*Tabla: VarianteDeProducto*
```json
{
  "id": 2002,
  "plantilla_id": 1000,
  "sku_interno": "PSS-VAR-00123",
  "estado": "Borrador",
  "version": 1,
  "especificaciones": {
    "color": "Rojo",
    "logo_technique": "embroidered",
    "logo_file": "/logos/cliente_x.ai",
    "thread_material": "algodon",
    "gsm_value": 320,
    "unit_packaging_description": "Bolsa de polietileno individual con adhesivo de talla."
  }
}
```

*Tabla: ProductoDeProyecto*
```json
{
  "id": 302,
  "proyecto_id": 50,
  "variante_id": 2002,
  "cantidad": 300,
  "condiciones_de_compra_id": null // Aún no se ha definido
}
```

### Paso 3: Proceso de Sourcing (Rol: Analista)

El proceso de RFQ y recepción de cotizaciones con desviaciones sigue igual que en el ejemplo anterior. Se elige la oferta del Proveedor B, que proponía usar hilo de poliéster a un costo de $4.80.

### Paso 4: Decisión y Finalización (Rol: Analista)

Aquí es donde el modelo mejorado muestra su claridad.

**4.a. Se actualiza y finaliza la `VarianteDeProducto`:**

*Se actualiza el registro con id=2002 en la tabla `VarianteDeProducto`*
```json
{
  "estado": "Finalizada",
  "version": 2,
  "especificaciones": {
    "color": "Rojo",
    "logo_technique": "embroidered",
    "logo_file": "/logos/cliente_x.ai",
    "thread_material": "polyester", // Valor actualizado
    "gsm_value": 320,
    "unit_packaging_description": "Bolsa de polietileno individual con adhesivo de talla."
  }
}
```

**4.b. Se crean las `CondicionesDeCompra` definitivas:**

*Se crea un nuevo registro en la tabla `CondicionesDeCompra`*
```json
{
  "id": 701,
  "proveedor_id": 99,
  "costo_unitario": 4.80,
  "moneda": "USD",
  "incoterm": "FOB",
  "puerto_de_entrega": "Shanghai",
  "lead_time_produccion": 25
}
```

**4.c. Se vincula todo en el `ProductoDeProyecto`:**

*Se actualiza el registro con id=302 en la tabla `ProductoDeProyecto`*
```json
{
  "condiciones_de_compra_id": 701 // Se asigna el ID del acuerdo comercial
}
```

## Resultado Final Mejorado

Con este modelo, la información queda perfectamente organizada y separada:

*   La **`VarianteDeProducto`** contiene únicamente la **especificación técnica** final del producto.
*   Las **`CondicionesDeCompra`** contienen únicamente los **términos comerciales** acordados con el proveedor.
*   El **`ProductoDeProyecto`** actúa como el **conector central**, uniendo la especificación técnica, las condiciones comerciales y el contexto del proyecto (cantidad).

```