# Sección 2: Modelo de Entidades y Sus Atributos

Esta sección actúa como el diccionario de datos del sistema, definiendo cada entidad, su propósito y sus campos más relevantes.

---

### 2.1. Entidades del Catálogo

Estas entidades forman la base de conocimiento curada y estable del sistema. Son gestionadas por administradores.

#### `CategoriaDeProducto`
*   **Propósito:** El nivel más alto de agrupación para la taxonomía (ej. "Textiles", "Merchandising").
*   **Atributos Clave:** `id`, `nombre`.

#### `SubcategoriaDeProducto`
*   **Propósito:** Un nivel de clasificación más específico (ej. "Gorras", "Tazones").
*   **Atributos Clave:** `id`, `nombre`, `categoria_id`.

#### `GrupoDeAtributos`
*   **Propósito:** Corresponde a las "Dimensiones de Especificación" de `taxonomia.md`. Agrupa atributos para organizar la interfaz y el modelo mental (ej. "Atributos Centrales", "Visual y Marca").
*   **Atributos Clave:** `id`, `nombre`.

#### `PlantillaDeProducto`
*   **Propósito:** El "molde" o "plano" para un tipo de producto. Define qué atributos son relevantes y personalizables.
*   **Atributos Clave:** `id`, `nombre` (ej. "Gorra Textil 3 Paneles"), `subcategoria_id`.

#### `Atributo`
*   **Propósito:** Definición canónica y multilingüe de una única característica personalizable reutilizable en todas las plantillas.
*   **Relación:** No se vincula directamente a una plantilla; la asociación a plantillas y su configuración se realiza vía el pivot `PlantillaAtributo`.
*   **Atributos Clave:**
    *   `id`
    *   `key`: Identificador interno, único y en inglés (ej. `thread_color`).
    *   `label_es`: Etiqueta para la interfaz y documentos en español (ej. "Color del Bordado").
    *   `label_en`: Etiqueta para la interfaz y documentos en inglés (ej. "Embroidery Color").
    *   `tipo`: Tipo de dato (ej. `string`, `integer`, `enum`, `boolean`).
    *   `opciones`: (Para `enum`) JSON con opciones predefinidas `[{key, label_es, label_en}]`.
    *   `activo`: Booleano para habilitar/deshabilitar el atributo en el catálogo.

#### `PlantillaAtributo` (Pivot de Configuración por Plantilla)
*   **Propósito:** Define cómo se usa un `Atributo` específico dentro de una `PlantillaDeProducto`: grupo/dimensión, obligatoriedad, visibilidad, orden y propiedades de UI/validación.
*   **Atributos Clave:**
    *   `id`
    *   `plantilla_id`: Referencia a `PlantillaDeProducto`.
    *   `atributo_id`: Referencia a `Atributo`.
    *   `grupo_id`: Referencia a `GrupoDeAtributos` (clasificación dentro de la plantilla).
    *   `es_requerido`: Booleano de obligatoriedad.
    *   `visible`: Booleano de visibilidad en UI.
    *   `orden`: Entero para orden de presentación.
    *   `ui_component`: Componente de UI sugerido (ej. `text`, `select`, `toggle`).
    *   `ui_props`: JSON de propiedades específicas del componente.
    *   `default_value`: Valor por defecto (JSON cuando aplique).
    *   `reglas_validacion`: JSON con reglas adicionales de validación (ej. `['min:1','max:100']`).
    *   `ayuda_ui`, `placeholder`, `seccion`, `columna_span`: Metadatos de presentación.

---

### 2.2. Entidades Transaccionales (de Proyecto y Sourcing)

Estas entidades se crean y modifican durante las operaciones diarias del negocio.

#### `Proyecto`
*   **Propósito:** El contenedor principal para todas las actividades relacionadas con un cliente y un objetivo de negocio.
*   **Atributos Clave:** `id`, `nombre`, `cliente_id`.

#### `VarianteDeProducto`
*   **Propósito:** La "receta específica" o ficha técnica única de un producto. Es agnóstica al proyecto o la cantidad.
*   **Atributos Clave:**
    *   `id`
    *   `plantilla_id`: La plantilla en la que se basa.
    *   `sku_interno`: Un SKU generado internamente para esta configuración única.
    *   `estado`: El estado de la especificación (ej. `Borrador`, `Finalizada`).
    *   `version`: Un número para versionar los cambios sobre la especificación.
    *   `especificaciones`: Un campo JSON que almacena los valores de los atributos en formato `{"key": "valor"}`.

#### `ProductoDeProyecto`
*   **Propósito:** La "orden de trabajo" o línea de pedido interna. Es la entidad central que conecta la especificación, el contexto del proyecto y las condiciones comerciales.
*   **Atributos Clave:**
    *   `id`
    *   `proyecto_id`: El proyecto al que pertenece.
    *   `variante_id`: La "receta" que se va a producir.
    *   `cantidad`: El número de unidades requeridas.
    *   `condiciones_de_compra_id`: Vínculo a las condiciones comerciales finales.

#### `CondicionesDeCompra`
*   **Propósito:** Almacena el acuerdo comercial final con un proveedor para un `ProductoDeProyecto` específico.
*   **Atributos Clave:** `id`, `proveedor_id`, `costo_unitario`, `moneda`, `incoterm`, `puerto_de_entrega`, `lead_time_produccion`.

#### `SolicitudDeCotizacion` (RFQ)
*   **Propósito:** La solicitud formal enviada a proveedores para costear una `VarianteDeProducto`.
*   **Atributos Clave:** `id`, `variante_id`, `fecha_envio`, `fecha_limite`.

#### `CotizacionProveedor`
*   **Propósito:** La respuesta de un proveedor a un RFQ.
*   **Atributos Clave:** `id`, `solicitud_id`, `proveedor_id`, `precio_unitario`, `moq`, `tiempo_entrega`, `desviacion_especificacion` (JSON).

---

### 2.3. Catálogo de Datos Canónicos (Ejemplos Exhaustivos)

Para que el modelo sea tangible, a continuación se detallan los registros canónicos y ejemplos exhaustivos que deben poblar las entidades del catálogo, basados en los requerimientos de `taxonomia.md`.

#### `CategoriasDeProducto` y `SubcategoriasDeProducto` Sugeridas

*   **Merchandising:**
    *   Artículos para Beber (tazones, botellas de agua)
    *   Instrumentos de Escritura
    *   Vestuario y Accesorios (poleras, jockeys)
    *   Accesorios Tecnológicos
    *   Artículos de Oficina y Escritorio
    *   Estilo de Vida y Aire Libre
    *   Premios y Reconocimientos
*   **Material de Punto de Venta (PDV) y Exhibición (Material POP):**
    *   Señalética y Banners (pendones roller)
    *   Soportes y Unidades de Exhibición
    *   Materiales Impresos
    *   Mobiliario Promocional
*   **Textiles:**
    *   Vestuario (Prendas)
    *   Textiles para el Hogar (toallas, mantas)
    *   Bolsos y Soluciones de Transporte
    *   Textiles Técnicos Especializados

#### `GruposDeAtributos` Canónicos

Corresponden a las 7 "Dimensiones de Especificación":

1.  `informacion_basica` (Información Básica del Producto)
2.  `atributos_centrales` (Atributos Centrales del Producto)
3.  `visual_marca` (Especificaciones Visuales y de Marca)
4.  `textiles` (Características Específicas de Textiles)
5.  `embalaje_logistica` (Embalaje y Logística)
6.  `uso_ciclo_vida` (Uso y Ciclo de Vida)
7.  `comercial_abastecimiento` (Comercial y Abastecimiento)

#### Listado Exhaustivo de `Atributos` por Grupo

Nota: El agrupamiento operativo en formularios se determina por `PlantillaAtributo.grupo_id`. Este listado refleja la clasificación canónica sugerida del catálogo para facilitar la configuración de plantillas. Cada ítem representa un registro potencial en `Atributo` (la `key` es el identificador).

##### Grupo: `informacion_basica`
*   `official_product_name`
*   `variant_name`
*   `seo_friendly_title`
*   `internal_sku`
*   `short_marketing_description`
*   `long_detailed_description`
*   `key_selling_points`
*   `target_audience`
*   `disclaimers`

##### Grupo: `atributos_centrales`
*   `primary_material`
*   `secondary_materials`
*   `recycled_content_percentage`
*   `material_certifications`
*   `specific_material_grade`
*   `material_origin`
*   `overall_external_dimensions`
*   `internal_dimensions`
*   `diameter`
*   `capacity_volume`
*   `thickness`
*   `specific_part_dimensions`
*   `dimensional_tolerances`
*   `folded_collapsed_dimensions`
*   `net_weight`
*   `maximum_load_capacity`
*   `weight_distribution`
*   `gsm_value`
*   `gsm_tolerance`
*   `thread_count`
*   `assembly_required`
*   `assembly_type`
*   `joining_mechanisms`
*   `structural_design`
*   `number_of_parts`

##### Grupo: `visual_marca`
*   `printing_methods`
*   `colors_per_print_location`
*   `maximum_printable_area`
*   `ink_type`
*   `specialty_inks`
*   `embroidery_details`
*   `surface_texture`
*   `lamination_type`
*   `protective_coatings`
*   `specific_treatments`
*   `standard_base_colors`
*   `custom_color_capability`
*   `pantone_numbers`
*   `color_consistency_requirements`
*   `colorfastness_rating`
*   `imprint_locations`
*   `imprint_area_dimensions`
*   `placement_restrictions`
*   `multiple_imprint_locations`
*   `label_type`
*   `label_material`
*   `label_dimensions`
*   `label_information_content`
*   `label_attachment_method`
*   `accepted_file_formats`
*   `minimum_resolution`
*   `color_mode`
*   `vector_format_required`
*   `bleed_specifications`
*   `fonts_outlined`
*   `template_available`

##### Grupo: `textiles`
*   `fit_style`
*   `garment_construction`
*   `fabric_pattern`
*   `neckline_style`
*   `sleeve_style`
*   `hem_style`
*   `specific_features`
*   `available_sizes`
*   `sizing_chart`
*   `sizing_tolerance`
*   `international_size_conversions`
*   `washing_instructions`
*   `drying_instructions`
*   `ironing_instructions`
*   `bleaching_instructions`
*   `dry_cleaning_instructions`
*   `care_symbols`

##### Grupo: `embalaje_logistica`
*   `unit_packaging_description`
*   `unit_packaging_material`
*   `unit_packaging_dimensions`
*   `unit_packaging_branding`
*   `unit_packaging_closure`
*   `units_per_inner_pack`
*   `inner_packaging_type`
*   `inner_pack_dimensions`
*   `inner_pack_labeling`
*   `units_per_master_carton`
*   `master_carton_material`
*   `master_carton_dimensions`
*   `master_carton_gross_weight`
*   `master_carton_net_weight`
*   `master_carton_markings`
*   `palletization_info`
*   `unit_of_measure`
*   `pricing_ordering_base`
*   `estimated_shipping_dimensions`
*   `estimated_shipping_weight`
*   `volumetric_weight`

##### Grupo: `uso_ciclo_vida`
*   `primary_intended_use`
*   `intended_environment`
*   `system_compatibility`
*   `target_user_group`
*   `expected_lifespan`
*   `applicable_shelf_life`
*   `expiry_date_requirements`
*   `batch_coding_requirements`
*   `storage_temperature`
*   `stacking_limitations`
*   `storage_sensitivities`

##### Grupo: `comercial_abastecimiento`
*   `exw_unit_cost`
*   `fob_unit_cost`
*   `cif_unit_cost`
*   `volume_price_tiers`
*   `setup_charges`
*   `moq_per_sku`
*   `moq_per_order`
*   `preferred_supplier`
*   `supplier_part_number`
*   `country_of_origin`
*   `hs_code`
*   `country_specific_codes`
