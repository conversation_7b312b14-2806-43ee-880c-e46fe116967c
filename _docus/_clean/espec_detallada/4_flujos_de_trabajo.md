# Sección 4: Flujos de Trabajo Clave

Esta sección describe la aplicación práctica del modelo de entidades a través de los procesos de negocio principales. Narra el ciclo de vida de un producto desde la concepción hasta la cotización, explicando qué entidades se crean y modifican en cada paso.

---

### Flujo 1: Creación de la Especificación Inicial ("Idea en Evolución")

**Objetivo:** Traducir el requerimiento de un cliente en una especificación técnica inicial.

1.  **Contexto:** Un analista de ventas recibe un brief para un nuevo producto (ej. "500 gorras rojas con logo bordado").
2.  **Selección de Plantilla:** El analista navega el catálogo y elige la `PlantillaDeProducto` "Gorra Textil 3 Paneles".
3.  **Creación de la Variante:** El sistema presenta un formulario basado en los `Atributos` de la plantilla. El analista rellena los campos en español, creando una nueva `VarianteDeProducto` ("borrador" o "versión de trabajo").
    *   **Entidad Creada:** `VarianteDeProducto`.
    *   **Datos Guardados:** Las especificaciones (`{"color": "Rojo", "logo_technique": "embroidered", ...}`) se guardan en el campo `especificaciones`.
4.  **Creación del `ProductoDeProyecto`:** Se crea un `ProductoDeProyecto` que asocia esta nueva `VarianteDeProducto` al `Proyecto` actual y establece la cantidad solicitada (500 unidades).

**Resultado:** Tenemos una especificación inicial lista para el proceso de sourcing.

---

### Flujo 2: Sourcing y Recepción de Cotizaciones (RFQ)

**Objetivo:** Obtener costos y propuestas de múltiples proveedores para la especificación inicial.

1.  **Creación del RFQ:** El analista inicia el proceso de sourcing para la `VarianteDeProducto` creada en el flujo anterior. 
    *   **Entidad Creada:** `SolicitudDeCotizacion`, vinculada a la `VarianteDeProducto`.
2.  **Envío a Proveedores:** El sistema registra a qué proveedores se les envía la solicitud.
3.  **Recepción de Ofertas:** A medida que los proveedores responden, el analista registra cada oferta.
    *   **Entidad Creada:** Una `CotizacionProveedor` por cada respuesta.
    *   **Registro de Desviaciones:** Si un proveedor (ej. Proveedor B) propone un cambio (usar hilo de poliéster), el analista anota esta diferencia en el campo `desviacion_especificacion` de la `CotizacionProveedor` del Proveedor B. La `VarianteDeProducto` original no se modifica en este paso.

**Resultado:** Tenemos un conjunto de ofertas comparables. Para cada una, conocemos el precio, las condiciones y si su especificación coincide o se desvía de nuestra solicitud original.

---

### Flujo 3: Selección y Consolidación de la Especificación Final

**Objetivo:** Elegir la mejor oferta y establecer la especificación técnica definitiva que se producirá.

1.  **Análisis y Decisión:** El equipo revisa todas las `CotizacionesDeProveedor`. Comparan precios, condiciones y el impacto de cualquier desviación en la especificación. Se elige la oferta ganadora (ej. la del Proveedor B).
2.  **Consolidación de la Variante:** Este es el paso clave. El analista ahora **edita la `VarianteDeProducto` original** (la "idea en evolución").
    *   **Acción:** Modifica los atributos necesarios para que coincidan con la especificación de la oferta ganadora (ej. cambia el material del hilo a "poliéster").
3.  **Registro de Condiciones Finales:** El analista actualiza el `ProductoDeProyecto` con los datos finales de la `CotizacionProveedor` ganadora: `costo_unitario_final` y `proveedor_final_id`.

**Resultado:** La `VarianteDeProducto` ahora representa la **especificación final y definitiva**. Está validada por un proveedor, tiene un costo real asociado y está lista para ser presentada al cliente.

---

### Flujo 4: Generación de Documentos Multilenguaje

**Objetivo:** Comunicar la especificación final de manera precisa a diferentes audiencias.

1.  **Contexto:** Tenemos la `VarianteDeProducto` finalizada, con sus especificaciones guardadas con `keys` canónicas (ej. `{"thread_color": "white"}`).
2.  **Generar Cotización a Cliente (ES):**
    *   El sistema recorre las especificaciones de la variante.
    *   Para cada `key`, busca el `Atributo` correspondiente en la `Plantilla` y extrae la `label_es`.
    *   El documento muestra: **"Color del Bordado: Blanco"**.
3.  **Generar Orden de Compra (EN):**
    *   El sistema realiza el mismo proceso, pero extrae la `label_en`.
    *   El documento muestra: **"Embroidery Color: White"**.

**Resultado:** Se generan documentos precisos y adaptados a cada audiencia desde una única fuente de verdad, eliminando errores de comunicación.
