# Sección 3: Relaciones Entre Entidades

Esta sección describe cómo las entidades definidas en la sección anterior se conectan para formar un modelo de datos coherente. Las relaciones se expresan desde la perspectiva de una entidad y su conexión con otra.

---

### Relaciones del Catálogo

*   Una `CategoriaDeProducto` **tiene muchas** `SubcategoriasDeProducto`.
    *   *Una `SubcategoriaDeProducto` **pertenece a una** `CategoriaDeProducto`.*

*   Una `SubcategoriaDeProducto` **tiene muchas** `PlantillasDeProducto`.
    *   *Una `PlantillaDeProducto` **pertenece a una** `SubcategoriaDeProducto`.*

*   Una `PlantillaDeProducto` **tiene muchos** `Atributos`.
    *   *Un `Atributo` **pertenece a una** `PlantillaDeProducto`.*

*   Un `GrupoDeAtributos` **tiene muchos** `Atributos`.
    *   *Un `Atributo` **pertenece a un** `GrupoDeAtributos`.*

---

### Relaciones Transaccionales (Proyecto y Sourcing)

*   Un `Proyecto` **tiene muchos** `ProductosDeProyecto`.
    *   *Un `ProductoDeProyecto` **pertenece a un** `Proyecto`.*

*   Un `ProductoDeProyecto` **tiene una** `VarianteDeProducto`.
    *   *Una `VarianteDeProducto` **pertenece a un** `ProductoDeProyecto`.*
    *   *(Nota: Esta es una relación 1 a 1, ya que en este modelo la variante es única para el producto del proyecto).*

*   Una `VarianteDeProducto` **se basa en una** `PlantillaDeProducto`.
    *   *Esta relación define qué `Atributos` son aplicables a la `Variante`.*

*   Una `VarianteDeProducto` **puede tener una** `SolicitudDeCotizacion` (RFQ).
    *   *Una `SolicitudDeCotizacion` **pertenece a una** `VarianteDeProducto`.*

*   Una `SolicitudDeCotizacion` **tiene muchas** `CotizacionesDeProveedor`.
    *   *Una `CotizacionProveedor` **pertenece a una** `SolicitudDeCotizacion`.*
