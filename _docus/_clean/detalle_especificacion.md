## Especificación detallada de atributos (nivel JSON Schema)

1) Convenciones generales
- **Formato**: JSON Schema Draft 2020-12.
- **Unidades canónicas**: longitudes en mm, volúmenes en ml, peso en kg, tolerancias en % (0-100).
- **Campos comunes**: cuando aplique, incluir `unit` si se permite unidad variable; por defecto se omite si la unidad es fija.
- **Nulos**: salvo indicación, no se aceptan `null`.
- **i18n**: descripciones en español; claves en snake_case.

```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "$id": "https://example.com/schemas/product_attributes.schema.json",
  "title": "Atributos de producto",
  "type": "object",
  "additionalProperties": false,
  "$defs": {
    "nonEmptyString": {
      "type": "string",
      "minLength": 1
    },
    "positiveNumber": {
      "type": "number",
      "exclusiveMinimum": 0
    },
    "nonNegativeNumber": {
      "type": "number",
      "minimum": 0
    },
    "mm": {
      "type": "number",
      "minimum": 0,
      "description": "Milímetros (mm)"
    },
    "ml": {
      "type": "number",
      "minimum": 0,
      "description": "Mililitros (ml)"
    },
    "kg": {
      "type": "number",
      "minimum": 0,
      "description": "Kilogramos (kg)"
    },
    "percentage": {
      "type": "number",
      "minimum": 0,
      "maximum": 100,
      "description": "Porcentaje 0-100"
    },
    "dimensions3Dmm": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "width_mm": { "$ref": "#/$defs/mm" },
        "height_mm": { "$ref": "#/$defs/mm" },
        "depth_mm": { "$ref": "#/$defs/mm" }
      },
      "required": ["width_mm", "height_mm", "depth_mm"]
    },
    "tolerancesDims": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "width_mm": { "$ref": "#/$defs/mm" },
        "height_mm": { "$ref": "#/$defs/mm" },
        "depth_mm": { "$ref": "#/$defs/mm" },
        "diameter_mm": { "$ref": "#/$defs/mm" },
        "percent": { "$ref": "#/$defs/percentage" }
      }
    },
    "specificPartDimensions": {
      "type": "array",
      "items": {
        "type": "object",
        "additionalProperties": false,
        "properties": {
          "part": { "$ref": "#/$defs/nonEmptyString" },
          "width_mm": { "$ref": "#/$defs/mm" },
          "height_mm": { "$ref": "#/$defs/mm" },
          "depth_mm": { "$ref": "#/$defs/mm" },
          "diameter_mm": { "$ref": "#/$defs/mm" }
        },
        "required": ["part"]
      }
    },
    "colorPantone": {
      "type": "string",
      "pattern": "^[A-Za-z0-9\-\s]+$",
      "description": "Código Pantone o equivalente normalizado"
    },
    "fileFormat": {
      "type": "string",
      "enum": ["ai", "pdf", "eps", "svg", "psd", "tiff", "png", "jpg"],
      "description": "Formato de archivo de arte"
    },
    "countryCode": {
      "type": "string",
      "pattern": "^[A-Z]{2}$",
      "description": "Código de país ISO 3166-1 alpha-2"
    },
    "currency": {
      "type": "string",
      "pattern": "^[A-Z]{3}$",
      "description": "Código de moneda ISO 4217"
    }
  },
  "properties": {
    "information_basica_del_producto": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "official_product_name": { "$ref": "#/$defs/nonEmptyString", "description": "Nombre oficial del producto" },
        "variant_name": { "type": ["string", "null"], "description": "Nombre de variante si aplica" },
        "seo_friendly_title": { "type": ["string", "null"], "description": "Título SEO" },
        "internal_sku": { "type": ["string", "null"], "description": "SKU interno" },
        "short_marketing_description": { "type": ["string", "null"], "description": "Descripción breve" },
        "long_detailed_description": { "type": ["string", "null"], "description": "Descripción detallada" },
        "key_selling_points": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Puntos de venta clave" },
        "target_audience": { "type": ["string", "null"], "description": "Público objetivo" },
        "disclaimers": { "type": ["string", "null"], "description": "Advertencias / disclaimers" }
      },
      "required": ["official_product_name"]
    },

    "atributos_centrales_del_producto": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "primary_material": { "$ref": "#/$defs/nonEmptyString", "description": "Material principal" },
        "secondary_materials": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Materiales secundarios" },
        "recycled_content_percentage": { "$ref": "#/$defs/percentage", "description": "% de contenido reciclado" },
        "material_certifications": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Certificaciones de material" },
        "specific_material_grade": { "type": ["string", "null"], "description": "Grado específico de material" },
        "material_origin": { "type": ["string", "null"], "description": "Origen del material" },

        "overall_external_dimensions": { "$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones externas" },
        "internal_dimensions": { "$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones internas" },
        "diameter": { "$ref": "#/$defs/mm", "description": "Diámetro (mm)" },
        "capacity_volume": { "$ref": "#/$defs/ml", "description": "Capacidad volumétrica (ml)" },
        "thickness": { "$ref": "#/$defs/mm", "description": "Grosor (mm)" },
        "specific_part_dimensions": { "$ref": "#/$defs/specificPartDimensions", "description": "Dimensiones por parte" },
        "dimensional_tolerances": { "$ref": "#/$defs/tolerancesDims", "description": "Tolerancias dimensionales" },
        "folded_collapsed_dimensions": { "$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones plegado/colapsado" },

        "net_weight": { "$ref": "#/$defs/kg", "description": "Peso neto (kg)" },
        "maximum_load_capacity": { "$ref": "#/$defs/kg", "description": "Capacidad máxima de carga (kg)" },
        "weight_distribution": { "type": ["string", "null"], "description": "Distribución de peso / notas" },

        "gsm_value": { "$ref": "#/$defs/nonNegativeNumber", "description": "Gramaje GSM" },
        "gsm_tolerance": { "$ref": "#/$defs/percentage", "description": "Tolerancia GSM %" },
        "thread_count": { "$ref": "#/$defs/nonNegativeNumber", "description": "Recuento de hilos" },

        "assembly_required": { "type": "boolean", "description": "Requiere ensamblaje" },
        "assembly_type": { "type": ["string", "null"], "description": "Tipo de ensamblaje" },
        "joining_mechanisms": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Mecanismos de unión" },
        "structural_design": { "type": ["string", "null"], "description": "Diseño estructural" },
        "number_of_parts": { "$ref": "#/$defs/nonNegativeNumber", "description": "Número de partes" }
      },
      "required": ["primary_material"]
    },

    "especificaciones_visuales_y_marca": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "printing_methods": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Métodos de impresión" },
        "colors_per_print_location": { "type": "array", "items": { "$ref": "#/$defs/nonNegativeNumber" }, "description": "Colores por ubicación de impresión" },
        "maximum_printable_area": { "$ref": "#/$defs/dimensions3Dmm", "description": "Área máxima imprimible (usar width/height)" },
        "ink_type": { "type": ["string", "null"], "description": "Tipo de tinta" },
        "specialty_inks": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Tintas especiales" },
        "embroidery_details": { "type": ["string", "null"], "description": "Detalles de bordado" },

        "surface_texture": { "type": ["string", "null"], "description": "Textura de superficie" },
        "lamination_type": { "type": ["string", "null"], "description": "Tipo de laminado" },
        "protective_coatings": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Recubrimientos protectores" },
        "specific_treatments": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Tratamientos específicos" },

        "standard_base_colors": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Colores base estándar" },
        "custom_color_capability": { "type": "boolean", "description": "Soporta color personalizado" },
        "pantone_numbers": { "type": "array", "items": { "$ref": "#/$defs/colorPantone" }, "description": "Códigos Pantone" },
        "color_consistency_requirements": { "type": ["string", "null"], "description": "Requisitos de consistencia de color" },
        "colorfastness_rating": { "$ref": "#/$defs/nonNegativeNumber", "description": "Solidez del color (escala definida)" },

        "imprint_locations": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Ubicaciones de impresión" },
        "imprint_area_dimensions": { "type": "array", "items": { "$ref": "#/$defs/dimensions3Dmm" }, "description": "Dimensiones de área por ubicación (usar width/height)" },
        "placement_restrictions": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Restricciones de ubicación" },
        "multiple_imprint_locations": { "type": "boolean", "description": "Múltiples ubicaciones" },

        "label_type": { "type": ["string", "null"], "description": "Tipo de etiqueta (tejida/impresa/otra)" },
        "label_material": { "type": ["string", "null"], "description": "Material de etiqueta" },
        "label_dimensions": { "$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones de etiqueta (usar width/height)" },
        "label_information_content": { "type": ["string", "null"], "description": "Contenido informativo" },
        "label_attachment_method": { "type": ["string", "null"], "description": "Método de fijación" },

        "accepted_file_formats": { "type": "array", "items": { "$ref": "#/$defs/fileFormat" }, "description": "Formatos aceptados" },
        "minimum_resolution": { "$ref": "#/$defs/nonNegativeNumber", "description": "Resolución mínima (dpi)" },
        "color_mode": { "type": "string", "enum": ["RGB", "CMYK", "PMS"], "description": "Modo de color" },
        "vector_format_required": { "type": "boolean", "description": "Se requiere vector" },
        "bleed_specifications": { "$ref": "#/$defs/mm", "description": "Sangrado requerido (mm)" },
        "fonts_outlined": { "type": "boolean", "description": "Fuentes contorneadas" },
        "template_available": { "type": "boolean", "description": "Plantilla disponible" }
      }
    },

    "caracteristicas_especificas_textiles": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "fit_style": { "type": ["string", "null"], "description": "Corte / calce" },
        "garment_construction": { "type": ["string", "null"], "description": "Construcción de prenda" },
        "fabric_pattern": { "type": ["string", "null"], "description": "Patrón de tela" },
        "neckline_style": { "type": ["string", "null"], "description": "Tipo de cuello" },
        "sleeve_style": { "type": ["string", "null"], "description": "Tipo de manga" },
        "hem_style": { "type": ["string", "null"], "description": "Dobladillo" },
        "specific_features": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Características específicas" },

        "available_sizes": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Tallas disponibles" },
        "sizing_chart": { "type": ["string", "null"], "description": "Tabla de tallas (URL o referencia)" },
        "sizing_tolerance": { "$ref": "#/$defs/percentage", "description": "Tolerancia de tallaje %" },
        "international_size_conversions": { "type": "object", "additionalProperties": { "$ref": "#/$defs/nonEmptyString" }, "description": "Conversiones internacionales" },

        "washing_instructions": { "type": ["string", "null"], "description": "Lavado" },
        "drying_instructions": { "type": ["string", "null"], "description": "Secado" },
        "ironing_instructions": { "type": ["string", "null"], "description": "Planchado" },
        "bleaching_instructions": { "type": ["string", "null"], "description": "Blanqueo" },
        "dry_cleaning_instructions": { "type": ["string", "null"], "description": "Lavado en seco" },
        "care_symbols": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Símbolos de cuidado" }
      }
    },

    "embalaje_y_logistica": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "unit_packaging_description": { "type": ["string", "null"], "description": "Descripción del embalaje unitario" },
        "unit_packaging_material": { "type": ["string", "null"], "description": "Material del embalaje unitario" },
        "unit_packaging_dimensions": { "$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones del embalaje unitario" },
        "unit_packaging_branding": { "type": ["string", "null"], "description": "Branding del embalaje unitario" },
        "unit_packaging_closure": { "type": ["string", "null"], "description": "Cierre del embalaje unitario" },

        "units_per_inner_pack": { "$ref": "#/$defs/nonNegativeNumber", "description": "Unidades por embalaje interior" },
        "inner_packaging_type": { "type": ["string", "null"], "description": "Tipo de embalaje interior" },
        "inner_pack_dimensions": { "$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones embalaje interior" },
        "inner_pack_labeling": { "type": ["string", "null"], "description": "Etiquetado embalaje interior" },

        "units_per_master_carton": { "$ref": "#/$defs/nonNegativeNumber", "description": "Unidades por caja master" },
        "master_carton_material": { "type": ["string", "null"], "description": "Material caja master" },
        "master_carton_dimensions": { "$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones caja master" },
        "master_carton_gross_weight": { "$ref": "#/$defs/kg", "description": "Peso bruto caja master (kg)" },
        "master_carton_net_weight": { "$ref": "#/$defs/kg", "description": "Peso neto caja master (kg)" },
        "master_carton_markings": { "type": ["string", "null"], "description": "Marcaciones caja master" },
        "palletization_info": { "type": ["string", "null"], "description": "Paletización" },

        "unit_of_measure": { "type": "string", "enum": ["unit", "pair", "set", "kg", "m", "m2", "l"], "description": "UDM" },
        "pricing_ordering_base": { "type": "string", "enum": ["per_unit", "per_inner_pack", "per_master_carton", "per_weight"], "description": "Base de precio/pedido" },

        "estimated_shipping_dimensions": { "$ref": "#/$defs/dimensions3Dmm", "description": "Dims. estimadas de despacho (caja master)" },
        "estimated_shipping_weight": { "$ref": "#/$defs/kg", "description": "Peso estimado de despacho (kg)" },
        "volumetric_weight": { "$ref": "#/$defs/kg", "description": "Peso volumétrico (kg)" }
      }
    },

    "uso_y_ciclo_de_vida": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "primary_intended_use": { "type": ["string", "null"], "description": "Uso previsto principal" },
        "intended_environment": { "type": ["string", "null"], "description": "Ambiente previsto" },
        "system_compatibility": { "type": ["string", "null"], "description": "Compatibilidad de sistema" },
        "target_user_group": { "type": ["string", "null"], "description": "Grupo de usuario objetivo" },
        "expected_lifespan": { "$ref": "#/$defs/nonNegativeNumber", "description": "Vida útil esperada (meses)" },

        "applicable_shelf_life": { "$ref": "#/$defs/nonNegativeNumber", "description": "Vida de estante aplicable (meses)" },
        "expiry_date_requirements": { "type": ["string", "null"], "description": "Requisitos de fecha de caducidad" },
        "batch_coding_requirements": { "type": ["string", "null"], "description": "Requisitos de codificación de lotes" },

        "storage_temperature": { "type": ["string", "null"], "description": "Temperatura de almacenamiento" },
        "stacking_limitations": { "type": ["string", "null"], "description": "Limitaciones de apilamiento" },
        "storage_sensitivities": { "type": ["string", "null"], "description": "Sensibilidades de almacenamiento" }
      }
    },

    "comercial_y_abastecimiento": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "exw_unit_cost": { "$ref": "#/$defs/nonNegativeNumber", "description": "Costo unitario EXW" },
        "fob_unit_cost": { "$ref": "#/$defs/nonNegativeNumber", "description": "Costo unitario FOB" },
        "cif_unit_cost": { "$ref": "#/$defs/nonNegativeNumber", "description": "Costo unitario CIF" },
        "volume_price_tiers": {
          "type": "array",
          "description": "Escalas de precio por volumen",
          "items": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
              "min_qty": { "$ref": "#/$defs/nonNegativeNumber" },
              "unit_price": { "$ref": "#/$defs/nonNegativeNumber" },
              "currency": { "$ref": "#/$defs/currency" }
            },
            "required": ["min_qty", "unit_price", "currency"]
          }
        },
        "setup_charges": { "$ref": "#/$defs/nonNegativeNumber", "description": "Cargos de preparación" },

        "moq_per_sku": { "$ref": "#/$defs/nonNegativeNumber", "description": "MOQ por SKU" },
        "moq_per_order": { "$ref": "#/$defs/nonNegativeNumber", "description": "MOQ por orden" },

        "preferred_supplier": { "type": ["string", "null"], "description": "Proveedor preferido" },
        "supplier_part_number": { "type": ["string", "null"], "description": "Código de parte del proveedor" },
        "country_of_origin": { "$ref": "#/$defs/countryCode", "description": "País de origen" },

        "hs_code": { "type": ["string", "null"], "description": "Código HS" },
        "country_specific_codes": { "type": "object", "additionalProperties": { "type": "string" }, "description": "Códigos específicos por país" }
      }
    },

    "certificaciones_y_requerimientos": {
      "type": "object",
      "additionalProperties": false,
      "properties": {
        "client_specific_certifications": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Certificaciones del cliente" },
        "client_audit_requirements": { "type": ["string", "null"], "description": "Requisitos de auditoría del cliente" },
        "client_quality_standards": { "type": ["string", "null"], "description": "Estándares de calidad del cliente" },
        "client_compliance_documents": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Documentos de cumplimiento del cliente" },

        "country_specific_regulations": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Regulaciones por país" },
        "dangerous_goods_classification": { "type": ["string", "null"], "description": "Clasificación mercancías peligrosas" },
        "safety_certifications": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Certificaciones de seguridad" },
        "sanitary_registrations": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Registros sanitarios" },

        "internal_quality_standards": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Estándares internos de calidad" },
        "internal_compliance_requirements": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Requisitos internos de compliance" },
        "internal_documentation_requirements": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Documentación interna requerida" },
        "internal_audit_requirements": { "type": ["string", "null"], "description": "Requisitos de auditoría interna" },

        "material_certifications": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Certificaciones de materiales" },
        "required_standards": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Normas requeridas" },
        "testing_documents": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Documentos de pruebas/certificación (referencias)" },
        "msds_required": { "type": "boolean", "description": "Requiere MSDS" },
        "factory_audit_status": { "type": ["string", "null"], "description": "Estatus de auditoría de fábrica" },
        "imo_certification": { "type": "boolean", "description": "Certificación IMO" },

        "children_product": { "type": "boolean", "description": "Producto para niños" },
        "children_safety_standards": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Normas de seguridad infantil" },
        "age_appropriateness": { "$ref": "#/$defs/nonNegativeNumber", "description": "Edad recomendada mínima (años)" },

        "food_contact_product": { "type": "boolean", "description": "Producto de contacto con alimentos" },
        "food_grade_certification": { "type": ["string", "null"], "description": "Certificación grado alimenticio" },
        "food_safety_standards": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Normas de seguridad alimentaria" },

        "topical_product": { "type": "boolean", "description": "Producto tópico" },
        "dermatological_testing": { "type": ["string", "null"], "description": "Pruebas dermatológicas" },
        "skin_safety_standards": { "type": "array", "items": { "$ref": "#/$defs/nonEmptyString" }, "description": "Normas de seguridad de piel" },

        "aql_level": { "$ref": "#/$defs/nonNegativeNumber", "description": "Nivel AQL" },
        "aql_inspection_level": { "type": ["string", "null"], "description": "Nivel de inspección AQL" },
        "aql_sample_size": { "$ref": "#/$defs/nonNegativeNumber", "description": "Tamaño de muestra AQL" },
        "quality_inspection_frequency": { "type": ["string", "null"], "description": "Frecuencia de inspección de calidad" },

        "defect_percentage": { "$ref": "#/$defs/percentage", "description": "% de defecto permitido" },
        "overage_quantity": { "$ref": "#/$defs/nonNegativeNumber", "description": "Cantidad de sobreproducción permitida" },
        "production_buffer": { "$ref": "#/$defs/percentage", "description": "Buffer de producción %" },
        "rework_percentage": { "$ref": "#/$defs/percentage", "description": "% de retrabajo permitido" }
      }
    }
  }
}
```

2) Notas por grupo
- **1. Información Básica**: sólo `official_product_name` es requerido; el resto contextual.
- **2. Atributos Centrales**: para 2.2 Dimensiones se usan objetos estructurados en mm; tolerancias opcionales por eje y/o porcentaje.
- **3. Visuales y Marca**: áreas imprimibles usan `dimensions3Dmm` pero operan típicamente con `width_mm` y `height_mm`.
- **4. Textiles**: `available_sizes` no se normaliza aquí; puede mapearse a una lista controlada según categoría.
- **5. Embalaje y Logística**: `units_per_*` y pesos/dimensiones son no negativos; `pricing_ordering_base` define semántica comercial.
- **6. Uso y Ciclo de Vida**: se modela vida útil en meses para consistencia; se puede ajustar por dominio.
- **7. Comercial**: `volume_price_tiers` admite múltiples escalas, cada una con `min_qty`, `unit_price`, `currency`.
- **8. Certificaciones**: campos textuales permiten nomenclaturas de estándares; se puede especializar por vertical.

3) Ejemplos mínimos
```json
{
  "information_basica_del_producto": {
    "official_product_name": "Botella deportiva 750ml",
    "variant_name": "Tapa rosca negra"
  },
  "atributos_centrales_del_producto": {
    "primary_material": "PET",
    "overall_external_dimensions": {"width_mm": 75, "height_mm": 250, "depth_mm": 75},
    "capacity_volume": 750,
    "thickness": 1.2
  },
  "especificaciones_visuales_y_marca": {
    "printing_methods": ["serigrafía"],
    "pantone_numbers": ["PMS 186 C"],
    "accepted_file_formats": ["ai", "pdf"],
    "vector_format_required": true
  },
  "embalaje_y_logistica": {
    "units_per_master_carton": 50,
    "master_carton_dimensions": {"width_mm": 600, "height_mm": 520, "depth_mm": 400},
    "master_carton_gross_weight": 12.5
  }
}
```
