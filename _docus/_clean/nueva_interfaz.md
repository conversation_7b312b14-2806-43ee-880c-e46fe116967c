# Interfaz del Catálogo y Flujo de Configuración (Consistente con nuevo_modelo_codex)

Este documento describe pantallas y componentes de interfaz coherentes con el modelo de datos propuesto en `nuevo_modelo_codex.md`.
- Entidades del sistema (en inglés): `product_templates`, `product_projects`, `product_variants`, `procurement_lots`, `procurement_groups`, `purchase_orders`.
- Términos de negocio en español para los rótulos de UI.

---

``` Vista 1 — Catálogo de Plantillas (Admin / Sourcing)
📁 Catálogo de Plantillas (`product_templates`)

Navegación por Taxonomía

  📁 Merchandising
     📁 Vestuario y Accesorios
        📋 Jockey con Logo Bordado         [ACTIVO]
        📋 Polera Básica Personalizada     [ACTIVO]
        📋 Bufanda Corporativa             [BORRADOR]
     📁 Artículos para Beber
        📋 Botella Térmica Personalizada   [ACTIVO]
        📋 Taza Cerámica Premium           [ACTIVO]

[➕ Nueva Plantilla]   [🔎 Buscar]   [⚙️ Configuración]
```

``` 
Vista 1.1 — Editor de Plantilla (`product_templates`)
🔧 PLANTILLA: Jockey con Logo Bordado

📋 INFORMACIÓN BÁSICA
├─ Nombre: [Jockey con Logo Bordado]
├─ Subcategoría: [Vestuario y Accesorios ▼]
├─ Estado: [🟢 Active ▼] (interno: `status = active|draft|deprecated`)
└─ Descripción: [Gorra personalizable con logo bordado...]

📊 GRUPOS DE ATRIBUTOS (definen el esquema de `configuration` en variantes)

🎯 ATRIBUTOS CENTRALES
├─ ✓ panel_count             (enum)   [Requerido] [Visible] [Orden: 1]
├─ ✓ primary_material        (enum)   [Requerido] [Visible] [Orden: 2]
└─ ✓ crown_height_cm         (number) [Opcional]  [Visible] [Orden: 3]

🎨 VISUAL Y MARCA
├─ ✓ base_color              (enum)   [Requerido] [Visible] [Orden: 1]
├─ ✓ embroidery_position     (enum)   [Requerido] [Visible] [Orden: 2]
├─ ✓ thread_color            (enum)   [Opcional]  [Visible] [Orden: 3]
└─ ✓ stitch_count            (number) [Opcional]  [Visible] [Orden: 4]

💼 COMERCIAL / ABASTECIMIENTO
├─ ✓ moq_per_color           (number) [Opcional]  [Oculto]  [Orden: 1]
└─ ✓ embroidery_setup_cost   (money)  [Opcional]  [Oculto]  [Orden: 2]

[+ Agregar Atributo]   [💾 Guardar Plantilla]   [👁️ Vista Previa]
```
---

``` 
Vista 2 — Crear Producto del Proyecto (`product_projects`)
🆕 NUEVO PRODUCTO DEL PROYECTO

📋 Proyecto: [TechInnovate Summit 2024 ▼] (`projects`)
📦 Plantilla: [Jockey con Logo Bordado ▼] (`product_templates`)
🔢 Cantidad Total: [100] unidades  (campo: `total_quantity`)
💱 Moneda: [USD ▼]
🧾 Notas: [—]

[💾 Crear Producto del Proyecto]   [❌ Cancelar]
```

``` 
Vista 3 — Variantes del Producto (`product_variants`)
📦 PRODUCTO DEL PROYECTO: Jockey con Logo Bordado (PP-101)

Suma de variantes = 100 (regla: Σ `quantity` = `total_quantity`)

[➕ Agregar Variante]

— VARIANTE V1 —
Cantidad: [30]
SKU Generado: [JCK-6P-ALG-VRD-001]   [🔄 Regenerar]
Precio Unitario Estimado: [13.00]

CONFIGURACIÓN (JSONB) — campos renderizados según la plantilla
🎯 Centrales
  • panel_count:          [6_panels ▼]
  • primary_material:     [cotton ▼]
  • crown_height_cm:      [10]
🎨 Visual y Marca
  • base_color:           [green ▼]
  • embroidery_position:  (•) frontal  ( ) lateral  ( ) posterior
  • thread_color:         [white ▼]
  • stitch_count:         [5000]

— VARIANTE V2 —
Cantidad: [70]
SKU Generado: [JCK-5P-POL-ROJ-001]   [🔄 Regenerar]
Precio Unitario Estimado: [11.50]

CONFIGURACIÓN (JSONB)
🎯 Centrales
  • panel_count:          [5_panels ▼]
  • primary_material:     [polyester ▼]
🎨 Visual y Marca
  • base_color:           [red ▼]
  • embroidery_position:  (•) frontal  ( ) lateral  ( ) posterior
  • thread_color:         [white ▼]

[💾 Guardar Cambios]   [📊 Validar Σ Cantidades]   [🔎 Buscar variantes por configuración]

```

Notas de validación (negocio)
- La configuración de cada variante respeta el esquema de la plantilla.
- `generated_sku` es único.
- Cambio de `quantity` o `configuration` se bloquea al emitir OC (ver estados más adelante).

---

``` 
Vista 4 — Lotes de Abastecimiento (`procurement_lots`)
📦 LOTES PARA PP-101

Listado de Lotes (1 lote = 1 variante)
  • Lote L1 — Variante V1 (JCK-6P-ALG-VRD-001) — Cantidad: 30 — Proveedor (si adjudicado): CapMaster Pro — Precio Acordado: 13.00 — Estado: [Awarded]
  • Lote L2 — Variante V2 (JCK-5P-POL-ROJ-001) — Cantidad: 70 — Proveedor: — — Precio Acordado: — — Estado: [Quoted]

📈 Promedio Ponderado (referencia para `product_projects.avg_unit_price`): 11.95

[➕ Nuevo Lote]   [✏️ Editar Lote]   [💬 Abrir RFQ]   [📄 Ver Cotizaciones]   [✅ Adjudicar]   [❌ Cancelar]

Detalle de Lote (seleccionado)
  • Variante: V1 — JCK-6P-ALG-VRD-001
  • Cantidad: [30]
  • Proveedor (si adjudicado): [CapMaster Pro]
  • Precio Acordado: [13.00]
  • Moneda: [USD]
  • Condiciones: [EXW, 21 días]
  • Notas: [—]
  [💾 Guardar] [⬅️ Volver]
```

``` 
Vista 4.0 — Cotizaciones del Lote (`supplier_quotes`)
🧾 COTIZACIONES — Lote L2 (Variante V2)

Propuestas recibidas
  • Q1 — Supplier: CapMaster Pro — 12.40 USD — Incoterm: EXW — Lead time: 21 días — Status: received — Vigencia: 2024‑10‑30
  • Q2 — Supplier: HeadWorks — 12.20 USD — Incoterm: FOB — Lead time: 25 días — Status: received — Vigencia: 2024‑11‑05

Acciones
[📤 Enviar RFQ]   [📎 Adjuntar Especificaciones]   [⭐ Preseleccionar]   [✅ Adjudicar Q2]   [❌ Rechazar Q1]

Nota: Al adjudicar, el Lote toma supplier/price/condiciones en snapshot.
```

``` 
Vista 4.1 — Grupo de Consolidación Comercial (`procurement_groups`)
🤝 GRUPO DE CONSOLIDACIÓN: Gorras – CapMaster Pro — USD — Estado: [Quoted]

Participantes (multi‑proyecto, 1 lote = 1 variante)
  • Proyecto A — Lote L1 (Variante V1) — Cantidad: 30
  • Proyecto A — Lote L2 (Variante V2) — Cantidad: 70
  • Proyecto B — Lote L3 (Variante V3) — Cantidad: 100
Total consolidado: 200

Tramos de precio (cotización adjudicada / `pricing_agreements`)
  • 1–99     → 13.00
  • 100–199  → 12.20
  • 200+     → 11.80   ← aplica al total actual

Acciones
[📨 Enviar RFQ]   [✅ Adjudicar]   [🧾 Crear Acuerdo de Precio]   [🔗 Aplicar a OCs]

Nota: Cada `purchase_order_item` tomará el precio del tramo vigente (snapshot).
```

``` 
Vista 4.1a — Cotizaciones de Grupo (`group_supplier_quotes`)
🤝 RFQ CONSOLIDADO — Grupo de Gorras (200 u estimadas)

Cotizaciones por proveedor
  • CapMaster Pro — Status: received — Vigencia: 2024‑10‑30
     Tramos:
       ◦ 1–99 → 13.00
       ◦ 100–199 → 12.20
       ◦ 200+ → 11.80
  • HeadWorks — Status: received — Vigencia: 2024‑10‑28
     Tramos:
       ◦ 1–149 → 12.50
       ◦ 150+  → 11.90

Acciones
[🗂️ Comparar]   [✅ Adjudicar CapMaster Pro]   [🧾 Generar Pricing Agreement]

Nota: Al adjudicar, se crea `pricing_agreements` con sus tramos; las futuras OCs aplicarán el tier correspondiente.
```

---

``` 
Vista 5 — Orden de Compra (`purchase_orders`)
🧾 OC-2024-018 — CapMaster Pro — USD — Estado: [Issued]

Ítems (`purchase_order_items`)
  • V1 — Cantidad: 30 — Unit Price: 13.00
  • V2 — Cantidad: 70 — Unit Price: 11.50

Reglas de estado
- Tras "Issued": bloquear edición de `product_variants.quantity` y `configuration`.
- Flujo: Issued → Accepted → In Production → Shipped → Closed

[📤 Emitir]   [🆗 Aceptar]   [🏭 En Producción]   [🚚 Enviado]   [✅ Cerrar]
```

``` 
Vista 5.1 — Lotes de Producción (`production_lots`) y Hand‑over
🏭 LOTES DE PRODUCCIÓN
  • PO‑Item V2 — plan: 40 + 30
     ◦ Lote P1 — planned_qty: 40 — ETA: 2024‑10‑05 — Estado: in_production — QC: pending — Tracking fábrica: FTR‑8892
     ◦ Lote P2 — planned_qty: 30 — ETA: 2024‑10‑12 — Estado: planned — QC: pending

🤝 ENTREGA A FORWARDER (hand‑over)
  • P1 — handed_to_forwarder_at: 2024‑10‑04
```

``` 
Vista 6 — Embarques Internacionales (`shipments`)
🚢 SHIPMENT SH‑2024‑091 (Ocean) — Forwarder: FastForward — Estado: Departed
  Puertos: Ningbo → San Antonio — ETD: 2024‑10‑05 — ETA: 2024‑10‑28
  Docs: BL, Invoice, Packing List

Ítems (`shipment_items`)
  • Lote P1 — qty: 40 — cartons: 4 — CBM: 0.85 — Weight: 22.5 kg
  • Lote P2 — qty: 30 — cartons: 3 — CBM: 0.62 — Weight: 17.1 kg

[📦 Crear Shipment] [➕ Agregar Lote] [📄 Adjuntar Docs] [🔁 Cambiar Estado]
```

``` 
Vista 7 — Distribución Local (`local_distributions`)
🚚 LD‑2024‑044 — Carrier: EntregaExpress — Origen: DC Maipú — Ventana: 2024‑10‑30 .. 2024‑11‑02 — Estado: Picking
  Docs: Manifiesto, PODs pendientes

Ítems (`local_distribution_items`)
  • ShipmentItem (P1) — qty: 20 — Destino: Sucursal Centro
  • ShipmentItem (P2) — qty: 10 — Destino: Sucursal Norte

[🚚 Crear LD] [➕ Agregar Ítem] [📄 Adjuntar Docs] [✅ Marcar Entregado]
```

---

``` 
Vista 6 — Búsqueda por Configuración (JSONB)
🔎 Buscar variantes por atributos de configuración (`product_variants.configuration`)

Filtros rápidos
- base_color = [red]
- primary_material = [cotton|polyester]
- panel_count = [5_panels|6_panels]

Resultados (paginados)
  • V2 — base_color:red — primary_material:polyester — quantity:70 — SKU:JCK-5P-POL-ROJ-001
  • V3 — base_color:red — primary_material:cotton    — quantity:40 — SKU:JCK-6P-ALG-ROJ-002

(Implementación: índice GIN sobre `configuration`)
```

---

Resumen de consistencia con el modelo
- Catálogo administra `product_templates` y su esquema, que guía la UI de configuración.
- Los analistas crean `product_projects` (consolidación comercial) y definen `product_variants` (granularidad operativa).
- Negociación por proyecto vía `procurement_lots`; consolidación multi‑proyecto vía `procurement_groups` con acuerdos de precio por tramos.
- Ejecución con `purchase_orders`; `production_lots` para planificación y hand‑over; `shipments` para tránsito internacional; `local_distributions` para última milla.
- Reglas de negocio: suma de cantidades, precio promedio ponderado y bloqueos por estado.
