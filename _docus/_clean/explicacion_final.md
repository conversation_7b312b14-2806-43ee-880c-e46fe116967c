# Explicación narrativa de la “Arquitectura Final: Plantillas de Producto con Filament 4”

La propuesta de arquitectura final consolida un modelo robusto para definir, gobernar y operar plantillas de producto dentro de un stack Laravel + Filament 4 + Postgres. Integra tres ideas clave: un catálogo global de atributos, un pivot “rico” que contextualiza esos atributos por plantilla, y especificaciones transaccionales almacenadas en JSONB para variantes. Así, conecta el “diccionario de datos” canónico descrito en `_docs/plans/tax/espec_detallada/2_modelo_de_entidades.md` con ejemplos aplicados como los de `_docs/plans/epica1/tax/ejemplo/plantillas_de_producto.md`, y lo lleva a una realización práctica y escalable en Filament 4.

## 1) Núcleo del modelo de datos

- Catálogo global de atributos (`atributos`): listado canónico y reutilizable de características (key, labels multilenguaje, tipo y opciones cuando corresponda). Este catálogo es único y transversal a todas las plantillas, alineado con la sección 2.1–2.3 del “modelo de entidades” (dimensiones, agrupamientos y llaves como `primary_material`, `capacity_volume`, etc.).
- Grupos de atributos (`grupos_atributos`): representan las “Dimensiones de Especificación” (p. ej., `informacion_basica`, `atributos_centrales`, `visual_marca`, `textiles`, `embalaje_logistica`, `uso_ciclo_vida`, `comercial_abastecimiento`). Facilitan UI, orden y semántica.
- Plantillas de producto (`plantillas_producto`): definen el molde para un tipo de producto y referencian subcategorías del catálogo. Incluyen estado validado por CHECK (borrador/activa/obsoleta) para gobernanza.
- Pivot rico (`plantilla_atributo`): el corazón de la configuración. Para cada atributo en una plantilla, determina grupo, visibilidad, obligatoriedad, orden, componente UI sugerido, propiedades de UI, valores por defecto y reglas de validación adicionales. Esto materializa cómo, por ejemplo, la “Botella Térmica Estándar” del documento de ejemplos activa `capacity_volume` como `capacity_selector`, con opciones y ayudas de interfaz.
- Variantes de producto (`variantes_producto`): instancias transaccionales que guardan la “receta” concreta en `especificaciones` (JSONB). Se indexan con GIN para búsquedas eficientes (p. ej., filtrar por `primary_material`) y combinan índices BTREE para claves y estados.
- Versionado opcional (`plantilla_versiones` + `plantilla_version_atributo`): permite congelar configuraciones históricas sin romper variantes existentes, útil en operaciones con trazabilidad.

Decisiones de base de datos destacadas: se reemplazan ENUMs nativos de Postgres por `VARCHAR(50)` + `CHECK`, lo que simplifica migraciones; se validan tipos de atributo a nivel de BD; y se aplican índices adecuados (GIN solo sobre JSONB, BTREE en claves), mejorando rendimiento sin sacrificar flexibilidad.

## 2) Generación dinámica de formularios con Filament 4

La capa de modelos Eloquent y componentes Filament traduce la configuración del pivot en formularios y validaciones dinámicas:

- `PlantillaProducto` expone `atributos()` con metadatos del pivot y `atributosPorGrupo()` para organizar el formulario por secciones (una por grupo/dimensión). El método `getFilamentFormSchema()` construye y cachea el esquema completo, mejorando tiempos de render y manteniendo la UI consistente.
- `Atributo` conoce su tipo (vía PHP Backed Enums) y cómo renderizarse en Filament (`buildFilamentComponent()`), ya sea con componentes genéricos (`text`, `select`, `toggle`, etc.) o especializados del dominio (`material_selector`, `capacity_selector`, `dimension_input`, `gsm_slider`, `pantone_color`). Aplica `ui_props`, `default_value`, `placeholder`, `ayuda_ui` y `columna_span` desde el pivot.
- Reglas de validación: se combinan automáticamente tres capas: requerido/nullable desde el pivot, reglas por tipo (`string`, `integer`, `enum`, …) y reglas específicas adicionales (`reglas_validacion`). El resultado se mapea sobre `especificaciones.{key}` de la variante.

En Filament 4, esto se materializa en dos recursos principales:

- Recurso de Variantes: Al crear/editar, se elige una plantilla y se genera el formulario de especificaciones en vivo. Incluye generación de SKU, columnas de tabla que leen campos del JSONB (p. ej., `especificaciones.official_product_name`, `especificaciones.primary_material`) y filtros dinámicos usando JSONB. Las acciones contemplan duplicación de variantes y las operaciones CRUD habituales.
- Recurso de Plantillas (Template Builder): Para administradores. Un Repeater permite seleccionar atributos activos del catálogo, ubicarlos en un grupo, definir el componente UI, el orden, visibilidad, obligatoriedad, props y validaciones. Es la interfaz de “gobernanza” que lleva el catálogo canónico a instancias operativas de plantillas.

## 3) Correcciones clave y motivación

- Sin ENUMs de Postgres: migraciones más ágiles y sin locks, delegando la “seguridad de tipos” a PHP Enums + validaciones de app y CHECKs.
- Catálogo global de atributos: elimina duplicaciones, centraliza etiquetas y descripciones multilenguaje y garantiza consistencia entre plantillas.
- Índices e implementación Postgres: GIN solo donde aporta (JSONB), BTREE en claves y referencias; mejora tiempos de consulta y mantiene costo de escritura aceptable.
- API de Filament 4 actualizada: uso de `TextColumn::badge()` y patrones v4 nativos asegura compatibilidad y mejor DX.
- Versionado de plantillas: facilita cambios mayores en el tiempo, preservando coherencia con variantes emitidas.

## 4) Beneficios operativos

- Escalabilidad: nuevos atributos o ajustes de UI no exigen migraciones estructurales; basta con ampliar el catálogo o ajustar el pivot.
- Mantenibilidad: separación limpia entre catálogo (canónico) y configuración (por plantilla), con validación centralizada.
- Experiencia de usuario: formularios por “dimensión” comprensibles, componentes especializados y ayudas contextuales; filtros y listados que leen desde JSONB.
- Rendimiento: índices adecuados para consultas reales (por plantilla, estado, claves de especificación), caching de esquemas y uso prudente de JSONB.

## 5) Aterrizaje con los ejemplos de plantillas

Los ejemplos del documento de “Plantillas de Producto” muestran cómo la teoría se convierte en práctica:

- “Botella Térmica Estándar”: activa atributos de `informacion_basica`, `atributos_centrales` y `visual_marca`, asigna `capacity_volume` a un `capacity_selector` y `pantone_numbers` a un componente de color; define orden, obligatoriedad y props (como `multiple` en `printing_methods`).
- “Jockey Básico” y “Polera Algodón Premium”: explotan la dimensión `textiles` (p. ej., `fit_style`, `available_sizes`, `sizing_chart`). El pivot define selectores, archivos y campos numéricos con límites (como `gsm_slider`).
- Ítems de PDV (pendón, portafolletos, hablador): combinan `atributos_centrales` de dimensiones y espesores con `visual_marca` para impresión y sangrados, además de `embalaje_logistica` para embalajes y unidades por caja.

En todos los casos, la plantilla es solo la “configuración” y la variante guarda los valores reales en JSONB, habilitando reportabilidad y filtros ad‑hoc.

## 6) Consideraciones de implementación y gobernanza

- IDs y claves: se usan `BIGINT id()` por defecto en tablas y `foreignId()->constrained()`, sin UUID/ULID, cumpliendo las convenciones del proyecto.
- Semillas iniciales: poblar `grupos_atributos` según las siete dimensiones; cargar el catálogo `atributos` con las keys canónicas del modelo de entidades; y crear plantillas base con su configuración pivot.
- Validaciones y i18n: mantener `label_es`/`label_en` y descripciones para documentación viva del catálogo.
- Evolución: cuando cambie una plantilla activa, evaluar si corresponde versionado para no afectar variantes en curso.

---

En síntesis, la arquitectura final toma el diccionario de entidades y dimensiones del diseño canónico, lo cruza con ejemplos concretos de plantillas y lo plasma en una solución Filament 4 que es dinámica, gobernable y performante. El catálogo global garantiza consistencia semántica; el pivot rico convierte esa semántica en UI y reglas; y el JSONB de variantes ofrece la flexibilidad necesaria para operar a escala sin comprometer consultas ni mantenibilidad.

## Extensión: RFQ, Cotizaciones de Proveedores y Condiciones de Compra

Para cerrar el ciclo desde la especificación técnica hasta la adquisición, se incorpora una capa de sourcing que modela RFQs (solicitudes de cotización), cotizaciones de proveedores y el acuerdo adjudicado como snapshot.

- Tablas clave:
  - `proveedores`: registro maestro (identidad, contacto, país, estado activo).
  - `solicitudes_cotizacion` (RFQ): enlaza una `variante` (y/o `producto_proyecto`) con cantidad, moneda/incoterm objetivo, fechas y estado (`borrador|enviada|cerrada|adjudicada|cancelada`).
  - `rfq_proveedor` (pivot): invitaciones y seguimiento de respuesta por proveedor (`pendiente|enviada|aceptada|rechazada`).
  - `cotizaciones_proveedor`: respuesta del proveedor con precio, lead time, incoterm, condiciones de pago, adjuntos y `desviacion_especificacion` (JSONB por `key` de atributo).
  - `condiciones_de_compra`: snapshot de la cotización ganadora y de la variante al adjudicar; referencia a `producto_proyecto` y a la `cotizacion` ganadora; estado (`borrador|vigente|terminada|cancelada`).

- Relaciones operativas:
  - `VarianteProducto hasMany SolicitudDeCotizacion`; `SolicitudDeCotizacion hasMany CotizacionProveedor` y `belongsToMany Proveedor` via `rfq_proveedor`.
  - `ProductoDeProyecto hasOne CondicionesDeCompra`; `CondicionesDeCompra belongsTo CotizacionProveedor|Proveedor`.

- Flujo principal:
  1) Crear RFQ desde una variante o un producto de proyecto (con cantidad y condiciones objetivo) e invitar proveedores.
  2) Recibir cotizaciones, registrar precio, plazos y desviaciones vs. especificación.
  3) Comparar y adjudicar: marca la cotización como `ganadora`, cierra/adjudica la RFQ y crea `condiciones_de_compra` con snapshots de especificación y términos comerciales; enlaza el `producto_proyecto` al acuerdo.
  4) Ejecutar y cerrar el acuerdo (`vigente` → `terminada`).

- Decisiones técnicas:
  - Evitar ENUMs nativos de Postgres; usar `VARCHAR(50)` + `CHECK` para estados.
  - JSONB en `desviacion_especificacion` y snapshots; índices GIN para filtros por clave.
  - Índices BTREE por `rfq_id`, `proveedor_id`, `estado` y claves foráneas.

- UI en Filament 4:
  - `SolicitudDeCotizacionResource`: formulario con variante/cantidad, moneda/incoterm objetivo e invitados; acciones “Enviar RFQ” y “Cerrar RFQ”.
  - `CotizacionProveedorResource`: captura de precio/lead time/condiciones y `desviacion_especificacion`; acciones “Preseleccionar” y “Seleccionar ganadora”.
  - `CondicionesDeCompraResource`: muestra el snapshot adjudicado, referencias y estado; accesible desde `ProductoDeProyecto`.

Con esta extensión, la arquitectura cubre el ciclo completo: desde el catálogo y la definición de plantillas, pasando por la configuración de variantes, hasta la interacción con proveedores y la consolidación del acuerdo de compra, preservando trazabilidad y gobernanza.

## Alineación de Nomenclatura (Código/BD en Inglés)

Siguiendo `ia_contexto_nomenclatura_idioma.md`, el código (clases, tablas, columnas) debe estar en inglés y la UI/docs en español. Mapeo principal aplicado:

- Catálogo y plantillas:
  - `atributos` → `attributes` (cols: `key`, `label_es`, `label_en`, `tipo` → `type`, `opciones_json` → `options_json`)
  - `grupos_atributos` → `attribute_groups` (cols: `key`, `nombre_es`/`nombre_en` → `name_es`/`name_en`, `descripcion_*` → `description_*`, `icono` → `icon`, `orden` → `position`)
  - `plantillas_producto` → `product_templates` (cols: `nombre` → `name`, `subcategoria_id` → `subcategory_id`, `estado` → `status`, `descripcion` → `description`)
  - `plantilla_atributo` → `product_template_attributes` (cols: `plantilla_id` → `product_template_id`, `atributo_id` → `attribute_id`, `grupo_id` → `attribute_group_id`, `es_requerido` → `is_required`, `orden` → `position`, `reglas_validacion` → `validation_rules`, `ayuda_ui` → `help_text`, `seccion` → `section`, `columna_span` → `column_span`)
  - `variantes_producto` → `product_variants` (cols: `plantilla_id` → `product_template_id`, `sku_interno` → `internal_sku`, `estado` → `status`, `especificaciones` → `specifications`)

- Sourcing (RFQ y compras):
  - `proveedores` → `suppliers`
  - `solicitudes_cotizacion` → `rfqs` (cols: `unidad_medida` → `unit_of_measure`, `moneda_objetivo` → `target_currency`, `incoterm_objetivo` → `target_incoterm`, `puerto_objetivo` → `target_port`, `estado` → `status`, `fecha_envio` → `sent_at`, `fecha_limite` → `due_at`, `notas` → `notes`, `adjuntos_json` → `attachments_json`)
  - `rfq_proveedor` → `rfq_suppliers` (cols: `estado_invitacion` → `invitation_status`, `fecha_invitacion` → `invited_at`, `fecha_respuesta` → `responded_at`, `observaciones` → `remarks`)
  - `cotizaciones_proveedor` → `supplier_quotes` (cols: `precio_unitario` → `unit_price`, `moneda` → `currency`, `lead_time_produccion` → `production_lead_time`, `puerto_entrega` → `delivery_port`, `validez_hasta` → `valid_until`, `condiciones_pago` → `payment_terms`, `gastos_incluidos_json` → `included_costs_json`, `desviacion_especificacion` → `spec_deviation_json`, `estado` → `status`, `adjuntos_json` → `attachments_json`)
  - `condiciones_de_compra` → `purchase_conditions` (cols: `producto_proyecto_id` → `project_product_id`, `cotizacion_id` → `quote_id`, `proveedor_id` → `supplier_id`, `costo_unitario` → `unit_cost`, `moneda` → `currency`, `puerto_entrega` → `delivery_port`, `lead_time_produccion` → `production_lead_time`, `condiciones_pago` → `payment_terms`, `validez_hasta` → `valid_until`, `observaciones` → `remarks`, `snapshot_variante_json` → `variant_snapshot_json`, `snapshot_cotizacion_json` → `quote_snapshot_json`, `estado` → `status`, `adjudicada_at` → `awarded_at`)

- Estados (valores):
  - Plantillas: `draft|active|obsolete`
  - Variantes: `draft|finalized|obsolete`
  - RFQs: `draft|sent|closed|awarded|canceled`
  - Cotizaciones: `received|shortlisted|awarded|rejected`
  - Condiciones de compra: `draft|active|completed|canceled`

- Modelos/Enums (ejemplos):
  - `PlantillaProducto` → `ProductTemplate`, `Atributo` → `Attribute`, `GrupoAtributos` → `AttributeGroup`, `VarianteProducto` → `ProductVariant`.
  - `AtributoTipo` → `AttributeType`, `EstadoPlantilla` → `TemplateStatus`, `EstadoVariante` → `VariantStatus`.

La UI y la documentación mantienen etiquetas en español; los valores y nombres en código/BD se expresan en inglés según esta convención.
