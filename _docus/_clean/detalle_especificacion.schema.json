{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://example.com/schemas/product_attributes.schema.json", "title": "Atributos de producto", "type": "object", "additionalProperties": false, "$defs": {"nonEmptyString": {"type": "string", "minLength": 1, "defaultPlaceholder": "Texto de ejemplo"}, "positiveNumber": {"type": "number", "exclusiveMinimum": 0}, "nonNegativeNumber": {"type": "number", "minimum": 0}, "mm": {"type": "number", "minimum": 0, "description": "Milímetros (mm)", "defaultPlaceholder": "Ej: 150"}, "ml": {"type": "number", "minimum": 0, "description": "<PERSON><PERSON><PERSON><PERSON> (ml)", "defaultPlaceholder": "Ej: 500"}, "kg": {"type": "number", "minimum": 0, "description": "Kilogramos (kg)", "defaultPlaceholder": "Ej: 1.2"}, "percentage": {"type": "number", "minimum": 0, "maximum": 100, "description": "Porcentaje 0-100", "defaultPlaceholder": "Ej: 75"}, "dimensions2Dmm": {"type": "object", "additionalProperties": false, "properties": {"width_mm": {"$ref": "#/$defs/mm"}, "height_mm": {"$ref": "#/$defs/mm"}}, "required": ["width_mm", "height_mm"], "description": "Dimensiones 2D (ancho x alto) en milímetros"}, "dimensions3Dmm": {"type": "object", "additionalProperties": false, "properties": {"width_mm": {"$ref": "#/$defs/mm"}, "height_mm": {"$ref": "#/$defs/mm"}, "depth_mm": {"$ref": "#/$defs/mm"}}, "required": ["width_mm", "height_mm", "depth_mm"]}, "tolerancesDims": {"type": "object", "additionalProperties": false, "properties": {"width_mm": {"$ref": "#/$defs/mm"}, "height_mm": {"$ref": "#/$defs/mm"}, "depth_mm": {"$ref": "#/$defs/mm"}, "diameter_mm": {"$ref": "#/$defs/mm"}, "percent": {"$ref": "#/$defs/percentage"}}}, "specificPartDimensions": {"type": "array", "items": {"type": "object", "additionalProperties": false, "properties": {"part": {"$ref": "#/$defs/nonEmptyString"}, "width_mm": {"$ref": "#/$defs/mm"}, "height_mm": {"$ref": "#/$defs/mm"}, "depth_mm": {"$ref": "#/$defs/mm"}, "diameter_mm": {"$ref": "#/$defs/mm"}}, "required": ["part"]}}, "colorPantone": {"type": "string", "pattern": "^[A-Za-z0-9\\-]+(\\s[A-Za-z0-9\\-]+)*$", "description": "Código Pan<PERSON> o equivalente normalizado"}, "fileFormat": {"type": "string", "enum": ["ai", "pdf", "eps", "svg", "psd", "tiff", "png", "jpg"], "description": "Formato de archivo de arte"}, "countryCode": {"type": "string", "pattern": "^[A-Z]{2}$", "description": "Código de país ISO 3166-1 alpha-2"}, "currency": {"type": "string", "pattern": "^[A-Z]{3}$", "description": "Código de moneda ISO 4217"}}, "properties": {"information_basica_del_producto": {"type": "object", "additionalProperties": false, "properties": {"nombre_y_titulo_del_producto": {"type": "object", "additionalProperties": false, "properties": {"official_product_name": {"$ref": "#/$defs/nonEmptyString", "description": "Nombre oficial del producto", "defaultPlaceholder": "Ej: <PERSON><PERSON> 6 Paneles"}, "variant_name": {"type": ["string", "null"], "description": "Nombre de variante si aplica"}, "seo_friendly_title": {"type": ["string", "null"], "description": "Título SEO"}, "internal_sku": {"type": ["string", "null"], "description": "SKU interno"}}, "required": ["official_product_name"]}, "descripcion_del_producto": {"type": "object", "additionalProperties": false, "properties": {"short_marketing_description": {"type": ["string", "null"], "description": "Descripción breve", "defaultPlaceholder": "Ej: <PERSON><PERSON> de algodón suave y resistente."}, "long_detailed_description": {"type": ["string", "null"], "description": "Descripción detallada"}, "key_selling_points": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Puntos de venta clave"}, "target_audience": {"type": ["string", "null"], "description": "Público objetivo"}, "disclaimers": {"type": ["string", "null"], "description": "Advertencias / disclaimers"}}}}}, "atributos_centrales_del_producto": {"type": "object", "additionalProperties": false, "properties": {"material_composicion": {"type": "object", "additionalProperties": false, "properties": {"primary_material": {"$ref": "#/$defs/nonEmptyString", "description": "Material principal"}, "secondary_materials": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Materiales secundarios"}, "recycled_content_percentage": {"$ref": "#/$defs/percentage", "description": "% de contenido reciclado"}, "specific_material_grade": {"type": ["string", "null"], "description": "Grado específico de material"}, "material_origin": {"type": ["string", "null"], "description": "Origen del material"}}, "required": ["primary_material"]}, "tamano_dimensiones": {"type": "object", "additionalProperties": false, "properties": {"overall_external_dimensions": {"$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones externas"}, "internal_dimensions": {"$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones internas"}, "diameter": {"$ref": "#/$defs/mm", "description": "Diámetro (mm)"}, "capacity_volume": {"$ref": "#/$defs/ml", "description": "Capacidad volumétrica (ml)"}, "thickness": {"$ref": "#/$defs/mm", "description": "Grosor (mm)"}, "specific_part_dimensions": {"$ref": "#/$defs/specificPartDimensions", "description": "Dimensiones por parte"}, "dimensional_tolerances": {"$ref": "#/$defs/tolerancesDims", "description": "Tolerancias dimensionales"}, "folded_collapsed_dimensions": {"$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones plegado/colapsado"}}}, "peso_capacidad_de_carga": {"type": "object", "additionalProperties": false, "properties": {"net_weight": {"$ref": "#/$defs/kg", "description": "Peso neto (kg)"}, "maximum_load_capacity": {"$ref": "#/$defs/kg", "description": "Capacidad máxima de carga (kg)"}, "weight_distribution": {"type": ["string", "null"], "description": "Distribución de peso / notas"}}}, "gramaje_de_la_tela_gsm": {"type": "object", "additionalProperties": false, "properties": {"gsm_value": {"$ref": "#/$defs/nonNegativeNumber", "description": "Gramaje GSM"}, "gsm_tolerance": {"$ref": "#/$defs/percentage", "description": "Tolerancia GSM %"}, "thread_count": {"$ref": "#/$defs/nonNegativeNumber", "description": "Recuento de hilos"}}}, "tipo_de_ensamblaje_estructura": {"type": "object", "additionalProperties": false, "properties": {"assembly_required": {"type": "boolean", "description": "Requiere ensamblaje"}, "assembly_type": {"type": ["string", "null"], "description": "Tipo de ensamblaje"}, "joining_mechanisms": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Mecanismos de unión"}, "structural_design": {"type": ["string", "null"], "description": "Diseño estructural"}, "number_of_parts": {"$ref": "#/$defs/nonNegativeNumber", "description": "Número de partes"}}}}}, "especificaciones_visuales_y_marca": {"type": "object", "additionalProperties": false, "properties": {"metodo_tecnica_de_impresion": {"type": "object", "additionalProperties": false, "properties": {"printing_methods": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Métodos de impresión"}, "colors_per_print_location": {"type": "array", "items": {"$ref": "#/$defs/nonNegativeNumber"}, "description": "Colores por ubicación de impresión"}, "maximum_printable_area": {"$ref": "#/$defs/dimensions2Dmm", "description": "<PERSON>rea máxima imprimible (ancho x alto)"}, "ink_type": {"type": ["string", "null"], "description": "Tipo de tinta"}, "specialty_inks": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Tintas especiales"}, "embroidery_details": {"type": ["string", "null"], "description": "Detalles de bordado"}}}, "acabado_tratamiento_de_superficie": {"type": "object", "additionalProperties": false, "properties": {"surface_texture": {"type": ["string", "null"], "description": "Textura de superficie"}, "lamination_type": {"type": ["string", "null"], "description": "Tipo de laminado"}, "protective_coatings": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Recubrimientos protectores"}, "specific_treatments": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Tratamientos específicos"}}}, "opciones_de_color_concordancia_pantone": {"type": "object", "additionalProperties": false, "properties": {"standard_base_colors": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Colores base estándar"}, "custom_color_capability": {"type": "boolean", "description": "Soporta color personalizado"}, "pantone_numbers": {"type": "array", "items": {"$ref": "#/$defs/colorPantone"}, "description": "Códigos Pantone"}, "color_consistency_requirements": {"type": ["string", "null"], "description": "Requisitos de consistencia de color"}, "colorfastness_rating": {"$ref": "#/$defs/nonNegativeNumber", "description": "Solidez del color (escala definida)"}}}, "ubicacion_de_logo_areas_de_impresion": {"type": "object", "additionalProperties": false, "properties": {"imprint_locations": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Ubicaciones de impresión"}, "imprint_area_dimensions": {"type": "array", "items": {"$ref": "#/$defs/dimensions2Dmm"}, "description": "Dimensiones de área por ubicación (ancho x alto)"}, "placement_restrictions": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Restricciones de ubicación"}, "multiple_imprint_locations": {"type": "boolean", "description": "<PERSON><PERSON><PERSON>les ubicaciones"}}}, "etiquetado_tejido_impreso_otro": {"type": "object", "additionalProperties": false, "properties": {"label_type": {"type": ["string", "null"], "description": "Tipo de etiqueta (tejida/impresa/otra)"}, "label_material": {"type": ["string", "null"], "description": "Material de etiqueta"}, "label_dimensions": {"$ref": "#/$defs/dimensions2Dmm", "description": "Dimensiones de etiqueta (ancho x alto)"}, "label_information_content": {"type": ["string", "null"], "description": "Contenido informativo"}, "label_attachment_method": {"type": ["string", "null"], "description": "Método de fijación"}}}, "requisitos_de_archivos_de_arte_diseno": {"type": "object", "additionalProperties": false, "properties": {"accepted_file_formats": {"type": "array", "items": {"$ref": "#/$defs/fileFormat"}, "description": "Formatos aceptados"}, "minimum_resolution": {"$ref": "#/$defs/nonNegativeNumber", "description": "Resolución mínima (dpi)"}, "color_mode": {"type": "string", "enum": ["RGB", "CMYK", "PMS"], "description": "Modo de color"}, "vector_format_required": {"type": "boolean", "description": "Se requiere vector"}, "bleed_specifications": {"$ref": "#/$defs/mm", "description": "Sangrado requerido (mm)"}, "fonts_outlined": {"type": "boolean", "description": "<PERSON><PERSON><PERSON> con<PERSON>"}, "template_available": {"type": "boolean", "description": "Plantilla disponible"}}}}}, "caracteristicas_especificas_textiles": {"type": "object", "additionalProperties": false, "properties": {"corte_calce_patron_vestuario": {"type": "object", "additionalProperties": false, "properties": {"fit_style": {"type": ["string", "null"], "description": "Corte / calce"}, "garment_construction": {"type": ["string", "null"], "description": "Construcción de prenda"}, "fabric_pattern": {"type": ["string", "null"], "description": "Patrón de tela"}, "neckline_style": {"type": ["string", "null"], "description": "<PERSON><PERSON><PERSON> de <PERSON>"}, "sleeve_style": {"type": ["string", "null"], "description": "Tipo de manga"}, "hem_style": {"type": ["string", "null"], "description": "<PERSON><PERSON><PERSON><PERSON>"}, "specific_features": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Características específicas"}}}, "rango_de_tallas_ropa": {"type": "object", "additionalProperties": false, "properties": {"available_sizes": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Tallas disponibles"}, "sizing_chart": {"type": ["string", "null"], "description": "Tabla de tallas (URL o referencia)"}, "sizing_tolerance": {"$ref": "#/$defs/percentage", "description": "Tolerancia de tallaje %"}, "international_size_conversions": {"type": "object", "additionalProperties": {"$ref": "#/$defs/nonEmptyString"}, "description": "Conversiones internacionales"}}}, "instrucciones_de_cuidado": {"type": "object", "additionalProperties": false, "properties": {"washing_instructions": {"type": ["string", "null"], "description": "<PERSON><PERSON><PERSON>"}, "drying_instructions": {"type": ["string", "null"], "description": "Secado"}, "ironing_instructions": {"type": ["string", "null"], "description": "Planchado"}, "bleaching_instructions": {"type": ["string", "null"], "description": "Blanqueo"}, "dry_cleaning_instructions": {"type": ["string", "null"], "description": "Lavado en seco"}, "care_symbols": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Símbolos de cuidado"}}}}}, "embalaje_y_logistica": {"type": "object", "additionalProperties": false, "properties": {"tipo_de_embalaje_embalaje_unitario": {"type": "object", "additionalProperties": false, "properties": {"unit_packaging_description": {"type": ["string", "null"], "description": "Descripción del embalaje unitario"}, "unit_packaging_material": {"type": ["string", "null"], "description": "Material del embalaje unitario"}, "unit_packaging_dimensions": {"$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones del embalaje unitario"}, "unit_packaging_branding": {"type": ["string", "null"], "description": "Branding del embalaje unitario"}, "unit_packaging_closure": {"type": ["string", "null"], "description": "Cierre del embalaje unitario"}}}, "embalaje_interior": {"type": "object", "additionalProperties": false, "properties": {"units_per_inner_pack": {"$ref": "#/$defs/nonNegativeNumber", "description": "Unidades por embalaje interior"}, "inner_packaging_type": {"type": ["string", "null"], "description": "Tipo de embalaje interior"}, "inner_pack_dimensions": {"$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones embalaje interior"}, "inner_pack_labeling": {"type": ["string", "null"], "description": "Etiquetado embalaje interior"}}}, "embalaje_master_caja_master_de_despacho": {"type": "object", "additionalProperties": false, "properties": {"units_per_master_carton": {"$ref": "#/$defs/nonNegativeNumber", "description": "Unidades por caja master"}, "master_carton_material": {"type": ["string", "null"], "description": "Material caja master"}, "master_carton_dimensions": {"$ref": "#/$defs/dimensions3Dmm", "description": "Dimensiones caja master"}, "master_carton_gross_weight": {"$ref": "#/$defs/kg", "description": "Peso bruto caja master (kg)"}, "master_carton_net_weight": {"$ref": "#/$defs/kg", "description": "Peso neto caja master (kg)"}, "master_carton_markings": {"type": ["string", "null"], "description": "Marcaciones caja master"}, "palletization_info": {"type": ["string", "null"], "description": "Paletización"}}}, "cantidad_por_unidad_udm_unidad_de_medida": {"type": "object", "additionalProperties": false, "properties": {"unit_of_measure": {"type": "string", "enum": ["unit", "pair", "set", "kg", "m", "m2", "l"], "description": "UDM"}, "pricing_ordering_base": {"type": "string", "enum": ["per_unit", "per_inner_pack", "per_master_carton", "per_weight"], "description": "Base de precio/pedido"}}}, "dims_y_peso_de_despacho_est_caja_master": {"type": "object", "additionalProperties": false, "properties": {"estimated_shipping_dimensions": {"$ref": "#/$defs/dimensions3Dmm", "description": "Dims. estima<PERSON> de <PERSON> (caja master)"}, "estimated_shipping_weight": {"$ref": "#/$defs/kg", "description": "Peso estimado de despacho (kg)"}, "volumetric_weight": {"$ref": "#/$defs/kg", "description": "Peso volumétrico (kg)"}}}}}, "uso_y_ciclo_de_vida": {"type": "object", "additionalProperties": false, "properties": {"compatibilidad_de_uso_uso_previsto": {"type": "object", "additionalProperties": false, "properties": {"primary_intended_use": {"type": ["string", "null"], "description": "<PERSON><PERSON> previsto principal"}, "intended_environment": {"type": ["string", "null"], "description": "Ambiente previsto"}, "system_compatibility": {"type": ["string", "null"], "description": "Compatibilidad de sistema"}, "target_user_group": {"type": ["string", "null"], "description": "Grupo de usuario objetivo"}, "expected_lifespan": {"$ref": "#/$defs/nonNegativeNumber", "description": "Vida útil esperada (meses)"}}}, "vida_util_caducidad": {"type": "object", "additionalProperties": false, "properties": {"applicable_shelf_life": {"$ref": "#/$defs/nonNegativeNumber", "description": "Vida de estante aplicable (meses)"}, "expiry_date_requirements": {"type": ["string", "null"], "description": "Requisitos de fecha de caducidad"}, "batch_coding_requirements": {"type": ["string", "null"], "description": "Requisitos de codificación de lotes"}}}, "requisitos_de_almacenamiento": {"type": "object", "additionalProperties": false, "properties": {"storage_temperature": {"type": ["string", "null"], "description": "Temperatura de almacenamiento"}, "stacking_limitations": {"type": ["string", "null"], "description": "Limitaciones de apilamiento"}, "storage_sensitivities": {"type": ["string", "null"], "description": "Sensibilidades de almacenamiento"}}}}}, "comercial_y_abastecimiento": {"type": "object", "additionalProperties": false, "properties": {"costo_unitario_escalas_de_precios": {"type": "object", "additionalProperties": false, "properties": {"exw_unit_cost": {"$ref": "#/$defs/nonNegativeNumber", "description": "Costo unitario EXW"}, "fob_unit_cost": {"$ref": "#/$defs/nonNegativeNumber", "description": "Costo unitario FOB"}, "cif_unit_cost": {"$ref": "#/$defs/nonNegativeNumber", "description": "Costo unitario CIF"}, "volume_price_tiers": {"type": "array", "description": "Escalas de precio por volumen", "items": {"type": "object", "additionalProperties": false, "properties": {"min_qty": {"$ref": "#/$defs/nonNegativeNumber"}, "unit_price": {"$ref": "#/$defs/nonNegativeNumber"}, "currency": {"$ref": "#/$defs/currency"}}, "required": ["min_qty", "unit_price", "currency"]}}, "setup_charges": {"$ref": "#/$defs/nonNegativeNumber", "description": "Cargos de preparación"}}}, "cantidad_minima_de_pedido_moq": {"type": "object", "additionalProperties": false, "properties": {"moq_per_sku": {"$ref": "#/$defs/nonNegativeNumber", "description": "MOQ por SKU"}, "moq_per_order": {"$ref": "#/$defs/nonNegativeNumber", "description": "MOQ por orden"}}}, "informacion_del_proveedor": {"type": "object", "additionalProperties": false, "properties": {"preferred_supplier": {"type": ["string", "null"], "description": "Proveedor preferido"}, "supplier_part_number": {"type": ["string", "null"], "description": "Código de parte del proveedor"}, "country_of_origin": {"$ref": "#/$defs/countryCode", "description": "<PERSON><PERSON> de origen"}}}, "codigo_del_sistema_armonizado_hs_codigo_arancelario": {"type": "object", "additionalProperties": false, "properties": {"hs_code": {"type": ["string", "null"], "description": "Código HS"}, "country_specific_codes": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Códigos específicos por país"}}}}}, "certificaciones_y_requerimientos": {"type": "object", "additionalProperties": false, "properties": {"requerimientos_del_cliente": {"type": "object", "additionalProperties": false, "properties": {"client_specific_certifications": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Certificaciones del cliente"}, "client_audit_requirements": {"type": ["string", "null"], "description": "Requisitos de auditoría del cliente"}, "client_quality_standards": {"type": ["string", "null"], "description": "Estándares de calidad del cliente"}, "client_compliance_documents": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Documentos de cumplimiento del cliente"}}}, "requerimientos_regulatorios_por_pais": {"type": "object", "additionalProperties": false, "properties": {"country_specific_regulations": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Regulaciones por país"}, "dangerous_goods_classification": {"type": ["string", "null"], "description": "Clasificación mercancías peligrosas"}, "safety_certifications": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Certificaciones de seguridad"}, "sanitary_registrations": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Registros sanitarios"}}}, "requerimientos_internos": {"type": "object", "additionalProperties": false, "properties": {"internal_quality_standards": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Estándares internos de calidad"}, "internal_compliance_requirements": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Requisitos internos de compliance"}, "internal_documentation_requirements": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Documentación interna requerida"}, "internal_audit_requirements": {"type": ["string", "null"], "description": "Requisitos de auditoría interna"}}}, "certificaciones_de_materiales": {"type": "object", "additionalProperties": false, "properties": {"material_certifications": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Certificaciones de materiales"}}}, "certificaciones_de_cumplimiento_y_seguridad": {"type": "object", "additionalProperties": false, "properties": {"required_standards": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "<PERSON><PERSON>"}}}, "documentacion_de_certificacion": {"type": "object", "additionalProperties": false, "properties": {"testing_documents": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Documentos de pruebas/certificación (referencias)"}, "msds_required": {"type": "boolean", "description": "Requiere MSDS"}}}, "certificaciones_de_fabrica": {"type": "object", "additionalProperties": false, "properties": {"factory_audit_status": {"type": ["string", "null"], "description": "Estatus de auditoría de fábrica"}}}, "certificacion_imo_international_maritime_organization": {"type": "object", "additionalProperties": false, "properties": {"imo_certification": {"type": "boolean", "description": "Certificación IMO"}}}, "productos_para_ninos": {"type": "object", "additionalProperties": false, "properties": {"children_product": {"type": "boolean", "description": "Producto para niños"}, "children_safety_standards": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Normas de seguridad infantil"}, "age_appropriateness": {"$ref": "#/$defs/nonNegativeNumber", "description": "Edad recomendada mínima (años)"}}}, "productos_de_consumo_alimentos": {"type": "object", "additionalProperties": false, "properties": {"food_contact_product": {"type": "boolean", "description": "Producto de contacto con alimentos"}, "food_grade_certification": {"type": ["string", "null"], "description": "Certificación grado alimenticio"}, "food_safety_standards": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Normas de seguridad alimentaria"}}}, "productos_topicos_piel_pelo_etc": {"type": "object", "additionalProperties": false, "properties": {"topical_product": {"type": "boolean", "description": "Producto tópico"}, "dermatological_testing": {"type": ["string", "null"], "description": "Pruebas dermatológicas"}, "skin_safety_standards": {"type": "array", "items": {"$ref": "#/$defs/nonEmptyString"}, "description": "Normas de seguridad de piel"}}}, "niveles_de_aql_acceptable_quality_level": {"type": "object", "additionalProperties": false, "properties": {"aql_level": {"$ref": "#/$defs/nonNegativeNumber", "description": "Nivel AQL"}, "aql_inspection_level": {"type": ["string", "null"], "description": "Nivel de inspección AQL"}, "aql_sample_size": {"$ref": "#/$defs/nonNegativeNumber", "description": "Tamaño de muestra AQL"}, "quality_inspection_frequency": {"type": ["string", "null"], "description": "Frecuencia de inspección de calidad"}}}, "margen_de_error_y_saldos_de_produccion": {"type": "object", "additionalProperties": false, "properties": {"defect_percentage": {"$ref": "#/$defs/percentage", "description": "% de defecto permitido"}, "overage_quantity": {"$ref": "#/$defs/nonNegativeNumber", "description": "Cantidad de sobreproducción permitida"}, "production_buffer": {"$ref": "#/$defs/percentage", "description": "Buffer de producción %"}, "rework_percentage": {"$ref": "#/$defs/percentage", "description": "% de retrabajo permitido"}}}}}}, "required": ["information_basica_del_producto", "atributos_centrales_del_producto", "embalaje_y_logistica", "comercial_y_abastecimiento"]}