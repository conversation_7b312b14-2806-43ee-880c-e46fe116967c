# Arquitectura Final: Plantillas de Producto con Filament 4

**Recomendación Final**: Arquitectura Codex (A + B) mejorada con componentes específicos para Filament 4.

Esta propuesta combina lo mejor de la arquitectura Codex (catálogo global + pivot inteligente + JSONB) con mejoras específicas para el ecosistema Filament 4.

---

## **Modelo de Datos: Catálogo Global + Pivot Rico**

### 1. **Estructura de Tablas (EN en código/BD, ES en UI)**

```sql
-- Global attribute catalog (reusable)
CREATE TABLE attributes (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    key VARCHAR(100) UNIQUE NOT NULL,           -- e.g., 'primary_material'
    label_es VARCHAR(255) NOT NULL,
    label_en VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,                  -- 'string', 'integer', 'enum', 'boolean', etc.
    options_json JSONB DEFAULT NULL,            -- For enums: [{"key":"cotton","label_es":"Algodón","label_en":"Cotton","position":1}]
    description_es TEXT,
    description_en TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,

    CONSTRAINT chk_attributes_type CHECK (type IN ('string','integer','decimal','boolean','enum','text','date','email','url'))
);

-- Attribute groups (dimensions)
CREATE TABLE attribute_groups (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    key VARCHAR(100) UNIQUE NOT NULL,           -- 'informacion_basica', 'atributos_centrales', etc.
    name_es VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL,
    description_es TEXT,
    description_en TEXT,
    icon VARCHAR(100),
    position INTEGER DEFAULT 0,
    active BOOLEAN DEFAULT TRUE
);

-- Product templates
CREATE TABLE product_templates (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    name VARCHAR(255) NOT NULL,                 -- "Botella Térmica Estándar"
    subcategory_id BIGINT REFERENCES product_subcategories(id),
    status VARCHAR(50) DEFAULT 'draft',         -- 'draft', 'active', 'obsolete'
    description TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,

    CONSTRAINT chk_product_templates_status CHECK (status IN ('draft','active','obsolete'))
);

-- Rich pivot: template-specific attribute configuration
CREATE TABLE product_template_attributes (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    product_template_id BIGINT REFERENCES product_templates(id) ON DELETE CASCADE,
    attribute_id BIGINT REFERENCES attributes(id) ON DELETE RESTRICT,
    attribute_group_id BIGINT REFERENCES attribute_groups(id) ON DELETE RESTRICT,

    is_required BOOLEAN DEFAULT FALSE,
    visible BOOLEAN DEFAULT TRUE,
    position INTEGER DEFAULT 0,

    ui_component VARCHAR(100) NOT NULL,
    ui_props JSONB DEFAULT '{}',
    default_value JSONB DEFAULT NULL,

    validation_rules JSONB DEFAULT '{}',
    help_text TEXT,
    placeholder VARCHAR(255),

    section VARCHAR(100),
    column_span INTEGER DEFAULT 1,

    created_at TIMESTAMP,
    updated_at TIMESTAMP,

    CONSTRAINT chk_pta_column_span CHECK (column_span >= 1 AND column_span <= 12),
    UNIQUE(product_template_id, attribute_id)
);

-- Product variants with JSONB specs
CREATE TABLE product_variants (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    product_template_id BIGINT REFERENCES product_templates(id),
    internal_sku VARCHAR(255) UNIQUE,
    status VARCHAR(50) DEFAULT 'draft',         -- 'draft', 'finalized', 'obsolete'
    version INTEGER DEFAULT 1,

    specifications JSONB DEFAULT '{}',          -- {"primary_material":"cotton","capacity_volume":"500ml"}

    created_by INTEGER,
    updated_by INTEGER,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,

    CONSTRAINT chk_product_variants_status CHECK (status IN ('draft','finalized','obsolete'))
);

-- Optional template versioning
CREATE TABLE template_versions (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    product_template_id BIGINT REFERENCES product_templates(id),
    number INTEGER NOT NULL,
    published_at TIMESTAMP,
    active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE(product_template_id, number)
);

-- Pivot for versions
CREATE TABLE template_version_attributes (
    template_version_id BIGINT REFERENCES template_versions(id) ON DELETE CASCADE,
    attribute_id BIGINT REFERENCES attributes(id) ON DELETE RESTRICT,
    attribute_group_id BIGINT REFERENCES attribute_groups(id) ON DELETE RESTRICT,
    is_required BOOLEAN DEFAULT FALSE,
    visible BOOLEAN DEFAULT TRUE,
    position INTEGER DEFAULT 0,
    ui_component VARCHAR(100) NOT NULL,
    ui_props JSONB DEFAULT '{}',
    default_value JSONB DEFAULT NULL,
    validation_rules JSONB DEFAULT '{}',
    help_text TEXT,
    placeholder VARCHAR(255),
    section VARCHAR(100),
    column_span INTEGER DEFAULT 1,
    CONSTRAINT chk_tva_column_span CHECK (column_span >= 1 AND column_span <= 12),
    PRIMARY KEY(template_version_id, attribute_id)
);

-- Indexes
CREATE INDEX idx_attributes_key ON attributes(key);
CREATE INDEX idx_attributes_options_gin ON attributes USING GIN (options_json) WHERE options_json IS NOT NULL;
CREATE INDEX idx_pta_position ON product_template_attributes(product_template_id, attribute_group_id, position);
CREATE UNIQUE INDEX idx_pta_position_unique ON product_template_attributes(product_template_id, attribute_group_id, position) WHERE visible = TRUE;
CREATE INDEX idx_pta_visible ON product_template_attributes(product_template_id) WHERE visible = TRUE;
CREATE INDEX idx_pv_specs_gin ON product_variants USING GIN (specifications jsonb_path_ops);
CREATE INDEX idx_pv_template_status ON product_variants(product_template_id, status);
CREATE INDEX idx_pv_sku ON product_variants(internal_sku);
```

### 2. **Modelos Eloquent Optimizados (nombres en inglés)**

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Filament\Forms;
use Filament\Forms\Components\Component;
use App\Enums\AtributoTipo;

class ProductTemplate extends Model
{
    protected $table = 'product_templates';

    protected $fillable = ['name', 'subcategory_id', 'status', 'description'];

    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'product_template_attributes')
            ->withPivot([
                'attribute_group_id', 'is_required', 'visible', 'position',
                'ui_component', 'ui_props', 'default_value',
                'validation_rules', 'help_text', 'placeholder',
                'section', 'column_span'
            ])
            ->orderByPivot('position');
    }

    public function attributesByGroup(): Collection
    {
        return $this->attributes()
            ->get()
            ->groupBy('pivot.attribute_group_id')
            ->map->sortBy('pivot.position');
    }

    // Genera el schema de Filament dinámicamente
    public function getFilamentFormSchema(): array
    {
        $cacheKey = "plantilla_schema_{$this->id}_" . ($this->updated_at?->timestamp ?? 'new');

        return Cache::remember(
            $cacheKey,
            3600,
            fn() => $this->buildFormSchema()
        );
    }

    private function buildFormSchema(): array
    {
        $schema = [];

        foreach ($this->attributesByGroup() as $groupId => $attributes) {
            $grupoComponentes = [];

            foreach ($attributes as $attribute) {
                if ($attribute->pivot->visible) {
                    $grupoComponentes[] = $attribute->buildFilamentComponent();
                }
            }

            if (!empty($grupoComponentes)) {
                $grupo = AttributeGroup::find($groupId);
                $schema[] = Forms\Components\Section::make($grupo->name_es)
                    ->description($grupo->description_es)
                    ->icon($grupo->icon)
                    ->schema($grupoComponentes)
                    ->collapsible()
                    ->persistCollapsed();
            }
        }

        return $schema;
    }

    public function getValidationRules(): array
    {
        $rules = [];

        foreach ($this->attributes as $attribute) {
            $fieldRules = [];

            // Requerido por configuración del pivot
            if ($attribute->pivot->is_required) {
                $fieldRules[] = 'required';
            } else {
                $fieldRules[] = 'nullable';
            }

            // Reglas por tipo de atributo
            $fieldRules = array_merge($fieldRules, $attribute->getTypeValidationRules());

            // Reglas específicas del pivot (JSON → array)
            $pivotRules = $attribute->pivot->validation_rules ?? [];
            if (!is_array($pivotRules)) {
                $decoded = json_decode($pivotRules ?: '[]', true);
                $pivotRules = is_array($decoded) ? $decoded : [];
            }
            if (!empty($pivotRules)) {
                $fieldRules = array_merge($fieldRules, $pivotRules);
            }

            $rules["specifications.{$attribute->key}"] = $fieldRules;
        }

        return $rules;
    }
}

class Attribute extends Model
{
    protected $table = 'attributes';

    protected $fillable = ['key', 'label_es', 'label_en', 'type', 'options_json', 'description_es', 'description_en', 'active'];

    protected $casts = [
        'options_json' => 'array',
        'active' => 'boolean'
    ];

    // PHP Backed Enum para tipos
    public function getTypeEnum(): AttributeType
    {
        return AttributeType::from($this->type);
    }

    public function buildFilamentComponent(): Component
    {
        $component = $this->createBaseComponent();

        return $this->configureComponent($component);
    }

    private function createBaseComponent(): Component
    {
        return match($this->pivot->ui_component) {
            'text' => Forms\Components\TextInput::make("specifications.{$this->key}"),
            'textarea' => Forms\Components\Textarea::make("specifications.{$this->key}"),
            'number' => Forms\Components\TextInput::make("specifications.{$this->key}")->numeric(),
            'select' => Forms\Components\Select::make("specifications.{$this->key}"),
            'toggle' => Forms\Components\Toggle::make("specifications.{$this->key}"),
            'checkbox' => Forms\Components\Checkbox::make("specifications.{$this->key}"),
            'date' => Forms\Components\DatePicker::make("specifications.{$this->key}"),
            'file' => Forms\Components\FileUpload::make("specifications.{$this->key}"),
            'color' => Forms\Components\ColorPicker::make("specifications.{$this->key}"),
            'tags' => Forms\Components\TagsInput::make("specifications.{$this->key}"),

            // Componentes especializados para el dominio
            'material_selector' => $this->createMaterialSelector(),
            'dimension_input' => $this->createDimensionInput(),
            'capacity_selector' => $this->createCapacitySelector(),
            'gsm_slider' => $this->createGsmSlider(),
            'pantone_color' => $this->createPantoneColorPicker(),

            default => Forms\Components\TextInput::make("specifications.{$this->key}")
        };
    }

    private function configureComponent(Component $component): Component
    {
        $component = $component
            ->label($this->label_es)
            ->required($this->pivot->is_required)
            ->columnSpan($this->pivot->column_span ?? 1);

        // Placeholder
        if ($this->pivot->placeholder && method_exists($component, 'placeholder')) {
            $component = $component->placeholder($this->pivot->placeholder);
        }

        // Ayuda
        if ($this->pivot->ayuda_ui) {
            $component = $component->hint($this->pivot->ayuda_ui);
        }

        // Valor por defecto (JSON → escalar/array)
        if (isset($this->pivot->default_value)) {
            $default = $this->pivot->default_value;
            if (!is_scalar($default) && !is_array($default) && !is_null($default)) {
                $decoded = json_decode($default, true);
                $default = $decoded ?? $default;
            }
            $component = $component->default($default);
        }

        // Configurar opciones para selects
        if ($this->type === 'enum' && $this->options_json && method_exists($component, 'options')) {
            $options = collect($this->options_json)->pluck('label_es', 'key')->toArray();
            $component = $component->options($options);
        }

        // Aplicar propiedades específicas del UI (JSON → array)
        if (isset($this->pivot->ui_props)) {
            $uiProps = $this->pivot->ui_props;
            if (!is_array($uiProps)) {
                $decoded = json_decode($uiProps ?: '[]', true);
                $uiProps = is_array($decoded) ? $decoded : [];
            }
            $component = $this->applyUiProps($component, $uiProps);
        }

        return $component;
    }

    private function applyUiProps(Component $component, array $uiProps): Component
    {
        foreach ($uiProps as $prop => $value) {
            if (method_exists($component, $prop)) {
                $component = $component->$prop($value);
            }
        }

        return $component;
    }

    // Componentes especializados
    private function createMaterialSelector(): Component
    {
        return Forms\Components\Select::make("specifications.{$this->key}")
            ->options([
                'cotton' => 'Algodón 100%',
                'polyester' => 'Poliéster',
                'cotton_poly' => 'Algodón/Poliéster',
                'stainless_steel' => 'Acero Inoxidable',
                'aluminum' => 'Aluminio',
                'plastic' => 'Plástico',
                'wood' => 'Madera',
                'bamboo' => 'Bambú'
            ])
            ->searchable();
    }

    private function createDimensionInput(): Component
    {
        return Forms\Components\TextInput::make("specifications.{$this->key}")
            ->suffix('cm')
            ->placeholder('Ej: 15x25x8')
            ->rule('regex:/^\d+x\d+(x\d+)?$/');
    }

    private function createCapacitySelector(): Component
    {
        return Forms\Components\Select::make("specifications.{$this->key}")
            ->options([
                '250ml' => '250ml',
                '500ml' => '500ml',
                '750ml' => '750ml',
                '1000ml' => '1L',
                '1500ml' => '1.5L'
            ]);
    }

    private function createGsmSlider(): Component
    {
        return Forms\Components\TextInput::make("specifications.{$this->key}")
            ->numeric()
            ->suffix('GSM')
            ->minValue(80)
            ->maxValue(500)
            ->step(10);
    }

    private function createPantoneColorPicker(): Component
    {
        return Forms\Components\TextInput::make("specifications.{$this->key}")
            ->placeholder('Ej: PANTONE 186 C')
            ->prefix('PANTONE')
            ->rule('regex:/^[0-9]{1,4}\s?[CU]?$/');
    }

    public function getTypeValidationRules(): array
    {
        return match($this->type) {
            'string' => ['string', 'max:255'],
            'integer' => ['integer'],
            'decimal' => ['numeric'],
            'boolean' => ['boolean'],
            'enum' => $this->options_json ? ['in:' . collect($this->options_json)->pluck('key')->implode(',')] : [],
            'date' => ['date'],
            'email' => ['email'],
            'url' => ['url'],
            default => []
        };
    }
}

class VarianteProducto extends Model
{
    protected $table = 'variantes_producto';

    protected $fillable = ['plantilla_id', 'sku_interno', 'estado', 'version', 'especificaciones'];

    protected $casts = [
        'especificaciones' => 'array'
    ];

    public function plantilla(): BelongsTo
    {
        return $this->belongsTo(PlantillaProducto::class, 'plantilla_id');
    }

    // Scope para búsquedas en JSONB optimizado
    public function scopeConEspecificacion($query, string $key, $value)
    {
        return $query->whereRaw('especificaciones->> ? = ?', [$key, $value]);
    }

    public function scopeConEspecificacionNumericaEntre($query, string $key, $min, $max)
    {
        return $query->whereRaw("(especificaciones->>'{$key}')::numeric BETWEEN ? AND ?", [$min, $max]);
    }
}

class GrupoAtributos extends Model
{
    protected $table = 'grupos_atributos';

    protected $fillable = ['key', 'nombre_es', 'nombre_en', 'descripcion_es', 'descripcion_en', 'icono', 'orden', 'activo'];

    protected $casts = ['activo' => 'boolean'];
}
```

### 3. **Recursos Filament Optimizados**

```php
<?php

namespace App\Filament\Resources;

use App\Models\VarianteProducto;
use App\Models\PlantillaProducto;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class VarianteProductoResource extends Resource
{
    protected static ?string $model = VarianteProducto::class;
    protected static ?string $navigationIcon = 'heroicon-o-cube';
    protected static ?string $navigationGroup = 'Catálogo';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            // Información básica
            Forms\Components\Section::make('Información General')
                ->schema([
                    Forms\Components\Grid::make(2)
                        ->schema([
                            Forms\Components\Select::make('product_template_id')
                                ->label('Plantilla de Producto')
                                ->options(ProductTemplate::where('status', 'active')->pluck('name', 'id')->toArray())
                                ->required()
                                ->searchable()
                                ->live()
                                ->afterStateUpdated(function ($state, callable $set) {
                                    // Limpiar especificaciones al cambiar plantilla
                                    $set('specifications', []);

                                    // Generar SKU automático
                                    if ($state) {
                                        $template = ProductTemplate::find($state);
                                        $count = ProductVariant::where('product_template_id', $state)->count() + 1;
                                        $sku = strtoupper(substr($template->name, 0, 3)) . '-' . str_pad($count, 4, '0', STR_PAD_LEFT);
                                        $set('internal_sku', $sku);
                                    }
                                }),

                            Forms\Components\TextInput::make('internal_sku')
                                ->label('SKU Interno')
                                ->required()
                                ->unique(ignoreRecord: true)
                                ->alphaDash(),

                            Forms\Components\Select::make('status')
                                ->label('Estado')
                                ->options([
                                    'draft' => 'Borrador',
                                    'finalized' => 'Finalizada',
                                    'obsolete' => 'Obsoleta'
                                ])
                                ->default('draft'),

                            Forms\Components\TextInput::make('version')
                                ->label('Versión')
                                ->numeric()
                                ->default(1)
                                ->minValue(1),
                        ])
                ])
                ->collapsible(),

            // Especificaciones dinámicas
            Forms\Components\Group::make()
                ->schema(fn (callable $get) => self::getDynamicSpecificationSchema($get('product_template_id')))
                ->visible(fn (callable $get) => filled($get('product_template_id'))),
        ]);
    }

    protected static function getDynamicSpecificationSchema(?int $templateId): array
    {
        if (!$templateId) {
            return [
                Forms\Components\Placeholder::make('select_template')
                    ->label('')
                    ->content('Selecciona una plantilla para configurar las especificaciones del producto.')
            ];
        }

        $template = ProductTemplate::find($templateId);

        if (!$template) {
            return [];
        }

        return $template->getFilamentFormSchema();
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('internal_sku')
                    ->label('SKU')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\TextColumn::make('template.name')
                    ->label('Plantilla')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Estado')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'draft' => 'warning',
                        'finalized' => 'success',
                        'obsolete' => 'danger',
                        default => 'gray',
                    }),

                // Columnas dinámicas desde JSONB
                Tables\Columns\TextColumn::make('specifications.official_product_name')
                    ->label('Nombre del Producto')
                    ->searchable()
                    ->toggleable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('specifications.primary_material')
                    ->label('Material Principal')
                    ->badge()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('specifications.capacity_volume')
                    ->label('Capacidad')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('version')
                    ->label('Versión')
                    ->sortable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Actualizado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('product_template_id')
                    ->label('Plantilla')
                    ->options(ProductTemplate::pluck('name', 'id')->toArray())
                    ->searchable(),

                Tables\Filters\SelectFilter::make('estado')
                    ->options([
                        'borrador' => 'Borrador',
                        'finalizada' => 'Finalizada',
                        'obsoleta' => 'Obsoleta',
                    ]),

                // Filtros dinámicos por especificaciones JSONB
                Tables\Filters\Filter::make('material')
                    ->form([
                        Forms\Components\Select::make('primary_material')
                            ->label('Material Principal')
                            ->options([
                                'cotton' => 'Algodón',
                                'polyester' => 'Poliéster',
                                'stainless_steel' => 'Acero Inoxidable',
                                'aluminum' => 'Aluminio',
                            ])
                            ->multiple(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['primary_material'],
                            fn (Builder $query, $materials): Builder => $query->whereIn('specifications->primary_material', $materials)
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                // Acción personalizada para duplicar variante
                Tables\Actions\Action::make('duplicate')
                    ->label('Duplicar')
                    ->icon('heroicon-o-document-duplicate')
                    ->action(function (ProductVariant $record) {
                        $newVariant = $record->replicate();
                        $newVariant->internal_sku = $record->internal_sku . '-COPY';
                        $newVariant->status = 'draft';
                        $newVariant->version = 1;
                        $newVariant->save();

                        return redirect(ProductVariantResource::getUrl('edit', ['record' => $newVariant]));
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
```

### 4. **Template Builder para Administradores**

```php
<?php

namespace App\Filament\Resources;

use App\Models\PlantillaProducto;
use App\Models\Atributo;
use App\Models\GrupoAtributos;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;

class ProductTemplateResource extends Resource
{
    protected static ?string $model = ProductTemplate::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Administración';

    public static function form(Forms\Form $form): Forms\Form
    {
        return $form->schema([
            Forms\Components\Section::make('Información Básica')
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->label('Nombre de la Plantilla')
                        ->required()
                        ->maxLength(255),

                    Forms\Components\Select::make('subcategory_id')
                        ->label('Subcategoría')
                        ->relationship('subcategory', 'name')
                        ->required()
                        ->searchable(),

                    Forms\Components\Select::make('status')
                        ->label('Estado')
                        ->options([
                            'draft' => 'Borrador',
                            'active' => 'Activa',
                            'obsolete' => 'Obsoleta',
                        ])
                        ->default('draft'),

                    Forms\Components\Textarea::make('description')
                        ->label('Descripción')
                        ->rows(3),
                ]),

            Forms\Components\Section::make('Configuración de Atributos')
                ->schema([
                    Forms\Components\Repeater::make('attributesConfig')
                        ->label('Atributos de la Plantilla')
                        ->relationship('attributes')
                        ->schema([
                            Forms\Components\Grid::make(3)
                                ->schema([
                                    Forms\Components\Select::make('id')
                                        ->label('Atributo')
                                        ->options(Attribute::where('active', true)->pluck('label_es', 'id')->toArray())
                                        ->required()
                                        ->searchable()
                                        ->reactive()
                                        ->afterStateUpdated(function ($state, callable $set) {
                                            if ($state) {
                                                $atributo = Atributo::find($state);
                                                $set('ui_component', $atributo->tipo === 'enum' ? 'select' : 'text');
                                            }
                                        }),

                                    Forms\Components\Select::make('grupo_id')
                                        ->label('Grupo')
                                        ->options(GrupoAtributos::where('activo', true)->pluck('nombre_es', 'id')->toArray())
                                        ->required(),

                                    Forms\Components\Select::make('ui_component')
                                        ->label('Componente UI')
                                        ->options([
                                            'text' => 'Texto',
                                            'textarea' => 'Área de Texto',
                                            'number' => 'Número',
                                            'select' => 'Selección',
                                            'toggle' => 'Interruptor',
                                            'checkbox' => 'Casilla',
                                            'date' => 'Fecha',
                                            'file' => 'Archivo',
                                            'color' => 'Color',
                                            'tags' => 'Etiquetas',
                                            'material_selector' => 'Selector de Material',
                                            'dimension_input' => 'Dimensiones',
                                            'capacity_selector' => 'Selector de Capacidad',
                                            'gsm_slider' => 'GSM',
                                            'pantone_color' => 'Color Pantone',
                                        ])
                                        ->required(),
                                ]),

                            Forms\Components\Grid::make(3)
                                ->schema([
                                    Forms\Components\Toggle::make('es_requerido')
                                        ->label('Requerido'),

                                    Forms\Components\Toggle::make('visible')
                                        ->label('Visible')
                                        ->default(true),

                                    Forms\Components\TextInput::make('orden')
                                        ->label('Orden')
                                        ->numeric()
                                        ->default(0),
                                ]),

                            Forms\Components\Grid::make(2)
                                ->schema([
                                    Forms\Components\TextInput::make('placeholder')
                                        ->label('Placeholder'),

                                    Forms\Components\TextInput::make('columna_span')
                                        ->label('Columnas')
                                        ->numeric()
                                        ->minValue(1)
                                        ->maxValue(12)
                                        ->default(1)
                                        ->rules(['integer', 'min:1', 'max:12']),
                                ]),

                            Forms\Components\Textarea::make('ayuda_ui')
                                ->label('Texto de Ayuda')
                                ->rows(2),

                            Forms\Components\KeyValue::make('ui_props')
                                ->label('Propiedades UI')
                                ->helperText('Configuración específica del componente (JSON)'),

                            Forms\Components\TagsInput::make('validation_rules')
                                ->label('Reglas de Validación')
                                ->helperText('Reglas adicionales de validación Laravel (ej: min:1, max:100)')
                                ->placeholder('Agregar regla...'),
                        ])
                        ->collapsible()
                        ->itemLabel(function (array $state): ?string {
                            if (!isset($state['id'])) return null;
                            $attribute = Attribute::find($state['id']);
                            return $attribute?->label_es;
                        })
                        ->reorderable('position')
                        ->addActionLabel('Agregar Atributo'),
                ])
                ->collapsible(),
        ]);
    }

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nombre')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('subcategory.name')
                    ->label('Subcategoría')
                    ->searchable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'draft' => 'warning',
                        'active' => 'success',
                        'obsolete' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('attributes_count')
                    ->label('Atributos')
                    ->counts('attributes')
                    ->badge(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Actualizado')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('preview')
                    ->label('Vista Previa')
                    ->icon('heroicon-o-eye')
                    ->url(fn (ProductTemplate $record): string =>
                        ProductVariantResource::getUrl('create', ['product_template_id' => $record->id])
                    ),
            ]);
    }
}
```

## **Correcciones Implementadas (Feedback de Codex)**

### 1. **Eliminación de ENUMs PostgreSQL**
- ❌ `ENUM('borrador', 'activa')` → ✅ `VARCHAR(50)` + `CHECK` constraints
- ❌ `ENUM('string', 'integer')` → ✅ `VARCHAR(50)` + PHP Backed Enums
- **Ventaja**: Migraciones más flexibles, sin lock de tablas

### 2. **Catálogo Global de Atributos**
- ❌ `attributes(template_id, ...)` → ✅ `atributos(id, key, ...)` + pivot `plantilla_atributo`
- **Ventaja**: Reutilización entre plantillas, gobernanza centralizada

### 3. **Índices GIN Corregidos**
- ❌ `GIN (type)` en VARCHAR → ✅ `GIN (opciones_json)` solo en JSONB
- ❌ `GIN` en campos no-JSON → ✅ `BTREE` en claves y referencias
- **Ventaja**: Performance optimizada según tipo de dato

### 4. **Filament 4 API Actualizada**
- ❌ `BadgeColumn::make()` → ✅ `TextColumn::make()->badge()`
- ❌ API v2/v3 → ✅ API v4 nativa
- **Ventaja**: Compatibilidad con versión actual

### 5. **Versionado de Plantillas**
- ✅ Agregado `plantilla_versiones` y `plantilla_version_atributo`
- **Ventaja**: Trazabilidad histórica, no rompe variantes existentes

## **Ventajas de esta Arquitectura Corregida**

### 1. **Escalabilidad Empresarial**
- ✅ Catálogo global reutilizable reduce duplicación
- ✅ Pivot rico permite personalización por plantilla
- ✅ JSONB optimizado para búsquedas complejas
- ✅ Versionado para trazabilidad histórica

### 2. **Performance PostgreSQL**
- ✅ Índices GIN para búsquedas JSONB eficientes
- ✅ Índices BTREE en claves y referencias
- ✅ Constraints CHECK para validación DB
- ✅ Caching de esquemas de formularios

### 3. **Experiencia Filament 4**
- ✅ Formularios dinámicos nativos
- ✅ Componentes especializados para el dominio
- ✅ UI reactiva con validación en tiempo real
- ✅ API v4 actualizada

### 4. **Mantenibilidad**
- ✅ Separación clara entre catálogo y configuración
- ✅ Validación centralizada y consistente
- ✅ PHP Backed Enums para type safety
- ✅ Documentación automática a través del código

### 5. **Flexibilidad**
- ✅ Nuevos tipos de atributos sin migración
- ✅ Componentes UI especializados extensibles
- ✅ Búsquedas ad-hoc en especificaciones JSONB
- ✅ Migraciones de esquema sin locks

## **Migración Laravel Equivalente**

```php
<?php
// Migration: create_product_taxonomy_tables.php

use Illuminate\Support\Facades\DB;

Schema::create('attributes', function (Blueprint $table) {
    $table->id();
    $table->string('key', 100)->unique();
    $table->string('label_es');
    $table->string('label_en');
    $table->string('type', 50);
    $table->json('options_json')->nullable();
    $table->text('description_es')->nullable();
    $table->text('description_en')->nullable();
    $table->boolean('active')->default(true);
    $table->timestamps();
});
DB::statement("ALTER TABLE attributes ADD CONSTRAINT chk_attributes_type CHECK (type IN ('string','integer','decimal','boolean','enum','text','date','email','url'));");

Schema::create('attribute_groups', function (Blueprint $table) {
    $table->id();
    $table->string('key', 100)->unique();
    $table->string('name_es');
    $table->string('name_en');
    $table->text('description_es')->nullable();
    $table->text('description_en')->nullable();
    $table->string('icon', 100)->nullable();
    $table->integer('position')->default(0);
    $table->boolean('active')->default(true);
    $table->timestamps();
});

Schema::create('product_templates', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->foreignId('subcategory_id')->constrained('product_subcategories');
    $table->string('status', 50)->default('draft');
    $table->text('description')->nullable();
    $table->timestamps();
});
DB::statement("ALTER TABLE product_templates ADD CONSTRAINT chk_product_templates_status CHECK (status IN ('draft','active','obsolete'));");

Schema::create('product_template_attributes', function (Blueprint $table) {
    $table->id();
    $table->foreignId('product_template_id')->constrained('product_templates')->cascadeOnDelete();
    $table->foreignId('attribute_id')->constrained('attributes')->restrictOnDelete();
    $table->foreignId('attribute_group_id')->constrained('attribute_groups')->restrictOnDelete();

    $table->boolean('is_required')->default(false);
    $table->boolean('visible')->default(true);
    $table->integer('position')->default(0);

    $table->string('ui_component', 100);
    $table->json('ui_props')->default('{}');
    $table->json('default_value')->nullable();
    $table->json('validation_rules')->default('{}');
    $table->text('help_text')->nullable();
    $table->string('placeholder')->nullable();
    $table->string('section', 100)->nullable();
    $table->integer('column_span')->default(1);
    $table->timestamps();

    $table->unique(['product_template_id', 'attribute_id']);
});
DB::statement("ALTER TABLE product_template_attributes ADD CONSTRAINT chk_pta_column_span CHECK (column_span >= 1 AND column_span <= 12);");

Schema::create('product_variants', function (Blueprint $table) {
    $table->id();
    $table->foreignId('product_template_id')->constrained('product_templates');
    $table->string('internal_sku')->unique();
    $table->string('status', 50)->default('draft');
    $table->integer('version')->default(1);
    $table->json('specifications')->default('{}');
    $table->foreignId('created_by')->nullable();
    $table->foreignId('updated_by')->nullable();
    $table->timestamps();
});
DB::statement("ALTER TABLE product_variants ADD CONSTRAINT chk_product_variants_status CHECK (status IN ('draft','finalized','obsolete'));");

// Indexes
Schema::table('attributes', function (Blueprint $table) {
    $table->index('key');
});
DB::statement("CREATE INDEX idx_attributes_options_gin ON attributes USING GIN (options_json) WHERE options_json IS NOT NULL;");

Schema::table('product_template_attributes', function (Blueprint $table) {
    $table->index(['product_template_id', 'attribute_group_id', 'position']);
});
DB::statement("CREATE UNIQUE INDEX idx_pta_position_unique ON product_template_attributes(product_template_id, attribute_group_id, position) WHERE visible = TRUE;");

Schema::table('product_variants', function (Blueprint $table) {
    $table->index(['product_template_id', 'status']);
    $table->index('internal_sku');
});
DB::statement("CREATE INDEX idx_pv_specs_gin ON product_variants USING GIN (specifications jsonb_path_ops);");
```

## **PHP Backed Enums (Adición recomendada, nombres en inglés)**

```php
<?php

namespace App\Enums;

enum AttributeType: string
{
    case STRING = 'string';
    case INTEGER = 'integer';
    case DECIMAL = 'decimal';
    case BOOLEAN = 'boolean';
    case ENUM = 'enum';
    case TEXT = 'text';
    case DATE = 'date';
    case EMAIL = 'email';
    case URL = 'url';

    public function label(): string
    {
        return match($this) {
            self::STRING => 'Texto',
            self::INTEGER => 'Número Entero',
            self::DECIMAL => 'Número Decimal',
            self::BOOLEAN => 'Verdadero/Falso',
            self::ENUM => 'Selección',
            self::TEXT => 'Texto Largo',
            self::DATE => 'Fecha',
            self::EMAIL => 'Correo Electrónico',
            self::URL => 'URL',
        };
    }

    public function validationRules(): array
    {
        return match($this) {
            self::STRING => ['string', 'max:255'],
            self::INTEGER => ['integer'],
            self::DECIMAL => ['numeric'],
            self::BOOLEAN => ['boolean'],
            self::TEXT => ['string'],
            self::DATE => ['date'],
            self::EMAIL => ['email'],
            self::URL => ['url'],
            self::ENUM => [], // Se resuelve dinámicamente con opciones
        };
    }
}

enum TemplateStatus: string
{
    case DRAFT = 'draft';
    case ACTIVE = 'active';
    case OBSOLETE = 'obsolete';

    public function label(): string
    {
        return match($this) {
            self::DRAFT => 'Borrador',
            self::ACTIVE => 'Activa',
            self::OBSOLETE => 'Obsoleta',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::DRAFT => 'warning',
            self::ACTIVE => 'success',
            self::OBSOLETE => 'danger',
        };
    }
}

enum VariantStatus: string
{
    case DRAFT = 'draft';
    case FINALIZED = 'finalized';
    case OBSOLETE = 'obsolete';

    public function label(): string
    {
        return match($this) {
            self::DRAFT => 'Borrador',
            self::FINALIZED => 'Finalizada',
            self::OBSOLETE => 'Obsoleta',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::DRAFT => 'warning',
            self::FINALIZED => 'success',
            self::OBSOLETE => 'danger',
        };
    }
}
```

## **Resumen Final**

Esta arquitectura final implementa **todas las correcciones sugeridas por Codex**:

✅ **Eliminó ENUMs PostgreSQL** → VARCHAR + CHECK constraints
✅ **Catálogo global de atributos** → Reutilización entre plantillas
✅ **Índices GIN corregidos** → Solo en campos JSONB
✅ **API Filament 4 actualizada** → TextColumn::badge() en lugar de BadgeColumn
✅ **Versionado de plantillas** → Trazabilidad histórica completa
✅ **PHP Backed Enums** → Type safety en aplicación

La propuesta combina la robustez empresarial de Codex con la experiencia de usuario optimizada de Filament 4, proporcionando una base sólida, escalable y mantenible para el sistema de plantillas de producto.

---

## Extensión: RFQ, Cotizaciones de Proveedores y Condiciones de Compra

Esta sección amplía el modelo para cubrir el ciclo de sourcing: creación de RFQs, recepción de cotizaciones, evaluación y adjudicación con snapshot del acuerdo.

### 1. Tablas SQL (PostgreSQL) — Código/BD en inglés

```sql
-- Suppliers (master)
CREATE TABLE suppliers (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    legal_name VARCHAR(255) NOT NULL,
    tax_id VARCHAR(100),
    contact_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(100),
    country VARCHAR(100),
    rating INTEGER,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Variant procurement (purchase context per variant)
CREATE TABLE variant_procurements (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    product_variant_id BIGINT REFERENCES product_variants(id) ON DELETE RESTRICT,
    project_id BIGINT NULL,
    project_product_id BIGINT NULL,
    quantity NUMERIC(18,4) NOT NULL,
    unit_of_measure VARCHAR(50) NOT NULL,
    target_currency VARCHAR(10) NOT NULL,
    target_incoterm VARCHAR(10) NOT NULL,
    target_port VARCHAR(100),
    required_by TIMESTAMP NULL,
    delivery_window VARCHAR(100) NULL,
    status VARCHAR(50) DEFAULT 'draft', -- draft|rfq|awarded|po_issued|closed|canceled
    notes TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    CONSTRAINT chk_variant_procurements_status CHECK (status IN ('draft','rfq','awarded','po_issued','closed','canceled'))
);

-- RFQs
CREATE TABLE rfqs (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    variant_procurement_id BIGINT REFERENCES variant_procurements(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'draft',
    sent_at TIMESTAMP,
    due_at TIMESTAMP,
    notes TEXT,
    attachments_json JSONB,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    CONSTRAINT chk_rfqs_status CHECK (status IN ('draft','sent','closed','awarded','canceled'))
);

-- Invitations per supplier
CREATE TABLE rfq_suppliers (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    rfq_id BIGINT REFERENCES rfqs(id) ON DELETE CASCADE,
    supplier_id BIGINT REFERENCES suppliers(id) ON DELETE RESTRICT,
    invitation_status VARCHAR(50) DEFAULT 'pending',
    invited_at TIMESTAMP,
    responded_at TIMESTAMP,
    remarks TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    CONSTRAINT uniq_rfq_supplier UNIQUE(rfq_id, supplier_id),
    CONSTRAINT chk_rfq_inv_status CHECK (invitation_status IN ('pending','sent','accepted','rejected'))
);

-- Supplier quotes
CREATE TABLE supplier_quotes (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    rfq_id BIGINT REFERENCES rfqs(id) ON DELETE CASCADE,
    supplier_id BIGINT REFERENCES suppliers(id) ON DELETE RESTRICT,
    unit_price NUMERIC(18,4) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    moq INTEGER,
    production_lead_time INTEGER, -- days
    incoterm VARCHAR(10),
    delivery_port VARCHAR(100),
    valid_until TIMESTAMP,
    payment_terms VARCHAR(255),
    included_costs_json JSONB,
    spec_deviation_json JSONB, -- { key: comment/alternative }
    notes TEXT,
    status VARCHAR(50) DEFAULT 'received',
    attachments_json JSONB,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    CONSTRAINT chk_supplier_quotes_status CHECK (status IN ('received','shortlisted','awarded','rejected'))
);
CREATE INDEX idx_sq_rfq ON supplier_quotes(rfq_id);
CREATE INDEX idx_sq_supplier ON supplier_quotes(supplier_id);
CREATE INDEX idx_sq_status ON supplier_quotes(status);
CREATE INDEX idx_sq_specdev_gin ON supplier_quotes USING GIN (spec_deviation_json);

CREATE TABLE purchase_conditions (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    variant_procurement_id BIGINT REFERENCES variant_procurements(id) ON DELETE RESTRICT,
    quote_id BIGINT REFERENCES supplier_quotes(id) ON DELETE RESTRICT,
    supplier_id BIGINT REFERENCES suppliers(id) ON DELETE RESTRICT,
    unit_cost NUMERIC(18,4) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    incoterm VARCHAR(10),
    delivery_port VARCHAR(100),
    production_lead_time INTEGER,
    payment_terms VARCHAR(255),
    valid_until TIMESTAMP,
    remarks TEXT,
    variant_snapshot_json JSONB,
    quote_snapshot_json JSONB,
    status VARCHAR(50) DEFAULT 'draft',
    awarded_at TIMESTAMP,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    CONSTRAINT chk_purchase_conditions_status CHECK (status IN ('draft','active','completed','canceled'))
);
CREATE UNIQUE INDEX uniq_pc_per_variant_proc ON purchase_conditions(variant_procurement_id) WHERE status IN ('draft','active');
```

Índices adicionales recomendados: `solicitudes_cotizacion(variante_id, estado)`, `rfq_proveedor(rfq_id, estado_invitacion)`.

### 2. Migración Laravel equivalente (fragmentos, nombres en inglés)

```php
// En una migración dedicada a sourcing
Schema::create('suppliers', function (Blueprint $table) {
    $table->id();
    $table->string('legal_name');
    $table->string('tax_id')->nullable();
    $table->string('contact_name')->nullable();
    $table->string('email')->nullable();
    $table->string('phone')->nullable();
    $table->string('country')->nullable();
    $table->integer('rating')->nullable();
    $table->boolean('active')->default(true);
    $table->timestamps();
});

Schema::create('variant_procurements', function (Blueprint $table) {
    $table->id();
    $table->foreignId('product_variant_id')->constrained('product_variants');
    $table->foreignId('project_id')->nullable();
    $table->foreignId('project_product_id')->nullable();
    $table->decimal('quantity', 18, 4);
    $table->string('unit_of_measure', 50);
    $table->string('target_currency', 10);
    $table->string('target_incoterm', 10);
    $table->string('target_port', 100)->nullable();
    $table->timestamp('required_by')->nullable();
    $table->string('delivery_window', 100)->nullable();
    $table->string('status', 50)->default('draft');
    $table->text('notes')->nullable();
    $table->timestamps();
});
DB::statement("ALTER TABLE variant_procurements ADD CONSTRAINT chk_variant_procurements_status CHECK (status IN ('draft','rfq','awarded','po_issued','closed','canceled'));");

Schema::create('rfqs', function (Blueprint $table) {
    $table->id();
    $table->foreignId('variant_procurement_id')->constrained('variant_procurements')->cascadeOnDelete();
    $table->string('status', 50)->default('draft');
    $table->timestamp('sent_at')->nullable();
    $table->timestamp('due_at')->nullable();
    $table->text('notes')->nullable();
    $table->json('attachments_json')->nullable();
    $table->timestamps();
});
DB::statement("ALTER TABLE rfqs ADD CONSTRAINT chk_rfqs_status CHECK (status IN ('draft','sent','closed','awarded','canceled'));");

Schema::create('rfq_suppliers', function (Blueprint $table) {
    $table->id();
    $table->foreignId('rfq_id')->constrained('rfqs')->cascadeOnDelete();
    $table->foreignId('supplier_id')->constrained('suppliers')->restrictOnDelete();
    $table->string('invitation_status', 50)->default('pending');
    $table->timestamp('invited_at')->nullable();
    $table->timestamp('responded_at')->nullable();
    $table->text('remarks')->nullable();
    $table->timestamps();
    $table->unique(['rfq_id', 'supplier_id']);
});
DB::statement("ALTER TABLE rfq_suppliers ADD CONSTRAINT chk_rfq_inv_status CHECK (invitation_status IN ('pending','sent','accepted','rejected'));");

Schema::create('supplier_quotes', function (Blueprint $table) {
    $table->id();
    $table->foreignId('rfq_id')->constrained('rfqs')->cascadeOnDelete();
    $table->foreignId('supplier_id')->constrained('suppliers')->restrictOnDelete();
    $table->decimal('unit_price', 18, 4);
    $table->string('currency', 10);
    $table->integer('moq')->nullable();
    $table->integer('production_lead_time')->nullable();
    $table->string('incoterm', 10)->nullable();
    $table->string('delivery_port', 100)->nullable();
    $table->timestamp('valid_until')->nullable();
    $table->string('payment_terms', 255)->nullable();
    $table->json('included_costs_json')->nullable();
    $table->json('spec_deviation_json')->nullable();
    $table->text('notes')->nullable();
    $table->string('status', 50)->default('received');
    $table->json('attachments_json')->nullable();
    $table->timestamps();
    $table->index(['rfq_id']);
    $table->index(['supplier_id']);
    $table->index(['status']);
});
DB::statement("ALTER TABLE supplier_quotes ADD CONSTRAINT chk_supplier_quotes_status CHECK (status IN ('received','shortlisted','awarded','rejected'));");
DB::statement("CREATE INDEX idx_sq_specdev_gin ON supplier_quotes USING GIN (spec_deviation_json);");

Schema::create('purchase_conditions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('variant_procurement_id')->constrained('variant_procurements');
    $table->foreignId('quote_id')->constrained('supplier_quotes');
    $table->foreignId('supplier_id')->constrained('suppliers');
    $table->decimal('unit_cost', 18, 4);
    $table->string('currency', 10);
    $table->string('incoterm', 10)->nullable();
    $table->string('delivery_port', 100)->nullable();
    $table->integer('production_lead_time')->nullable();
    $table->string('payment_terms', 255)->nullable();
    $table->timestamp('valid_until')->nullable();
    $table->text('remarks')->nullable();
    $table->json('variant_snapshot_json')->nullable();
    $table->json('quote_snapshot_json')->nullable();
    $table->string('status', 50)->default('draft');
    $table->timestamp('awarded_at')->nullable();
    $table->integer('version')->default(1);
    $table->timestamps();
});
DB::statement("ALTER TABLE purchase_conditions ADD CONSTRAINT chk_purchase_conditions_status CHECK (status IN ('draft','active','completed','canceled'));");
DB::statement("CREATE UNIQUE INDEX uniq_pc_per_variant_proc ON purchase_conditions(variant_procurement_id) WHERE status IN ('draft','active');");
```

### 3. Relaciones y flujo en Filament (resumen)

- `VariantProcurementResource` (paso dentro de la variante o como recurso aparte):
  - Form: detalles de compra para la variante (cantidad, UoM, incoterm/moneda/puerto objetivo, fechas requeridas).
  - Acciones: “Crear RFQ” a partir de esta orden de variante.

- `SolicitudDeCotizacionResource`:
  - Form: ligado a `variant_procurement_id`; invitar proveedores (Repeater). Acciones: “Enviar RFQ”, “Cerrar RFQ”.
  - Tabla: estado, fechas, número de cotizaciones recibidas y atajos a detalle.

- `CotizacionProveedorResource`:
  - Form: `precio_unitario`, `moneda`, `lead_time_produccion`, `incoterm`, `condiciones_pago`, `desviacion_especificacion` (map por `key`), adjuntos.
  - Acciones: “Preseleccionar”, “Seleccionar ganadora”. Al adjudicar, se crea `condiciones_de_compra` con snapshots y se actualiza la RFQ y el `producto_proyecto`.

- `CondicionesDeCompraResource`:
  - Show/Form: términos adjudicados, snapshots, estado; acción para marcar `vigente`/`terminada`.

Reglas: adjudicar requiere al menos una cotización `recibida`; transacción atómica para crear el acuerdo, actualizar estados y enlazar `variant_procurement`.

### 4. Beneficios de la extensión

- Trazabilidad de extremo a extremo: especificación → RFQ → cotizaciones → acuerdo adjudicado.
- Gobernanza y comparabilidad: desviaciones declaradas por `key` y normalización de precios/condiciones.
- Flexibilidad y performance: JSONB con índices GIN donde corresponde; estados con `CHECK` sin ENUMs.

### 5. CostObligation y Payments (resumen)

```sql
-- Obligaciones de costo (FOB a nivel Variant Procurement; otros costos se prorratean en ProjectProduct)
CREATE TABLE cost_obligations (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    variant_procurement_id BIGINT REFERENCES variant_procurements(id) ON DELETE RESTRICT,
    project_product_id BIGINT NULL, -- para roll-up y prorrateos
    supplier_id BIGINT REFERENCES suppliers(id) ON DELETE RESTRICT,

    description VARCHAR(255),
    amount NUMERIC(18,4) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    cost_type VARCHAR(30) NOT NULL,  -- 'FOB','MOLD','SAMPLE','SHIPPING','CUSTOMS','INSURANCE','HANDLING','OTHER','CREDIT'
    status VARCHAR(20) NOT NULL DEFAULT 'PROVISIONED', -- 'PROVISIONED','COMMITTED','APPROVED','CANCELED'
    invoice_reference VARCHAR(100),

    committed_at TIMESTAMP NULL,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,

    CONSTRAINT chk_cost_obligations_type CHECK (cost_type IN ('FOB','MOLD','SAMPLE','SHIPPING','CUSTOMS','INSURANCE','HANDLING','OTHER','CREDIT')),
    CONSTRAINT chk_cost_obligations_status CHECK (status IN ('PROVISIONED','COMMITTED','APPROVED','CANCELED'))
);
CREATE INDEX idx_co_variant ON cost_obligations(variant_procurement_id);
CREATE INDEX idx_co_supplier ON cost_obligations(supplier_id);

-- Pagos y asignaciones (desacoplados)
CREATE TABLE payments (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    paid_at TIMESTAMP NOT NULL,
    method VARCHAR(50),
    reference VARCHAR(100),
    amount NUMERIC(18,4) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE payment_allocations (
    payment_id BIGINT REFERENCES payments(id) ON DELETE CASCADE,
    cost_obligation_id BIGINT REFERENCES cost_obligations(id) ON DELETE RESTRICT,
    amount NUMERIC(18,4) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    fx_rate_at_payment NUMERIC(18,8),
    PRIMARY KEY(payment_id, cost_obligation_id)
);

-- Costos de excepción (pérdidas)
CREATE TABLE exception_costs (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    project_id BIGINT,
    project_product_id BIGINT NULL,
    reason_type VARCHAR(30) NOT NULL, -- 'SUPPLIER_INCIDENT','CUSTOMER_CANCELLATION','OTHER'
    amount NUMERIC(18,4) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    reference VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

Relaciones (simplificadas)

```
Project
  └─ ProjectProduct (1..n)
      └─ ProductVariant (1..n)
          └─ VariantProcurement (1..n) ── RFQ ── SupplierQuote ── PurchaseConditions
               └─ CostObligation (FOB)
                    └─ PaymentAllocation ── Payment

Costos compartidos (SHIPPING/CUSTOMS/INSURANCE/HANDLING) → prorrateo a ProjectProduct
ExceptionCost → a nivel Project / ProjectProduct (pérdidas)
```
