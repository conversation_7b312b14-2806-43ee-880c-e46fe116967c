# Modelo Conceptual para un Escenario "Made to Order"

Este documento describe un modelo conceptual optimizado para un negocio "Made to Order", donde cada producto fabricado es único y no se reutiliza, como en el caso de productos con branding específico del cliente.

La clave del modelo es la simplicidad y la contención de la especificación única dentro del contexto del proyecto donde se crea.

### Nomenclatura Utilizada

*   **Plantilla de Producto:** El producto base o abstracto, proveniente de un catálogo. Define los atributos personalizables.
*   **ProductoDeProyecto:** La instancia concreta y única de un producto para un proyecto específico. Contiene los valores de los atributos (la especificación) y el contexto de negocio (cantidad, costo).
*   **Ítem de Cotización:** La línea principal en el documento de cotización, que puede agrupar varios `ProductosDeProyecto`.

---

### El Modelo Conceptual Aplicado al Ejemplo

**Escenario:** Una cotización con 1 ítem que agrupa dos configuraciones de gorra: 500 amarillas con logo impreso y 300 rojas con logo bordado.

#### Paso 1: Definir la Plantilla de Producto

El catálogo contiene la estructura base del producto.

*   **Plantilla de Producto:** "Gorra Textil 3 Paneles"
    *   Define los **Atributos** que se pueden personalizar:
        *   `Color` (Tipo: Texto)
        *   `Técnica de Logo` (Tipo: Opción Múltiple)
        *   `Archivo del Logo` (Tipo: Archivo)

#### Paso 2: Construir la Cotización y las Especificaciones al Vuelo

Aquí es donde el modelo simplificado actúa. Las especificaciones se crean directamente en el contexto de la cotización, sin una entidad de "Variante" intermedia en el catálogo.

1.  Se crea una **Cotización** para el proyecto.
2.  Se añade un único **Ítem de Cotización**:
    *   **Descripción:** "Surtido de Gorras Textiles 3 Paneles"
    *   **Cantidad Total:** 800

3.  Este `Ítem de Cotización` agrupa dos **`ProductosDeProyecto`**, cada uno con su propia cantidad y especificación:

    *   **ProductoDeProyecto 1:**
        *   **Cantidad:** 500
        *   **Especificaciones:**
            ```json
            {
              "color": "Amarillo",
              "tecnica_logo": "Impreso",
              "archivo_logo": "ruta/al/logo_cliente.ai"
            }
            ```

    *   **ProductoDeProyecto 2:**
        *   **Cantidad:** 300
        *   **Especificaciones:**
            ```json
            {
              "color": "Rojo",
              "tecnica_logo": "Bordado",
              "archivo_logo": "ruta/al/logo_cliente.ai"
            }
            ```

### Resumen del Modelo

```
Cotización
└── Ítem de Cotización ("Surtido de Gorras", Cant. Total: 800)
    ├── ProductoDeProyecto 1 (Cantidad: 500)
    │   ├── Especificación: {Color: Amarillo, Técnica: Impreso, ...}
    │   └── Usa la -> Plantilla de Producto ("Gorra")
    │
    └── ProductoDeProyecto 2 (Cantidad: 300)
        ├── Especificación: {Color: Rojo, Técnica: Bordado, ...}
        └── Usa la -> Plantilla de Producto ("Gorra")
```

### Justificación del Diseño

Este modelo se elige específicamente porque el negocio es **"Made to Order"** y los productos, al tener un branding único por cliente, **nunca se repiten**.

*   **Ventaja - Simplicidad:** Se elimina la entidad intermedia de una variante reutilizable, ya que no aporta valor. La especificación vive donde se necesita: en el `ProductoDeProyecto`.
*   **Compromiso - Análisis de Datos:** Se asume que la necesidad de analizar y agregar datos a través de especificaciones de productos pasados es secundaria frente a la eficiencia operativa del día a día.

