# Nuevo Modelo (Codex): Consolidación comercial con variantes configurables

Este documento define un modelo de datos pensado para Laravel + PostgreSQL donde:
- Las entidades del sistema usan nombres en inglés (tablas/modelos).
- Los términos de negocio y las explicaciones se presentan en español.

Objetivo: consolidar la negociación a nivel de producto del proyecto, sin perder granularidad por configuración (variante), costos y logística.

---

## Principios de diseño (negocio)
- Consolidación comercial a nivel de producto del proyecto (mejor precio, una sola negociación u OC si aplica).
- Granularidad operativa por variante (cantidad, configuración, precio específico opcional).
- Configuraciones flexibles usando JSONB con índices GIN para búsquedas eficientes.
- Trazabilidad desde oferta → OC → entregas, con reglas de estado que protegen cambios.

---

## Entidades del sistema (English) y propósito (ES)

- `customers`
  - Propósito: cliente que contrata los proyectos comerciales.
  - Campos: `id`, `name`, `tax_id`, `contact_info` JSONB, `status`.

- `projects`
  - Propósito: contexto del proyecto comercial (customer, fechas, estado).
  - Campos clave: `id`, `name`, `customer_id`, `status`, `starts_at`, `ends_at`.

- `product_categories`
  - Propósito: categoría principal de productos (p.e. "Textiles", "Electrónicos", "Promocionales").
  - Campos: `id`, `name`, `description`, `status`.

- `product_subcategories`
  - Propósito: subcategoría específica dentro de una categoría (p.e. "Gorras", "Camisetas", "Tazas").
  - Campos: `id`, `product_category_id` (FK), `name`, `description`, `status`.

- `product_templates`
  - Propósito: plantilla base (tipo/categoría) desde la cual se configuran variantes (p.e. "jockey_logo_bordado").
  - Campos: `id`, `name`, `product_subcategory_id` (FK), `status`, `metadata` JSONB.

- `suppliers`
  - Propósito: proveedor con el que se negocia o emite OC.
  - Campos: `id`, `name`, `tax_id`, `contact_info` JSONB.

- `product_projects`
  - Propósito (Producto del Proyecto): contenedor comercial consolidado dentro de un proyecto.
  - Campos: `id`, `project_id` (FK), `product_template_id` (FK), `supplier_id` (nullable), `total_quantity`, `avg_unit_price` (nullable), `currency`, `status` (draft, quoted, approved, po_issued, in_production, received, closed), `notes`.

- `product_variants`
  - Propósito (Variantes): instancias específicas con configuración y cantidad.
  - Campos: `id`, `product_project_id` (FK), `name` (nullable), `quantity`, `configuration` JSONB, `generated_sku` UNIQUE, `estimated_unit_price` (nullable), `suggested_supplier_id` (nullable), `delivery_window` (nullable), `variant_status`.
  - Índices: `USING GIN (configuration)`.

 - `procurement_lots`
  - Propósito (Lote de Abastecimiento mono‑variante): negociar una sola `product_variant` y, al adjudicar una cotización, fijar proveedor/precio/condiciones (snapshot). Base para crear el ítem de OC y dividir en sublotes de producción/entrega si aplica.
  - Campos: `id`, `product_project_id` (FK), `product_variant_id` (FK, NOT NULL), `supplier_id` (FK, nullable hasta adjudicar), `awarded_quote_id` (FK, nullable), `awarded_at` (nullable), `quantity`, `agreed_unit_price` (nullable hasta adjudicar), `incoterm` (nullable), `lead_time_days` (nullable), `currency` (nullable), `terms` (nullable), `status` (draft, rfq_open, quoted, awarded, closed).
  - Reglas: 1 `procurement_lot` = 1 `product_variant`. Unicidad opcional para evitar lotes paralelos abiertos (ver Índices).

 - `procurement_groups`
  - Propósito (Grupo de Consolidación Comercial): consolidar demanda de múltiples `product_projects` (mismo proveedor/ventana) para negociar precio por volumen.
  - Campos: `id`, `name`, `supplier_id` (FK), `currency`, `status` (draft, rfq_sent, quoted, awarded, closed), `valid_from`, `valid_to`, `notes`.

 - `procurement_group_items`
  - Propósito: demanda concreta agregada al grupo (cada fila referencia un lote mono‑variante y su cantidad requerida).
  - Campos: `id`, `procurement_group_id` (FK), `procurement_lot_id` (FK), `requested_quantity`.

 - `pricing_agreements`
  - Propósito (Acuerdos de Precio por Volumen): registrar la cotización adjudicada del proveedor y sus tramos.
  - Campos: `id`, `procurement_group_id` (FK), `supplier_id` (FK), `status` (active, expired), `valid_from`, `valid_to`, `notes`.

 - `pricing_agreement_tiers`
  - Propósito: tramos de precio por volumen aplicables a los ítems.
  - Campos: `id`, `pricing_agreement_id` (FK), `min_qty`, `max_qty` (nullable), `unit_price`.

 - `supplier_quotes`
  - Propósito (Cotizaciones por lote): gestionar cotizaciones por proveedor para un `procurement_lot` (mono‑variante) y adjudicar una.
  - Campos: `id`, `procurement_lot_id` (FK), `supplier_id` (FK), `status` (draft, sent, received, shortlisted, awarded, rejected, expired), `unit_price`, `currency`, `incoterm`, `lead_time_days`, `valid_until` (nullable), `attachments` JSONB, `notes`.

 - `group_supplier_quotes` (opcional)
  - Propósito (Cotizaciones por grupo): gestionar cotizaciones con tramos por volumen a nivel `procurement_group` antes de convertirlas en `pricing_agreements`.
  - Campos: `id`, `procurement_group_id` (FK), `supplier_id` (FK), `status` (draft, sent, received, shortlisted, awarded, rejected, expired), `valid_until` (nullable), `notes`.

 - `group_supplier_quote_tiers`
  - Propósito: tramos de una cotización de grupo.
  - Campos: `id`, `group_supplier_quote_id` (FK), `min_qty`, `max_qty` (nullable), `unit_price`.

- `purchase_orders`
  - Propósito (Orden de Compra): OC emitida al proveedor (puede consolidar múltiples variantes/paquetes del mismo proveedor).
  - Campos: `id`, `project_id` (FK), `supplier_id` (FK), `currency`, `status` (issued, accepted, in_production, shipped, closed), `notes`.

 - `purchase_order_items`
  - Propósito: detalle de la OC por variante (derivado de un lote mono‑variante).
  - Campos: `id`, `purchase_order_id` (FK), `procurement_lot_id` (FK), `product_variant_id` (FK), `quantity`, `unit_price`.

 - `production_lots`
  - Propósito (Lotes de Producción): subdividir un `purchase_order_item` para planificación y trazabilidad hasta la entrega al forwarder.
  - Campos: `id`, `purchase_order_item_id` (FK), `planned_qty`, `status` (planned, in_production, ready, shipped, received, closed), `eta` (nullable), `planned_start/finish`, `actual_start/finish`, `tracking_code`, `factory_tracking_code` (nullable), `qc_status` (pending, ok, reject, rework), `handed_to_forwarder_at` (nullable), `notes`.

 - `shipments`
  - Propósito (Embarques internacionales): cabecera del envío (ocean/air), documentos y hitos.
  - Campos: `id`, `reference` (BL/AWB/interno), `mode` (ocean, air), `incoterm`, `forwarder_id` (FK suppliers), `vessel`, `flight`, `origin_port`, `destination_port`, `etd`, `eta`, `status` (booking, cargo_received, loaded, departed, arrived, cleared, delivered_to_dc, closed), `docs` JSON, `notes`.

 - `shipment_items`
  - Propósito: pivot que vincula `production_lots` con un `shipment` y su cantidad/embalaje.
  - Campos: `id`, `shipment_id` (FK), `production_lot_id` (FK), `qty`, `cartons` (nullable), `cbm` (nullable), `weight_kg` (nullable).

 - `local_distributions`
  - Propósito (Distribución doméstica): cabecera de entregas locales desde DC/warehouse.
  - Campos: `id`, `carrier_id` (FK suppliers), `warehouse_origin`, `window_start`, `window_end`, `status` (planned, picking, out_for_delivery, delivered, closed), `docs` JSON, `notes`.

 - `local_distribution_items`
  - Propósito: pivot que vincula `shipment_items` (o directamente `production_lots`) con una distribución local y su cantidad.
  - Campos: `id`, `local_distribution_id` (FK), `shipment_item_id` (nullable, FK), `production_lot_id` (nullable, FK), `qty`.

 - `cost_items`
  - Propósito (Costos extra): flete, setup, moldes, impuestos. Se pueden asociar al producto, al paquete o a la OC.
  - Campos: `id`, `applies_to` ENUM('product_project','procurement_lot','procurement_group','purchase_order'), `applies_id`, `type` (freight, setup, tooling, tax, other), `amount`, `currency`, `notes`.

Notas:
- Claves primarias: bigint autoincrementales (Laravel `id()` y `foreignId()->constrained()`).
- Nada de UUID/ULID, consistente con las guías del repositorio.

---

## Relaciones (resumen)
- `customers` 1–N `projects`.
- `product_categories` 1–N `product_subcategories`.
- `product_subcategories` 1–N `product_templates`.
- `projects` 1–N `product_projects`.
- `product_projects` 1–N `product_variants`.
- `product_projects` 1–N `procurement_lots` (cada lote refiere a una `product_variant`).
 - `procurement_lots` 1–N `supplier_quotes` y 1–1 `purchase_order_items` (mapeo directo al emitir OC). Un `supplier_quote` adjudicado se refleja en el lote.
 - `procurement_groups` 1–N `procurement_group_items` (cada item refiere a un `procurement_lot` de cualquier `product_project`).
 - `group_supplier_quotes` 1–N `group_supplier_quote_tiers`; al adjudicar, se crea `pricing_agreements` 1–N `pricing_agreement_tiers`.
 - `pricing_agreements` 1–N `pricing_agreement_tiers`; `purchase_order_items` referencia el `pricing_agreement_tier` aplicado (snapshot) cuando el precio proviene del acuerdo por tramos.
 - `purchase_orders` (por `project` + `supplier`) 1–N `purchase_order_items` (cada ítem refiere a una `product_variant`).
 - `purchase_order_items` 1–N `production_lots`.
 - `shipments` 1–N `shipment_items` (cada item refiere a un `production_lot`).
 - `local_distributions` 1–N `local_distribution_items` (cada item refiere a un `shipment_item` o a un `production_lot`).
 - `cost_items` se asocian polimórficamente a `product_projects`, `procurement_lots`, `procurement_groups`, `purchase_orders`, `shipments` o `local_distributions`.

---

## Reglas e invariantes (negocio)
- Consistencia de cantidades: suma de `product_variants.quantity` = `product_projects.total_quantity`.
- Precio promedio del producto (cuando existan precios cerrados): `product_projects.avg_unit_price` = promedio ponderado por cantidad desde `purchase_order_items` (o desde `procurement_lots` adjudicados si aún no hay OC).
- Consolidación comercial cross‑project: cuando un lote participa en un `procurement_group` con acuerdo adjudicado, su precio se determina por el `pricing_agreement_tier` vigente (snapshot) según el volumen consolidado del grupo.
- Inmutabilidad por estado:
  - Tras `po_issued` en `product_projects` o `issued` en `purchase_orders`, no se puede modificar `configuration` ni `quantity` en `product_variants` sin un proceso de cambio controlado.
  - Estados avanzan en secuencia válida; no se puede retroceder sin autorización.
- `generated_sku` es único en el sistema (o, si se prefiere, único por proyecto).
 - Producción vs logística: `production_lots` modelan hasta “entregado al forwarder” (`handed_to_forwarder_at` y vínculo con `shipments`). `shipments` cubren tránsito internacional y `local_distributions` la última milla.

---

## Índices y performance
- JSONB: `CREATE INDEX idx_variants_configuration ON product_variants USING GIN (configuration jsonb_path_ops);`
- Claves foráneas compuestas frecuentes: índices BTREE (`product_project_id`, `supplier_id`).
- Unicidad: `generated_sku` UNIQUE.
- Política de lotes abiertos (Opción B elegida):
  - Un solo `procurement_lot` abierto por `product_variant_id` (la multicotización ocurre en `supplier_quotes`).
  - Índice parcial (PostgreSQL):
    - `CREATE UNIQUE INDEX uniq_open_lot_per_variant ON procurement_lots(product_variant_id) WHERE status IN ('draft','rfq_open','quoted');`
  - Nota: al excluir `awarded`, se permite abrir un nuevo lote tras adjudicar el anterior.
- Unicidad para cotizaciones activas por lote/proveedor: `CREATE UNIQUE INDEX uniq_open_quote_per_lot_supplier ON supplier_quotes(procurement_lot_id, supplier_id) WHERE status IN ('draft','sent','received','shortlisted');`

---

## Validación y esquema de configuración
- La `configuration` de `product_variants` debe validar contra el esquema de la `product_templates` (diccionario de atributos). Validación en aplicación (Laravel) + constraints de datos donde sea posible.
- Reglas típicas: enums restringidos, rangos, obligatorios por tipo de plantilla.

---

## Flujo de negocio (ejemplo breve)
- Proyecto “TechInnovate Summit 2024” crea un `product_project` para “jockey_logo_bordado”, `total_quantity`=100.
- Se definen dos `product_variants`:
  - V1: 30 u, `configuration`={panel_count:"6_panels", primary_material:"cotton", base_color:"green"}, `estimated_unit_price`=13.00, `generated_sku`="JCK-6P-ALG-VRD-001".
  - V2: 70 u, `configuration`={panel_count:"5_panels", primary_material:"polyester", base_color:"red"}, `estimated_unit_price`=11.50, `generated_sku`="JCK-5P-POL-ROJ-001".
- Se crean 2 `procurement_lots` (uno por variante). Se reciben varias `supplier_quotes` por lote; se adjudica una por cada lote (fijando proveedor, precio y condiciones en snapshot).
- Alternativamente (multi‑proyecto), se adjudica un `group_supplier_quote` con tramos → se crea `pricing_agreements`. Al emitir OCs, cada ítem toma el tier aplicable.
- Se emite una `purchase_order` consolidando V1 y V2 con sus precios acordados.
- Se registran `deliveries` parciales hasta completar 100 u.
- `avg_unit_price` = (30*13 + 70*11.5)/100 = 11.95; costos extra (`cost_items`) pueden prorratearse para costo final por unidad.

### Flujo de consolidación comercial (multi‑proyecto)
- Proyecto A agrega V1 (30) y V2 (70) y Proyecto B agrega V3 (100) a un `procurement_group` con proveedor “CapMaster Pro”. Total: 200 u.
- El proveedor cotiza con tramos (`pricing_agreement_tiers`): 1–99: 13.00; 100–199: 12.20; 200+: 11.80. Se adjudica → `pricing_agreements` activo.
- Cada proyecto emite su `purchase_order`; cada `purchase_order_item` toma el precio del `pricing_agreement_tier` aplicable (200 → 11.80) y guarda snapshot.
- Opcional: dividir `purchase_order_items` en `production_lots` para entregas parcializadas; `delivery_items` pueden referenciar el `production_lot_id`.

---

## Consultas útiles (orientativas)
- Filtrar por configuración: `WHERE configuration->>'base_color' = 'red'` (usa índice GIN).
- Avance logístico de una variante: sumar cantidades en `shipment_items` y `local_distribution_items` asociados a los `production_lots` del `purchase_order_item`.
- Costeo consolidado del `product_project`: suma ponderada de `purchase_order_items` + prorrateo de `cost_items` asociados.

---

## Notas de implementación (Laravel + PostgreSQL)
- Usar casts JSON en Eloquent para `configuration`.
- Migraciones: `jsonb` para configuraciones, crear índices GIN y UNIQUE para `generated_sku`.
- Políticas/estados: implementar una pequeña state machine para transiciones válidas y bloqueos de edición.
- Sin UUID/ULID: usar `id()` y `foreignId()->constrained()`.
- Tests (Pest):
  - Validar suma de cantidades VS total.
  - Calcular `avg_unit_price` ponderado.
  - Bloquear cambios de variantes después de `po_issued`.

---

## Mapeo rápido término negocio ↔ entidad
- Producto del proyecto → `product_projects`
- Variante de producto → `product_variants`
- Lote de abastecimiento → `procurement_lots`
- Grupo de consolidación comercial → `procurement_groups` / `procurement_group_items`
- Acuerdo de precio → `pricing_agreements` / `pricing_agreement_tiers`
- Orden de compra (OC) → `purchase_orders` / `purchase_order_items`
- Lote de producción → `production_lots`
- Embarque internacional → `shipments` / `shipment_items`
- Distribución local → `local_distributions` / `local_distribution_items`
- Costos extra → `cost_items`

Este modelo equilibra simplicidad operativa, consolidación comercial y trazabilidad detallada por configuración, costos y logística.

---

## Costeo de proyecto complejo (ejemplo)

Escenario
- Proveedor: CapMaster Pro (USD).
- `procurement_groups` (consolidación): Gorras bordadas.
- Tramos (`pricing_agreements`): 1–99 = 13.00; 100–199 = 12.20; 200+ = 11.80.

Proyectos y lotes
- Proyecto A (`product_project` A, 100 u)
  - Variante V1: 6 paneles, algodón, verde — 60 u → Lote L1 (`procurement_lot`)
  - Variante V2: 5 paneles, poliéster, rojo — 40 u → Lote L2
  - `purchase_order` A: consolida L1 y L2
- Proyecto B (`product_project` B, 100 u)
  - Variante V3: 6 paneles, algodón, azul — 100 u → Lote L3
  - `purchase_order` B: contiene L3

Total consolidado en `procurement_group`: 200 u → aplica tramo 200+ a 11.80 USD para todos los `purchase_order_items` (snapshot del tier).

Costos comunes y específicos (`cost_items`)
- A nivel `procurement_group` (multi‑proyecto): embroidery_setup_cost = 150 USD (único para el logo)
- A nivel `purchase_order`: PO A freight = 180 USD; PO B freight = 120 USD
- A nivel `product_project`: Diseño/arte A = 100 USD; Diseño/arte B = 60 USD
- A nivel `procurement_lot` (mono‑variante): Color change fee (solo L2) = 30 USD
- Impuestos (referencial): Derecho de importación 6% sobre valor de mercancía (unit_price de `purchase_order_item`)

Reglas de prorrateo
- Grupo (setup) → por cantidad total del grupo (200 u) → 150/200 = 0.75 USD/u
- PO (flete) → por cantidad total de la OC → A: 180/100 = 1.80; B: 120/100 = 1.20 USD/u
- Proyecto (diseño) → por cantidad total del proyecto → A: 100/100 = 1.00; B: 60/100 = 0.60 USD/u
- Lote → propio del lote → L2: 30/40 = 0.75 USD/u
- Impuesto → 6% de 11.80 = 0.708 ≈ 0.71 USD/u

Unit cost por variante (fully loaded)
- V1 (Proyecto A, L1, 60 u)
  - Base goods: 11.80; Impuesto: 0.71; Setup grupo: 0.75; Flete PO A: 1.80; Diseño A: 1.00; Lote: 0.00
  - Total V1 = 16.06 USD/u → extendido = 963.60
- V2 (Proyecto A, L2, 40 u)
  - Base goods: 11.80; Impuesto: 0.71; Setup grupo: 0.75; Flete PO A: 1.80; Diseño A: 1.00; Lote: 0.75
  - Total V2 = 16.81 USD/u → extendido = 672.40
- V3 (Proyecto B, L3, 100 u)
  - Base goods: 11.80; Impuesto: 0.71; Setup grupo: 0.75; Flete PO B: 1.20; Diseño B: 0.60; Lote: 0.00
  - Total V3 = 15.06 USD/u → extendido = 1,506.00

Promedios del proyecto (fully loaded)
- Proyecto A (100 u): (60×16.06 + 40×16.81)/100 = 16.36 USD/u
- Proyecto B (100 u): 15.06 USD/u

Cómo se refleja en el modelo
- Precio base por unidad: `purchase_order_items.unit_price` = 11.80 (tier 200+ de `pricing_agreements`).
- Costos compartidos: `cost_items` en `procurement_group` (setup), `purchase_order` (flete), `product_project` (diseño) y `procurement_lot` (cargos de lote específicos).
- Impuesto: se calcula en reportes (6% de `purchase_order_items.unit_price`).
- Reportes: “Goods only” = sumar `purchase_order_items`; “Fully loaded” = goods + prorrateos (`cost_items`) + impuestos.

Nota: Políticas de redondeo y multi‑moneda deben fijarse explícitamente; guardar snapshots de `pricing_agreement_tier` y reglas de prorrateo para auditoría.
