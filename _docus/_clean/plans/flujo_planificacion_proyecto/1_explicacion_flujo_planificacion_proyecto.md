# Flujo de Planificación de Proyecto — Regalos Promocionales “Made to Order”

Este documento explica, de forma narrativa y operativa, cómo planificamos un proyecto de producción de regalos promocionales “made to order” para un cliente. El foco está en la planificación previa a la ejecución: convertir un brief comercial en una variante técnica producible, obtener ofertas competitivas de proveedores, consolidar todos los costos y presentar una cotización clara y trazable para su aprobación.

La calidad de esta planificación es crítica: permite estimar con precisión el margen del proyecto (nuestro KPI principal) y los hitos de producción y entrega (el principal factor de satisfacción del cliente), además de reducir la probabilidad de errores de producción que impactan negativamente el margen y la experiencia del cliente.

## Propósito y Alcance
- Objetivo: convertir necesidades de marketing/branding del cliente en una solución de producto viable, costeada y calendarizada.
- Alcance: abarca descubrimiento, definición técnica, solicitud y evaluación de cotizaciones a proveedores, consolidación de costos (producto, logística, aranceles, financieros, gestión), y presentación de la oferta al cliente. No incluye ejecución productiva ni logística posterior.
- Resultado: cotización aprobable, con supuestos explícitos, condiciones comerciales y trazabilidad técnica (especificación y condiciones versionadas/encadenadas por hito, con evidencias y responsables).

## Actores y Responsabilidades
- Analista de Ventas: lidera el proceso con foco en el cliente; levanta el brief, prioriza objetivos y restricciones, define la `ProductVariant` junto a QA/Operaciones, coordina internamente con Sourcing (sin gestionar la negociación), consolida costos y riesgos en el business case y prepara la cotización final.
- Analista de Sourcing: gestiona el proceso de RFQ y la relación con proveedores; consolida respuestas (precio, MOQs, lead time, condiciones), valida factibilidad e incoterms/logística, negocia y recomienda la adjudicación.
- Cliente: entrega objetivos, cantidades, plazos, branding y restricciones; valida la propuesta y aprueba la cotización.
- Proveedores: responden RFQs con precios, plazos y condiciones; declaran desviaciones; confirman viabilidad técnica.
- Soporte interno (según sea pertinente): Abastecimiento, Calidad/QA y Operaciones aportan criterios técnicos, de riesgo y de calendario.

## Artefactos y Conceptos (SI)

- Catálogo de Plantillas de Producto
  - Plantilla de Producto (`ProductTemplate`): molde parametrizable para un tipo de artículo y punto de partida para variantes “made to order”.

- Estructura del Proyecto y Producto
  - Proyecto (`Project`): contenedor general del encargo del cliente (objetivos, fechas, alcance).
  - Producto de Proyecto (`ProjectProduct`): línea de pedido interna con contexto de proyecto y cantidad objetivo.
  - Kit (opcional) (`Kit`): agrupación comercial/logística de varios `ProjectProduct` bajo una misma oferta o entrega.
  - Variante de Producto (`ProductVariant`): descomposición técnica de cada `ProjectProduct` en una o más fichas técnicas concretas (atributos, materiales, acabados, branding, embalaje, logística), con versionado.
  - Orden de Variante (`VariantProcurement`): contexto de compra asociado a una `ProductVariant` con cantidad, UoM, incoterm/moneda/puerto objetivo, fechas y notas; es el origen de RFQs, adjudicaciones y órdenes de compra.
  - Costeo del Proyecto (`ProjectCosting`): consolidación de costos y condiciones una vez seleccionadas las ofertas de proveedor y estimados los demás rubros. Incluye:
    - Costo unitario adjudicado (por `PurchaseConditions`: precio, incoterm, moneda) y costos no recurrentes (set‑up, moldes, muestras).
    - Logística estimada por tramo (origen, internacional, nacional), seguro, aranceles/impuestos (según HS y origen), y almacenaje/manipulación.
    - Financieros (plazos de pago vs. cobro, costo de capital, cobertura FX), overhead de gestión/QA y margen objetivo.
    - Cálculo de costo aterrizado y precio de venta unitario/total por `ProjectProduct` y por `Kit` (si aplica), con escenarios por cantidad.
  - Plan de Entrega (`DeliveryPlan`): compromiso de fechas por lote/entrega, derivado del lead time de producción adjudicado + tiempos logísticos + buffers.
  - Escenarios de Precio (`PriceScenario`): alternativas por tramos de cantidad/incoterm que alimentan la `CustomerQuote` (p. ej., 500/1.000/2.000 unidades, FOB vs. DDP).

- Artefactos de Interacción Comercial y Sourcing
  - Cotizacion a Cliente (`CustomerQuote`): documento comercial que resume precios, condiciones, vigencia y supuestos para aprobación del cliente.
  - Solicitud de Cotización a Proveedor (`RFQ`): requerimiento formal enviado a proveedores con la variante y condiciones objetivo.
  - Cotización de Proveedor (`SupplierQuote`): respuesta del proveedor (precio unitario, MOQs, lead time, incoterm, condiciones de pago y desviaciones).
  - Condiciones de Compra (`PurchaseConditions`): registro adjudicado (snapshot de variante + oferta ganadora) con términos comerciales definitivos.

## Fases del Proceso
1) Descubrimiento y Brief
- Reunión con el cliente para aclarar objetivos, público, cantidades, presupuesto, plazos, estándares de marca, certificaciones y restricciones.
- Identificación de riesgos (timing de campañas, estacionalidad, importaciones, tolerancias).

2) Diseño de Variante (Made to Order)
- Selección de plantilla y definición de atributos: materiales, colorimetría (Pantone), métodos de marcaje, dimensiones, acabados, packaging, requisitos logísticos.
- Construcción de la `ProductVariant` y versionado frente a cambios. Validación de campos obligatorios y reglas técnicas.

2.1) Detalles de Compra (Orden de Variante)
- Creación de la `VariantProcurement` asociada a la variante: cantidad, UoM, incoterm/moneda/puerto objetivo, fecha requerida y notas.
- Esta entidad será el origen de RFQs, cotizaciones de proveedor, adjudicación (`PurchaseConditions`) y emisión de OC.

3) Factibilidad Técnica Interna
- Revisión con QA/Operaciones sobre procesos disponibles, tolerancias, necesidad de muestras o calibraciones de color, y requisitos regulatorios.
- Go/No‑Go técnico preliminar o lista de ajustes.

4) Preparación y Envío de RFQ
- Definir cantidad objetivo, moneda, incoterm y puerto, ventana de entrega y fecha límite.
- Selección de proveedores calificados por categoría y mercado.
- Paquete RFQ: ficha técnica de la variante, artes/mocks, notas críticas y criterios de aceptación.
  (RFQ ligado a `VariantProcurement`).

5) Recepción y Evaluación de Cotizaciones
- Consolidación de `SupplierQuotes`: precio unitario, MOQs, lead time, condiciones de pago y desviaciones.
- Normalización a moneda/incoterm objetivo y matriz comparativa por costo, cumplimiento técnico, plazo, riesgo y calidad.
- Rondas de aclaración/negociación cuando corresponda.

6) Adjudicación y Registro de Condiciones de Compra
- Selección de oferta ganadora y creación de `PurchaseConditions` (snapshot de variante + oferta), con incoterm, lead time, pagos y vigencia.
- Congelamiento/versión de la variante para trazabilidad. Definición de próximas muestras (si aplica) y gating para ejecución.

7) Cálculo de Costos del Proyecto
- Costo base: precio proveedor según incoterm/moneda y tramos de volumen.
- No recurrentes: set‑up, troqueles, moldes, desarrollo de muestras.
- Logística: fletes (origen/destino), seguro, consolidación, embalaje adicional, etiquetado, manipulación, almacenaje.
- Aranceles e impuestos: HS code, origen/destino, DAI/IVA u otros.
- Financieros y tipo de cambio: costo de capital vs. plazos, coberturas FX.
- Overhead y margen: gestión, QA, documentación; margen objetivo y descuentos.
- Escenarios: simulación por cantidades, incoterms alternativos y ventanas de entrega.

## Modelo de Costeo Desacoplado (Obligaciones y Pagos)

Principios
- Bottom‑up: los costos se registran en el nivel de la `VariantProcurement` (FOB por variante) y se complementan con costos compartidos prorrateados a nivel de `ProjectProduct`. El costo del `Project` es la suma de sus productos (más `ExceptionCost`).
- Desacople: la deuda (Obligación) está separada del evento de tesorería (Pago), permitiendo pagos parciales y conciliación clara.

Entidades y estados
- `CostObligation` (tipo: FOB, MOLD, SAMPLE, SHIPPING, CUSTOMS, INSURANCE, HANDLING, OTHER, CREDIT):
  - `status`: PROVISIONED (estimación), COMMITTED (OC/compromiso), APPROVED (factura verificada), CANCELED (anulada/sustituida).
  - `payment_status` (calculado): PENDING, PARTIALLY_PAID, PAID según `PaymentAllocation`.
- `Payment` y `PaymentAllocation`: un pago puede imputarse a múltiples obligaciones, registrando montos y (si aplica) tipo de cambio del momento.
- `ExceptionCost`: captura pérdidas separadas del costo de producción para no distorsionar la rentabilidad.

Flujos operativos clave
- Adjudicación de `SupplierQuote` → `PurchaseConditions`: crear/actualizar `CostObligation` tipo FOB anclada a la `VariantProcurement` en PROVISIONED (o COMMITTED al emitir la OC).
- Aprobación de factura: requiere `invoice_reference` y transiciona la obligación a APPROVED; habilita la asociación de `Payment`.
- Pagos: sólo a obligaciones APPROVED; se permiten parciales mediante `PaymentAllocation`.
- Prorrateo asistido: costos compartidos (p. ej., SHIPPING/CUSTOMS/INSURANCE) se distribuyen en múltiples `ProjectProduct` según bases (unidades, peso, valor, volumen). El sistema conserva la diferencia de redondeo para cuadratura exacta.
- Consolidaciones de producción: cuando se fija un precio FOB consolidado, el campo se autocompleta y no es editable en la `CostObligation`.
- Cancelaciones: si un producto se cancela con pagos ya realizados, se generan `ExceptionCost` por el monto hundido y se anulan/cierran las obligaciones originales para evitar doble conteo.

Reglas críticas
- Un `Payment` sólo puede asociarse a `CostObligation` en estado APPROVED.
- Para transicionar a APPROVED se requiere `invoice_reference` (y opcionalmente recepción/GRN si aplica 3‑way match).
- No se puede eliminar una `CostObligation` con pagos asociados; tras COMMITTED, la edición de monto/divisa debe restringirse o hacerse vía ajustes (CREDIT/DEBIT).

Métricas y control
- `outstanding_amount` = obligación − sumatoria de asignaciones; aging desde APPROVED hasta PAID.
- Diferencias cambiarias realizadas si existen pagos en divisa distinta.
- Trazabilidad técnica: snapshots de especificación y oferta al adjudicar; enlaces entre `ProjectProduct` ↔ `VariantProcurement` ↔ `PurchaseConditions` ↔ `CostObligation` ↔ `Payment`.

8) Construcción y Entrega de la Cotización al Cliente
- Documento comercial con: precio unitario y total, incoterm, tiempos, vigencia, condiciones de pago, tolerancias y exclusiones.
- Anexos: ficha técnica de la variante, mockups, cronograma tentativo y supuestos de cálculo.

9) Revisión, Ajustes y Aprobación
- Resolución de observaciones del cliente; si cambian atributos o cantidades, se versiona la variante y se actualiza la evaluación/costos.
- Aprobación formal del cliente. Cierre de la planificación con cotización aceptada y condiciones comerciales fijas.

## Entregables de la Planificación
- Ficha técnica “made to order” (variante) con versionado y trazabilidad.
- Matriz comparativa de proveedores y fundamentos de adjudicación.
- Condiciones de compra adjudicadas con snapshots.
- Cotización comercial final con supuestos, riesgos y calendario de alto nivel.

## Decisiones Clave y Puntos de Control
- Go/No‑Go técnico tras factibilidad interna.
- Aprobación de la variante “lista para RFQ”.
- Cierre de evaluación y adjudicación (comité o criterio predefinido).
- Validación interna de costos y márgenes antes de ofrecer al cliente.

## Énfasis “Made to Order”
- Personalización y branding: cada proyecto ajusta materiales, colores, impresión y packaging al manual del cliente.
- Tiempos y MOQs: el calendario y los mínimos productivos definen viabilidad y precio; se comunican tolerancias y ventanas.
- Calidad y color: se establecen criterios de aceptación (muestras, pruebas de color, tolerancias) antes de ejecutar.

Con este flujo, la organización asegura que la planificación de cada proyecto de regalos promocionales “made to order” llegue a la mesa del cliente como una propuesta clara, costeada, viable y trazable, minimizando riesgos en la ejecución posterior.
