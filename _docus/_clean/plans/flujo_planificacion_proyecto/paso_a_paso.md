# Flujos de Trabajo del Nuevo Modelo Codex

Basado en el modelo de entidades implementado, estos son los flujos de trabajo clave del sistema de sourcing/procurement:

---

## Flujo 1: Creación del Proyecto y Producto Base

**Objetivo:** Establecer el contexto comercial y definir el producto a nivel consolidado.

### Pasos:
1. **Crear Proyecto**
   - **Entidad:** `Project`
   - **Datos:** `name`, `client_id`, `status='draft'`, `starts_at`, `ends_at`

2. **Seleccionar Plantilla de Producto**
   - **Entidad:** `ProductTemplate` (existente)
   - **Acción:** Elegir plantilla base (ej. "Gorra Textil 6 Paneles")

3. **Crear Producto del Proyecto**
   - **Entidad:** `ProductProject`
   - **Datos:** `project_id`, `product_template_id`, `total_quantity`, `status='draft'`
   - **Resultado:** Contenedor comercial consolidado para todas las variantes

---

## Flujo 2: Configuración de Variantes Específicas

**Objetivo:** Definir las configuraciones específicas y cantidades por variante.

### Pasos:
1. **Crear Primera Variante**
   - **Entidad:** `ProductVariant`
   - **Datos:** `product_project_id`, `quantity`, `configuration` (JSONB)
   - **Ejemplo:** `{"base_color": "red", "primary_material": "cotton", "logo_technique": "embroidered"}`

2. **Generar SKU Automático**
   - **Campo:** `generated_sku` (único en sistema)
   - **Formato:** "JCK-6P-ALG-ROJ-001"

3. **Agregar Variantes Adicionales**
   - **Repetir:** Para cada configuración diferente
   - **Validación:** `ProductProject::isQuantityConsistent()` debe ser `true`

4. **Validar Totales**
   - **Regla:** `ProductProject::totalVariantsQuantity()` = `ProductProject::total_quantity`

---

## Flujo 3: Sourcing Individual por Variante (sin adjudicar todavía)

**Objetivo:** Obtener cotizaciones específicas para cada variante.

### Pasos:
1. **Crear Lote de Abastecimiento**
   - **Entidad:** `ProcurementLot`
   - **Datos:** `product_project_id`, `product_variant_id`, `quantity`, `status='draft'`
   - **Restricción:** Un solo lote abierto por variante (índice único)

2. **Abrir RFQ**
   - **Acción:** Cambiar `status='rfq_open'`
   - **Resultado:** Lote listo para recibir cotizaciones

3. **Recibir Cotizaciones** (comprensión técnica + condiciones comerciales)
   - **Entidad:** `SupplierQuote`
   - **Datos mínimos normalizados:**
     - Precio: `unit_price`, `currency`
     - Plazos: `lead_time_days`, `valid_until`
     - Condiciones: `incoterm`, términos de pago (en `attachments/notes`)
   - **Comprensión/contrapropuesta técnica del proveedor:**
     - El proveedor puede plantear cambios sobre la `configuration` de la variante (p. ej., material, técnica de logo, gramaje, empaques).
     - Registrar evidencia en `attachments` (PDF/imagen/JSON) y detallar en `notes` los campos afectados.
     - Sugerido: usar una clave `attachments.spec_proposal` (JSON) con deltas vs configuración original para facilitar el comparador.
   - **MOQ y lotes mínimos:**
     - Registrar MOQ por variante/color/tamaño en `attachments.moq` o `notes` (ej.: "MOQ por color: 50").
   - **Otros costos y supuestos:**
     - Setup (bordado/serigrafía), moldes, empaques especiales, inspección QC, flete supuestos (si el proveedor lo declara).
     - Registrar en `attachments.cost_breakdown` o `notes`. Si la cotización se adjudica, modelar estos cargos como `CostItem` con el alcance correspondiente (`procurement_group`, `purchase_order` o `procurement_lot`).
   - **Validaciones del sistema (al cargar la quote):**
     - Destacar diferencias entre `attachments.spec_proposal` y la `configuration` original (semáforo).
     - Normalizar moneda a la del proyecto para comparativas (sin perder la `currency` original).
     - Enforzar unicidad: una cotización activa por (lote, proveedor) (`uniq_open_quote_per_lot_supplier`).

4. **Preseleccionar (Shortlist) y Congelar Decisión**
   - **Acción:** Marcar una o más `SupplierQuote` como `shortlisted` (no adjudicar aún)
   - **Motivo:** Permitir evaluar consolidación comercial (Flujo 4) antes de fijar proveedor/precio a nivel de lote
   - **Estado del Lote:** Mantener `status='quoted'` hasta resolver consolidación

---

## Flujo 4: Consolidación Comercial Multi-Proyecto (adjudicación prioritaria)

**Objetivo:** Aprovechar volúmenes consolidados para mejores precios.

### Pasos:
1. **Descubrir Demanda Consolidable
   - **Acción del analista:** Filtrar todos los productos del mismo tipo (p. ej. “gorros con logo”) en todos los proyectos vigentes, y detectar `procurement_lots` compatibles.
   - **Criterios de búsqueda (sugeridos):**
     - Por plantilla/tipo: `product_template_id = :cap_template_id`
     - Por atributos clave (JSONB): `configuration->>'primary_material'`, `configuration->>'base_color'`, etc. (usa índice GIN)
     - Por ventanas de entrega compatibles: overlap de fechas objetivo del proyecto
     - Por proveedor objetivo (si ya hay preferencia) o por cartera de proveedores aptos
   - **Compatibilidad mínima para consolidar:**
     - Mismo proveedor candidato o dispuestos a cotizar el mismo conjunto
     - Atributos técnicos suficientemente homogéneos o con tolerancias claras
     - Lead time y ventanas que permitan agrupar producción/embarque

2. **Crear Grupo de Consolidación**
   - **Entidad:** `ProcurementGroup`
   - **Datos:** `name`, `supplier_id`, `currency`, `status='draft'`

3. **Agregar Lotes al Grupo**
   - **Entidad:** `ProcurementGroupItem`
   - **Datos:** `procurement_group_id`, `procurement_lot_id`, `requested_quantity`
   - **Fuente:** Lotes de múltiples proyectos identificados en el paso previo

4. **Solicitar Cotización Grupal**
   - **Entidad:** `GroupSupplierQuote`
   - **Datos:** `procurement_group_id`, `supplier_id`, `status='sent'`

5. **Definir Tramos de Precio**
   - **Entidad:** `GroupSupplierQuoteTier`
   - **Datos:** `min_qty`, `max_qty`, `unit_price`
   - **Ejemplo:** 1-99: $13.00, 100-199: $12.20, 200+: $11.80

6. **Adjudicar y Crear Acuerdo**
   - **Entidad:** `PricingAgreement` + `PricingAgreementTier`
   - **Acción:** Adjudicar la `GroupSupplierQuote` ganadora y convertirla en acuerdo activo
   - **Efecto:** Todos los lotes del grupo toman precio por tramos desde el acuerdo (no por lote)
   - **Post‑acuerdo:** Marcar los `ProcurementLot` involucrados como `status='awarded'` con `supplier_id` del acuerdo (el precio se determina por el tier al emitir OC)

7. **Fallback (sin acuerdo)**
   - Si no hay acuerdo: adjudicar a nivel de cada `ProcurementLot` la `SupplierQuote` ganadora y fijar snapshot (`awarded_quote_id`, `agreed_unit_price`, `supplier_id`)

---

## Flujo 5: Emisión de Órdenes de Compra (aplicando la decisión de precio)

**Objetivo:** Formalizar la compra con proveedores adjudicados.

### Pasos:
1. **Crear Orden de Compra**
   - **Entidad:** `PurchaseOrder`
   - **Datos:** `project_id`, `supplier_id`, `currency`, `status='issued'`
   - **Agrupación:** Por proyecto + proveedor

2. **Agregar Items de la Orden**
   - **Entidad:** `PurchaseOrderItem`
   - **Datos:** `purchase_order_id`, `procurement_lot_id`, `product_variant_id`, `quantity`
   - **Precio y origen:**
     - Si hay acuerdo: `unit_price` desde `PricingAgreementTier` aplicado; setear `pricing_agreement_tier_id`; `unit_price_source = 'agreement'`
     - Si no hay acuerdo: `unit_price` desde `ProcurementLot::agreed_unit_price`; `unit_price_source = 'lot'`

3. **Registrar Costos Adicionales**
   - **Entidad:** `CostItem`
   - **Tipos:** Setup, flete, moldes, impuestos
   - **Alcance:** `product_project`, `procurement_lot`, `procurement_group`, `purchase_order`

4. **Calcular Costo Total**
   - **Base:** Suma de `PurchaseOrderItem::unit_price * quantity`
   - **Adicionales:** Prorrateo de `CostItem` según alcance
   - **Actualizar:** `ProductProject::avg_unit_price` (weighted average)

---

## Flujo 6: Producción y Hand‑over a Forwarder

**Objetivo:** Planificar fabricación por lotes y registrar la entrega al forwarder.

### Pasos:
1. **Crear Lotes de Producción**
   - **Entidad:** `ProductionLot`
   - **Datos:** `purchase_order_item_id`, `planned_qty`, `status='planned'`, `eta`
   - **QC:** `qc_status` (pending, ok, reject, rework)

2. **Avanzar Producción**
   - **Estados:** `planned` → `in_production` → `ready`
   - **Tiempos:** `planned_start/finish`, `actual_start/finish`

3. **Hand‑over a Forwarder**
   - **Acción:** registrar `handed_to_forwarder_at` y/o crear `Shipment` + `ShipmentItem` para el/los lotes entregados
   - **Tracking:** `factory_tracking_code` y `docs` del shipment

4. **Actualizar Estados**
   - **PurchaseOrder:** `enviada/confirmada` según tu flujo → `cerrada` tras concluir logística
   - **ProductionLot:** `ready` (hand‑over registrado)

---

## Flujo 7: Embarque Internacional

**Objetivo:** Consolidar lotes y rastrear tránsito internacional con documentos.

### Pasos:
1. **Crear Shipment**
   - **Entidad:** `Shipment`
   - **Datos:** `reference`, `mode`, `incoterm`, `forwarder_id`, puertos, `etd/eta`, `status`, `docs`

2. **Agregar Ítems de Shipment**
   - **Entidad:** `ShipmentItem`
   - **Datos:** `shipment_id`, `production_lot_id`, `qty`, `cartons`, `cbm`, `weight_kg`

3. **Avanzar Estados**
   - **Shipment:** `booking` → `cargo_received` → `loaded` → `departed` → `arrived` → `cleared` → `delivered_to_dc` → `closed`

4. **Costos Internacionales**
   - **CostItem (shipment):** flete, seguro, THC, doc fees; prorrateo por CBM/peso/cantidad según corresponda

## Flujo 8: Distribución Local

**Objetivo:** Gestionar la última milla desde DC a destinos finales.

### Pasos:
1. **Crear Distribución Local**
   - **Entidad:** `LocalDistribution`
   - **Datos:** `carrier_id`, `warehouse_origin`, `window_start/window_end`, `status`, `docs`

2. **Agregar Ítems de Distribución**
   - **Entidad:** `LocalDistributionItem`
   - **Datos:** `local_distribution_id`, `shipment_item_id` (o `production_lot_id`), `qty`

3. **Avanzar Estados**
   - **LocalDistribution:** `planned` → `picking` → `out_for_delivery` → `delivered` → `closed`

4. **Costos Domésticos**
   - **CostItem (local_distribution):** almacenaje, manipuleo, última milla; prorrateo por cantidad

## Flujo 9: Costeo y Reportes Financieros

**Objetivo:** Calcular costos reales y márgenes por proyecto/variante.

### Pasos:
1. **Costeo Base (Goods Only)**
   ```sql
   SELECT SUM(poi.unit_price * poi.quantity)
   FROM purchase_order_items poi
   WHERE poi.purchase_order_id IN (SELECT id FROM purchase_orders WHERE project_id = ?)
   ```

2. **Costeo Adicional por Alcance**
   - **Grupo:** Prorratear por cantidad total del grupo
   - **Proyecto:** Prorratear por cantidad total del proyecto
   - **OC:** Prorratear por cantidad total de la OC
   - **Lote:** Aplicar solo al lote específico

3. **Costeo Fully Loaded por Variante**
   ```
   Unit Cost = Base Price + (Setup/Total_Qty) + (Freight/PO_Qty) + (Design/Project_Qty) + Lot_Specific + Tax%
   ```

4. **Actualizar Promedios**
   - **Método:** `ProductProject::weightedEstimatedAvgUnitPrice()`
   - **Trigger:** Al adjudicar lotes o crear items de OC

---

## Validaciones y Reglas de Negocio

### Invariantes Clave:
- **Consistencia de cantidades:** `ProductProject::isQuantityConsistent()` = true
- **Un lote abierto por variante:** Índice único parcial en `procurement_lots`
- **Estados secuenciales:** No retroceso sin autorización
- **Inmutabilidad post-PO:** No cambios en configuración tras `po_issued`

### Constraints de Base de Datos:
- `generated_sku` UNIQUE
- Estados válidos con CHECK constraints
- Índices GIN en `configuration` JSONB
- Índices únicos parciales para lotes/cotizaciones activas

Este modelo permite escalabilidad desde sourcing simple (Flujo 3) hasta consolidación comercial compleja (Flujo 4), manteniendo trazabilidad completa y flexibilidad operativa.
