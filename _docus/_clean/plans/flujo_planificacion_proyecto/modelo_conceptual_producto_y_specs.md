# Modelo Conceptual del Sistema de Producto y Especificaciones

> Este documento resume la comprensión conceptual del sistema de taxonomía, especificaciones y cotización de productos, sintetizando la información de los documentos de arquitectura y diseño.

---

## 1. La Jerarquía Conceptual: De la Venta a la Fábrica

El sistema se estructura en capas que van desde el concepto comercial abstracto hasta la instancia concreta que se va a fabricar.

1.  **Kit (La Unidad Comercial):**
    *   Es la entidad fundamental desde la perspectiva de la venta y la cotización.
    *   Representa un "paquete" o agrupación de uno o más tipos de productos.
    *   Bajo el principio **"Todo es un Kit"**, un producto individual se vende como un kit de un solo componente, estandarizando el proceso.

2.  **Subcategoría de Producto (`ProductSubcategory`) (El Tipo de Artículo):**
    *   Define el tipo general de un artículo (ej. "<PERSON>ra", "Botella de Agua"). Es el concepto que en la arquitectura del planificador se denomina `Producto`.
    *   Su rol principal es servir como **plantilla de especificaciones**, determinando qué atributos son aplicables y requeridos para cualquier artículo de su tipo.

3.  **Producto / Variante de Diseño (`ProductItem`) (La Instancia Fabricable):**
    *   Es la entidad operativa central: la instancia específica y concreta de un producto que se va a cotizar, costear y fabricar.
    *   Corresponde al concepto de `Variante de Diseño` en la arquitectura del planificador.
    *   Cada `ProductItem` pertenece a una `ProductSubcategory` y debe cumplir con la plantilla de especificaciones que esta define.

## 2. El Flujo de Especificación: Un Proceso en Dos Pasos

El sistema desacopla la venta de la definición técnica a través de un proceso claro.

1.  **Cotización del `Kit`:** El proceso de venta comienza añadiendo un `Kit` a una cotización. En este punto, es una promesa comercial abstracta.

2.  **Configuración y "Resolución":** Al cotizar el `Kit`, el vendedor debe "configurarlo", es decir, **seleccionar la `Variante de Diseño` (`ProductItem`) específica** para cada componente del kit. Este paso crucial conecta la venta con la producción, generando un plan de fabricación concreto.

## 3. El Sistema de Atributos: El "Diccionario" de Características

Para que las especificaciones sean consistentes y válidas, el sistema se basa en un "diccionario" de atributos centralizado.

*   **Grupos y Subgrupos de Atributos (`AttributeGroup` / `AttributeSubgroup`):** Organizan los atributos en una jerarquía de dos niveles para dar orden a la UI y a la lógica de negocio. Los `Grupos` son las 7 dimensiones principales (ej. `atributos_centrales`, `visual_marca`) y los `Subgrupos` son secciones dentro de ellos (ej. `materiales`, `dimensiones`).

*   **Definición de Atributo (`AttributeDefinition`):** Es la ficha técnica de cada atributo posible. Define su clave canónica (`key`), su tipo de dato, unidad, reglas de validación y cómo se presenta en la interfaz.

## 4. El Modelo de Especificaciones: El "Valor" Concreto

Los valores específicos que describen un `ProductItem` se almacenan mediante un **enfoque híbrido** que balancea integridad y flexibilidad.

*   **Tablas Relacionales Dedicadas:** Los datos más críticos, transaccionales y estables (como información comercial, precios, dimensiones, empaque) se guardan en tablas propias con tipos de datos estrictos. Esto asegura la integridad y el rendimiento de las consultas.

*   **JSON por Grupo (`ProductItemGroupSpec`):** Los datos más descriptivos, variables o de "cola larga" se almacenan en un campo `spec_json` dentro de una tabla específica. Esto permite añadir nuevos atributos flexibles sin necesidad de modificar la estructura de la base de datos (migraciones).

## 5. Conceptos de Producción

*   **Lote de Producción:** Representa un grupo de unidades idénticas de un mismo `ProductItem` (Variante de Diseño) que se fabrican en conjunto, vinculando el plan directamente con la ejecución en fábrica.
