# Batería de Tests — Arquitectura Final (Filament 4 + Laravel + PostgreSQL)

Objetivo: validar la lógica de negocio y los flujos descritos en `_docs/plans/epica1/tax/arquitectura_claude_final.md`, asumiendo que Filament 4 funciona correctamente (ver `_docs/prompts/qa_filament.md`). Este plan considera que el entorno de testing usa PostgreSQL (no SQLite).

## Alcance y criterios (según qa_filament)
- Confiar en Filament 4; no testear funcionalidades nativas del framework.
- Probar componentes solo cuando haya comportamiento de aplicación (overrides, `->reactive()`, `afterStateUpdated`, esquemas dinámicos, acciones custom).
- Priorizar caminos negativos, bordes y autorización (`->assertForbidden()`).
- Validar atomicidad y unicidad en acciones críticas (transacciones, snapshots, únicos activos).
- Código/nombres en inglés; descripciones BDD legibles.

## Ambiente y tooling
- DB: PostgreSQL en testing (JSONB/GIN reales, CHECK constraints). No usar SQLite.
- Base de tests: Pest + `uses(RefreshDatabase::class)`; considerar `--parallel` para velocidad.
- Fakes y tiempo: `Bus::fake()`, `Mail::fake()`, `Notification::fake()`, `Queue::fake()`, `Storage::fake()`; `Carbon::setTestNow()`.
- Factories para todo dato (`Model::factory()->create()`).

## Suites de pruebas

### 1) Catálogo y plantillas
- Attributes
  - `key` único; `type` ∈ `AttributeType`.
  - Para `enum`, `options_json` restringe valores aceptados.
- AttributeGroups
  - Orden (`position`) y metadatos presentes; activos/inactivos si aplica.
- ProductTemplates
  - Estados válidos: `draft|active|obsolete` (transiciones si existen reglas).
- ProductTemplateAttributes (pivot)
  - Persistencia de `attribute_group_id`, `is_required`, `position`, `ui_component`, `ui_props`, `default_value`, `validation_rules`, `help_text`, `column_span ∈ [1,12]`.
  - Unicidad por (`product_template_id`, `attribute_id`).
  - Unicidad de `position` visible por grupo (índice parcial).

### 2) Form dinámico de ProductVariant
- Schema
  - Agrupación por `attribute_group_id` y orden por `position`.
  - Respeta `visible=false`.
- Validación combinada
  - `required/nullable` (pivot) + reglas por `AttributeType` + `validation_rules` (pivot) sobre `specifications.{key}`.
- Props/UI
  - `default_value`, `placeholder`, `help_text`, `column_span`, `ui_props` se aplican al componente.
- Cache
  - Cambios en el template invalidan el cache del schema (clave incluye `updated_at`).
- SKU
  - Generación de `internal_sku` al cambiar `product_template_id`; unicidad; no sobrescribe un SKU manual ya definido.

### 3) Listados y filtros
- Columnas que leen `specifications.*` muestran/buscan correctamente.
- Filtro por `specifications->primary_material` (múltiple) retorna el subconjunto correcto.

### 4) Versionado de plantillas (opcional)
- Creación de `template_versions` y `template_version_attributes` coherente.
- `UNIQUE(product_template_id, number)` respetado; schema puede provenir de versión activa.

### 5) RFQs (requests for quote)
- Creación desde `product_variant` y/o `project_product` con `quantity`, `unit_of_measure`, `target_currency/incoterm/port`.
- Invitaciones `rfq_suppliers` y estados de invitación (`pending|sent|accepted|rejected`).
- Transiciones RFQ: `draft → sent → (closed|awarded|canceled)` con `sent_at`/`due_at`.
- Rechazo de transiciones inválidas.

### 6) Supplier quotes
- Registro: `unit_price`, `currency`, `production_lead_time`, `incoterm`, `payment_terms`, `spec_deviation_json`, adjuntos.
- Estados: `received → shortlisted → (awarded|rejected)`.
- Consultas sobre `spec_deviation_json` (si existen filtros por clave/valor).

### 7) Adjudicación y purchase conditions
- Precondición: existen `supplier_quotes` válidas asociadas al RFQ.
- Adjudicar:
  - Crea `purchase_conditions` con `variant_snapshot_json` (de `specifications`) y `quote_snapshot_json` (términos).
  - Actualiza `rfqs.status = awarded`.
  - Enlaza `project_product_id` y `quote_id` correctamente.
- Unicidad: una sola `purchase_conditions` activa por `project_product_id` (índice parcial) — rechaza segunda activa.
- Atomicidad: fallo en unicidad u otra validación revierte cambios (transacción).

### 8) Autorización
- ProductTemplate builder: sólo roles permitidos crean/editan/eliminan; `assertForbidden()` para no autorizados.
- Adjudicar RFQ / crear `purchase_conditions`: restringido a roles específicos.

### 9) Negativos y bordes
- Faltan atributos `is_required` en `specifications` → error.
- Valor `enum` fuera de `options_json` → error.
- `column_span` fuera de rango → error.
- `internal_sku` duplicado → error.
- Duplicado de pivot (`product_template_id`, `attribute_id`) → error.
- Transiciones inválidas en `rfqs`, `supplier_quotes`, `purchase_conditions` → error.

## Estructura sugerida de archivos
- `tests/Feature/Domain/AttributesTest.php`
- `tests/Feature/Domain/ProductTemplateTest.php`
- `tests/Feature/Filament/ProductTemplateResourceTest.php`
- `tests/Feature/Filament/ProductVariantResourceTest.php`
- `tests/Feature/Domain/RfqLifecycleTest.php`
- `tests/Feature/Domain/SupplierQuotesTest.php`
- `tests/Feature/Domain/PurchaseConditionsAwardingTest.php`
- `tests/Feature/Auth/AuthorizationPoliciesTest.php`

## Configuración de PostgreSQL para tests
- `.env.testing` (ejemplo):
```
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=pss_test
DB_USERNAME=postgres
DB_PASSWORD=postgres
```
- Asegurar que `phpunit.xml` no fuerza SQLite y permite variables de entorno de testing.
- CI: levantar servicio PostgreSQL y ejecutar `php artisan migrate --env=testing` antes de la suite si no se usa `RefreshDatabase`.

## Comandos útiles
- Ejecutar todo: `php artisan test --parallel`
- Un archivo: `./vendor/bin/pest tests/Feature/Domain/ProductTemplateTest.php`
- Con cobertura: `XDEBUG_MODE=coverage php artisan test --coverage --min=85`

Nota: mantener los nombres de código en inglés y las etiquetas/descripciones visibles en español para respetar la guía de nomenclatura del proyecto.

## Browser Testing (Pest v4 + Playwright)

Pest 4 incorpora pruebas de navegador nativas mediante el plugin `pestphp/pest-plugin-browser` (sobre Playwright). Úsalo para validar journeys críticos end‑to‑end.

- Instalación (dev):
  - `composer require pestphp/pest-plugin-browser --dev`
  - `npm install playwright@latest && npx playwright install`

- Configuración opcional en `Pest.php`:
  - `pest()->browser()->inFirefox();` (o `inSafari()`) para fijar navegador por defecto.
  - `pest()->browser()->timeout(10);` para ajustar timeout por defecto.
  - `pest()->browser()->headed();` para ejecutar con ventana visible por defecto.

- Ejecución:
  - Básico: `./vendor/bin/pest`
  - Paralelo: `./vendor/bin/pest --parallel`
  - Elegir navegador: `./vendor/bin/pest --browser=firefox`
  - Modo headed: `./vendor/bin/pest --headed`
  - Depuración: `./vendor/bin/pest --debug`
  - Sharding (CI): `./vendor/bin/pest --shard=1/4 --parallel`

- Convenciones de uso:
  - Entrar a páginas con `visit('/')` → `$page`.
  - Interacciones: `$page->click('Login'); $page->type('email','<EMAIL>'); $page->press('Submit');`.
  - Asserts clave: `$page->assertSee('Welcome'); $page->assertUrlIs('/home'); $page->assertNoSmoke();` (sin errores JS ni logs de consola).
  - Multipágina: `$pages = visit(['/', '/about']); $pages->assertNoSmoke();` y luego indexar páginas.
  - Dispositivos/vista: `$page->on()->mobile();` o `$page->on()->iPhone14Pro();` y `->inDarkMode()`.

- CI (GitHub Actions) — pasos mínimos:
```
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*

    - name: Install NPM deps
      run: npm ci

    - name: Install Playwright Browsers
      run: npx playwright install --with-deps

    - name: Run Browser Tests
      run: ./vendor/bin/pest --ci --parallel
```

Recomendación: mantener el número de browser tests pequeño y centrado en los journeys más críticos; el resto cubrirlo con component tests y tests de dominio.

### Journeys críticos (browser tests mínimos)

Diseñados según `_docs/prompts/qa_filament.md` (no testear Filament en sí). Validan flujos completos y resultados de negocio.

1) Smoke del panel y vistas clave
- Páginas: dashboard/panel, Product Templates index, Product Variants index, RFQs index.
- Asserts: `assertNoSmoke()` (sin errores JS ni logs), carga OK.

2) Crear Product Variant desde plantilla activa (form dinámico)
- Paso: visitar Create Variant; seleccionar `product_template_id` (activa) → carga de schema por grupos; autogeneración de `internal_sku`.
- Completar campos mínimos requeridos por pivot; guardar.
- Asserts: notificación de éxito; fila en tabla con columnas de `specifications.*` visibles; `internal_sku` único; `status = draft` por defecto.

3) Filtro por especificaciones (JSONB)
- Paso: en Product Variants index, aplicar filtro por `specifications->primary_material` (múltiple).
- Asserts: resultados corresponden al subconjunto esperado; sin errores en consola; limpiar filtro restaura listado.

4) Crear y enviar RFQ
- Paso: desde Variant (o RFQs create), crear `rfq` con `quantity`, `unit_of_measure`, `target_currency/incoterm/port`; invitar `suppliers` (al menos 2); ejecutar acción “Enviar RFQ”.
- Asserts: estado cambia a `sent`; `sent_at` presente en UI; invitados listados con estado `sent/accepted` según flujo simulado.

5) Registrar Supplier Quote en RFQ
- Paso: abrir RFQ; crear `supplier_quote` para un invitado con `unit_price`, `currency`, `production_lead_time`, `payment_terms`, y `spec_deviation_json` (opcional).
- Asserts: aparece en tabla de cotizaciones con `status = received`; sin errores JS; valores renderizados.

6) Adjudicar cotización y generar Purchase Conditions
- Paso: desde RFQ, marcar una `supplier_quote` como ganadora (acción); confirmar.
- Asserts: `rfq.status = awarded`; se crea `purchase_conditions` (UI muestra resumen); snapshots visibles (p.ej., parte de `variant_snapshot_json`); restricción de unicidad: si existe una activa para el mismo `project_product_id`, mostrar error y no crear una segunda.

7) Autorización básica (acceso a recursos restringidos)
- Paso: usuario sin permisos intenta acceder a ProductTemplateResource; luego usuario autorizado accede.
- Asserts: no autorizado → redirección/login o 403; autorizado → index carga OK.

8) Vista previa desde Product Template (builder → create variant)
- Paso: en ProductTemplateResource, usar acción “Vista Previa” que navega a Create Variant con `product_template_id` preseleccionada.
- Asserts: formulario de Variant pre-cargado con schema correcto para esa plantilla; secciones/grupos visibles según configuración.
