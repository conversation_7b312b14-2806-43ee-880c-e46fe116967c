# Análisis de Arquitectura del Proyecto Laravel

Analizando la estructura de archivos del proyecto, se pueden identificar varias decisiones clave de arquitectura consistentes con un enfoque moderno de Laravel (como en la versión 12).

1.  **Uso del Patrón de Acciones (Actions)**: La lógica de negocio compleja o las tareas específicas no residen en los controladores o modelos, sino que se encapsulan en clases dedicadas de un solo uso. Esto se evidencia por el directorio `app/Actions/` con archivos como `CreatePurchaseOrderAction.php` y `AddItemToPurchaseOrderAction.php`.

2.  **Objetos de Transferencia de Datos (DTOs)**: Se utilizan objetos estructurados para pasar datos entre las capas de la aplicación, en lugar de usar arrays asociativos. Esto mejora la legibilidad y la seguridad de tipos. El directorio `app/DTOs/` con clases como `CreatePurchaseOrderDto.php` confirma esta decisión.

3.  **Value Objects y Casts Personalizados para Eloquent**: En lugar de usar tipos de datos primitivos (como `integer` para precios), se utilizan "Value Objects" que encapsulan la lógica y el contexto de un dato. El `app/ValueObjects/Money.php` es un claro ejemplo. Para que Eloquent pueda trabajar con estos objetos de forma nativa, se ha creado un "cast" personalizado en `app/Casts/MoneyCast.php`.

4.  **Construcción del Panel de Administración con Filament**: Una de las decisiones más importantes es el uso de [Filament](https://filamentphp.com/) para todo el back-office o panel de administración. La existencia del directorio `app/Filament/` lo confirma. Esto implica adoptar la pila TALL (Tailwind CSS, Alpine.js, Laravel, Livewire) para la interfaz de gestión.

5.  **Gestión de Roles y Permisos (RBAC) con Spatie**: Se ha implementado un sistema robusto de control de acceso basado en roles y permisos utilizando el popular paquete `spatie/laravel-permission`. Esto se deduce de los archivos de configuración `config/permission.php` y `config/filament-shield.php` (una herramienta que integra Spatie con Filament).

6.  **Excepciones Personalizadas para la Lógica de Negocio**: El proyecto define sus propias clases de excepción, como `BusinessLogicException.php` en `app/Exceptions/`. Esto permite un manejo de errores más limpio y específico, separando los errores de validación de los errores de reglas de negocio.

7.  **Uso de Enums Nativos de PHP**: Para representar estados fijos y predefinidos (como el estado de una cotización), se están utilizando Enums de PHP. El archivo `app/Enums/CustomerQuoteStatus.php` es un ejemplo de esta práctica moderna que evita el uso de "magic strings" o números.
