# **Clasificación y Especificación de Productos de PromoSmart**

## 1. Taxonomía de Productos
La taxonomía de productos proporciona una clasificación jerárquica para todos los productos gestionados por PromoSmart. Consiste en:

1.  **Categorías de Productos:** Agrupaciones amplias de productos, como se detalla a continuación.
2.  **Subcategorías de Productos:** Clasificaciones más específicas dentro de cada categoría.
3.  **Estructura de Especificación de Producto Específica por Subcategoría:** Cada `Subcategoría de Producto` definida está asociada con una `estructura de especificación de producto` única. Esta estructura dicta el conjunto de características (atributos) requeridas y opcionales para un producto en esa subcategoría. Sirve como plantilla para definir y validar los detalles del producto en todo el sistema, asegurando consistencia y completitud, y respalda directamente la funcionalidad de Validación y Generación de Entidades Potenciada por IA.

### Categorías y subcategorías de productos
1.  **Merchandising:** Esta categoría se define por productos promocionales de marca, regalos corporativos (giveaways) y artículos destinados a la compra o regalo por parte del consumidor, todos orientados principalmente a aumentar la visibilidad de la marca, el compromiso y la lealtad del cliente a través de bienes tangibles, a menudo prácticos o deseables.
    * **Las subcategorías podrían incluir:**
        * Artículos para Beber (ej. tazones, botellas de agua, vasos térmicos)
        * Instrumentos de Escritura (ej. lápices, portaminas, destacadores)
        * Vestuario y Accesorios (ej. poleras, jockeys, lanyards, bolsos no utilitarios)
        * Accesorios Tecnológicos (ej. pendrives, baterías externas, soportes para celular)
        * Artículos de Oficina y Escritorio (ej. cuadernos, mousepads, calendarios)
        * Estilo de Vida y Aire Libre (ej. llaveros, paraguas, multiherramientas)
        * Premios y Reconocimientos (ej. galvanos, trofeos)

2.  **Material de Punto de Venta (PDV) y Exhibición (Material POP):** Esta categoría comprende artículos diseñados y utilizados específicamente para mejorar la presencia de la marca, exhibir productos de manera efectiva o comunicar información directamente en el punto de venta o durante eventos, con el propósito central de atraer la atención y facilitar la interacción con el cliente.
    * **Las subcategorías podrían incluir:**
        * Señalética y Banners (ej. pósteres, pendones roller, banderas de gran formato, adhesivos para ventanas)
        * Banderas Miniatura, de Sobremesa y de Mano (ej. banderas tipo cuchillo, banderas de escritorio, banderines, banderas de mano para eventos, banderas de mondadientes)
        * Soportes y Unidades de Exhibición (ej. glorificadores de productos, portafolletos, displays pop-up, stands modulares para ferias)
        * Materiales Impresos (ej. volantes, folletos, habladores de mesa, flejes para góndola)
        * Mobiliario y Accesorios Promocionales (ej. mesones de marca, carpas para eventos, vitrinas de exhibición)
        * Pantallas Digitales y Kioscos Interactivos
3.  **Textiles:** La categoría de Textiles abarca productos cuya principal característica definitoria es su construcción en tela y su utilidad inherente, distinguiéndolos de artículos donde la marca o la intención promocional podría ser el atributo principal.
    * **Las subcategorías podrían incluir:**
        * Vestuario (Prendas) (ej. poleras, poleras polo, chaquetas, uniformes - *cuando la calidad y utilidad de la tela son más importantes que la pura promoción*)
        * Textiles para el Hogar (ej. toallas, mantas, delantales, fundas de cojín - *cuando se valoran principalmente por sus propiedades textiles*)
        * Bolsos y Soluciones de Transporte a base de Tela (ej. bolsos de lona de alta resistencia, contenedores de tela especializados - *donde el material y la construcción son características clave*)
        * Textiles Técnicos Especializados (ej. telas de alto rendimiento, textiles industriales)
        * Tela Cruda/Materiales (ej. rollos de tela para procesamiento posterior o proyectos a medida)


## 2. Estructura de Especificación de Producto
Las dimensiones y atributos de especificación clave relevantes para las diferentes categorías y subcategorías de productos gestionadas dentro de nuestro sistema.

Para cada categoría de producto, se define un conjunto de **Dimensiones de Especificación**. Estas dimensiones representan los atributos y características críticas que deben especificarse para cualquier producto dentro de esa categoría. Los ejemplos incluyen 'Material / Composición', 'Tamaño / Dimensiones', 'Método / Técnica de Impresión', 'Precio Unitario' y 'Tipo de Embalaje'.

Para proporcionar una visión más clara y organizada, estas dimensiones de especificación individuales se agrupan en clasificaciones de nivel superior como:

* **Información Básica del Producto**
* **Atributos Centrales del Producto**: Propiedades físicas y estructurales fundamentales.
* **Especificaciones Visuales y de Marca**: Aspectos relacionados con la apariencia, la marca y la personalización.
* **Características Específicas de Textiles**: Dimensiones únicamente relevantes para productos a base de textiles.
* **Embalaje y Logística**: Detalles sobre cómo se empaquetan los productos (incluido el embalaje unitario, interior y master), se unitarizan y se preparan para la distribución.
* **Uso y Ciclo de Vida**: Información sobre la aplicación del producto, su vida útil y su almacenamiento.
* **Comercial y Abastecimiento**: Términos relacionados con la adquisición, el costo y el cumplimiento de los productos.

### Dimensiones y Atributos de Especificación Detallados

Esta sección describe los atributos específicos que se incluyen en cada dimensión de especificación. Estos atributos proporcionan el detalle granular necesario para la definición del producto, el abastecimiento y el control de calidad.

### Información Básica del Producto

**Nombre / Título del Producto:**
`official_product_name`: Nombre oficial del producto para uso interno y externo
`variant_name`: Nombre de la variante (si aplica, ej., "Polera de Algodón Premium - Azul Rey")
`seo_friendly_title`: Consideraciones para un título amigable para SEO
`internal_sku`: SKU interno o ID de Producto (si es distinto del nombre genérico)

**Descripción del Producto:**
`short_marketing_description`: Descripción corta de marketing (ej., para listados en sitios web, catálogos, típicamente de 1-3 oraciones)
`long_detailed_description`: Descripción larga / detallada orientada a características (profundizando en materiales, beneficios, usos)
`key_selling_points`: Puntos clave de venta y propuesta de valor única (preferiblemente en viñetas)
`target_audience`: Público objetivo o caso de uso principal
`disclaimers`: Cualquier descargo de responsabilidad, advertencia o instrucción de uso importante no cubierta en 'Instrucciones de Cuidado'

### Atributos Centrales del Producto

**Material / Composición:**
`primary_material`: Material principal (ej., Algodón, Poliéster, Plástico ABS, Acero Inoxidable, Aluminio, Vidrio, Cerámica, Papel, Cartón, PVC, PET)
`secondary_materials`: Materiales secundarios o componentes (ej., porcentaje de Spandex en la tela, composición de la aleación, tipo de forro)
`recycled_content_percentage`: Porcentaje de contenido reciclado (pre-consumo, post-consumo)
`material_certifications`: Certificaciones de materiales (ej., GOTS para textiles orgánicos, FSC para papel/madera, OEKO-TEX, libre de BPA, grado alimenticio)
`specific_material_grade`: Grado, tipo o variante específica del material (ej., Acero Inoxidable 304, Algodón 100% Ring-Spun, Cartulina Couché 250 g/m², Polipropileno (PP) #5)
`material_origin`: Origen de los materiales clave (si es relevante para declaraciones de sostenibilidad o calidad)

**Tamaño / Dimensiones:**
`overall_external_dimensions`: Dimensiones externas totales (Largo x Ancho x Alto/Profundidad)
`internal_dimensions`: Dimensiones internas (si aplica, ej., para contenedores)
`diameter`: Diámetro (para artículos cilíndricos o redondos)
`capacity_volume`: Capacidad / Volumen (ej., ml, oz, litros para artículos de beber, bolsos)
`thickness`: Espesor (para artículos planos como posavasos, mousepads, paneles de señalética)
`specific_part_dimensions`: Dimensiones de partes específicas (ej., largo del asa, ancho de la correa, tamaño del ala, altura del mástil de la bandera)
`dimensional_tolerances`: Tolerancias dimensionales (ej., +/- 1mm, +/- 5%)
`folded_collapsed_dimensions`: Dimensiones plegado o colapsado (si aplica)

**Peso / Capacidad de Carga:**
`net_weight`: Peso neto de la unidad individual del producto (ej., gramos, kilogramos, onzas, libras)
`maximum_load_capacity`: Capacidad máxima de carga (ej., para bolsos, soportes de exhibición, repisas)
`weight_distribution`: Consideraciones sobre la distribución del peso (para la estabilidad de los exhibidores)

**Gramaje de la Tela (GSM - Gramos por Metro Cuadrado):**
`gsm_value`: Valor específico de GSM (ej., 150 GSM para poleras livianas, 320 GSM para polerones gruesos, Poliéster 600D para bolsos)
`gsm_tolerance`: Tolerancia para GSM (ej., +/- 5%)
`thread_count`: Cantidad de hilos (TC) para ciertos textiles (ej., ropa de cama, camisas de alta calidad)

**Tipo de Ensamblaje / Estructura:**
`assembly_required`: Requiere ensamblaje por el usuario final (Sí/No)
`assembly_type`: Tipo de ensamblaje (ej., autoensamblaje con instrucciones, pre-ensamblado, parcialmente ensamblado, componentes modulares)
`joining_mechanisms`: Mecanismos de unión o herrajes incluidos (ej., tornillos, pernos, encaje a presión, piezas interconectables, tiras adhesivas)
`structural_design`: Diseño estructural (ej., pop-up, roller, tipo A, voladizo, inflable, marco rígido)
`number_of_parts`: Número de partes o componentes

### Especificaciones Visuales y de Marca

**Método / Técnica de Impresión:**
`printing_methods`: Método(s) específico(s) utilizado(s) (ej., Serigrafía, Impresión Digital (DTG, UV), Bordado, Grabado Láser, Bajorrelieve (Debossing), Altorrelieve (Embossing), Tampografía, Sublimación, Vinilo Termotransferible (HTV), Litografía Offset, Flexografía)
`colors_per_print_location`: Número de colores por ubicación de impresión (ej., 1 color, proceso de 4 colores (CMYK), colores planos/spot)
`maximum_printable_area`: Dimensiones máximas del área imprimible (por ubicación)
`ink_type`: Tipo o propiedades de la tinta (ej., a base de agua, plastisol, eco-solvente, curado UV, tintas comestibles)
`specialty_inks`: Tintas o acabados especiales (ej., metálicas, fluorescentes, que brillan en la oscuridad, puff, alta densidad, glitter)
`embroidery_details`: Cantidad de puntadas y tipo de hilo para bordado

**Acabado / Tratamiento de Superficie:**
`surface_texture`: Textura/apariencia de la superficie (ej., Mate, Brillante, Satinado, Cepillado, Pulido, Arenado, Recubrimiento en Polvo (Powder Coated), Anodizado, Galvanizado, Lacado, Barnizado)
`lamination_type`: Tipo de laminado (ej., mate, brillante, soft-touch/aterciopelado)
`protective_coatings`: Recubrimientos protectores (ej., filtro UV, anti-rayas, anti-huellas, repelente al agua, anti-graffiti)
`specific_treatments`: Tratamientos específicos (ej., anti-pilling para textiles, anti-reflejo para pantallas, tratamiento ignífugo)

**Opciones de Color / Concordancia Pantone:**
`standard_base_colors`: Colores base estándar disponibles del producto
`custom_color_capability`: Capacidad de color personalizado (ej., "Teñido para igualar Pantone")
`pantone_numbers`: Números específicos del Pantone Matching System (PMS) para colores (producto e impresión)
`color_consistency_requirements`: Requisitos de consistencia de color (ej., tolerancia Delta E, proceso de aprobación de lab dip)
`colorfastness_rating`: Calificación de la solidez del color (para textiles, artículos impresos)

**Ubicación de Logo / Áreas de Impresión:**
`imprint_locations`: Ubicaciones definidas y permitidas para la impresión (ej., Pecho Centro Frontal, Manga Izquierda, Espalda Superior, Panel A, Lado 1)
`imprint_area_dimensions`: Dimensiones de cada área de impresión
`placement_restrictions`: Restricciones o directrices de ubicación (ej., distancia desde las costuras, consideraciones de curvatura)
`multiple_imprint_locations`: Se permiten múltiples ubicaciones de impresión (Sí/No, y si es así, cuántas)

**Etiquetado (Tejido / Impreso / Otro):**
`label_type`: Tipo de etiqueta (ej., etiqueta tejida en el cuello, etiqueta de cuidado impresa en satín, etiqueta de marca tipo "pip tag", etiqueta termotransferible, hang tag, sticker)
`label_material`: Material de la etiqueta (ej., poliéster, algodón, satín, tyvek, cartulina)
`label_dimensions`: Dimensiones y forma de la etiqueta
`label_information_content`: Contenido de información en la etiqueta (ej., logo de la marca, talla, instrucciones de cuidado, país de origen, composición del material, número RN)
`label_attachment_method`: Método de fijación (ej., cosido, termoprensado, adhesivo, atado con cordel)

**Requisitos de Archivos de Arte/Diseño:**
`accepted_file_formats`: Formatos de archivo aceptados (ej., .AI, .EPS, .PDF (vectorial), .PSD, .TIFF, .SVG)
`minimum_resolution`: Resolución mínima para imágenes rasterizadas (ej., 300 DPI a tamaño de impresión)
`color_mode`: Modo de color (ej., CMYK, RGB con referencias Pantone, Escala de grises)
`vector_format_required`: Formato vectorial preferido/requerido para logos y arte lineal
`bleed_specifications`: Especificaciones de sangrado, corte y zona segura (ej., 3mm de sangrado)
`fonts_outlined`: Fuentes trazadas/incrustadas
`template_available`: Disponibilidad de plantilla para la configuración del arte

### Características Específicas de Textiles

**Corte / Calce / Patrón (Vestuario):**
`fit_style`: Estilo de calce (ej., Corte Clásico, Corte Regular, Corte Ajustado (Slim Fit), Corte Atlético, Corte Suelto, Oversized, Unisex, Corte de Hombre, Corte de Mujer, Juvenil)
`garment_construction`: Detalles de confección de la prenda (ej., mangas montadas, mangas raglán, costuras laterales, tejido tubular, costura de doble aguja, costuras planas/flatlock)
`fabric_pattern`: Patrón de la tela (ej., Sólido, Jaspeado (Heather), Melange, Rayado, a Cuadros, Dobby, Jacquard, Impresión completa personalizada)
`neckline_style`: Estilo del cuello (ej., Cuello Redondo, Cuello en V, Cuello Ancho, Cuello Polo, con Capucha)
`sleeve_style`: Largo/estilo de manga (ej., Manga Corta, Manga Larga, Manga ¾, Sin Mangas, con Puño)
`hem_style`: Estilo del dobladillo (ej., recto, curvo, con aberturas laterales)
`specific_features`: Características específicas (ej., bolsillos, cierres, botones, cordones)

**Rango de Tallas (Ropa):**
`available_sizes`: Tallas disponibles (ej., XS, S, M, L, XL, XXL, 2XL, 3XL, etc.; o tallas numéricas como 8, 10, 12)
`sizing_chart`: Tabla de tallas específica con medidas clave (ej., ancho de pecho, largo del cuerpo, largo de manga, cintura, entrepierna) por talla
`sizing_tolerance`: Tolerancia para las medidas de las tallas (ej., +/- 2.5 cm)
`international_size_conversions`: Conversiones de tallas internacionales si aplica (ej., US, EU, UK)

**Instrucciones de Cuidado:**
`washing_instructions`: Instrucciones de lavado (ej., lavar a máquina con agua fría/tibia, lavar a mano, no lavar)
`drying_instructions`: Instrucciones de secado (ej., secar en secadora a baja/media temperatura, colgar/secar al aire, secar en plano, no usar secadora)
`ironing_instructions`: Instrucciones de planchado (ej., plancha fría/tibia/caliente, no planchar, planchar por el reverso)
`bleaching_instructions`: Instrucciones de blanqueo (ej., no usar cloro, solo blanqueador sin cloro)
`dry_cleaning_instructions`: Instrucciones de lavado en seco (ej., solo lavado en seco, solvente específico)
`care_symbols`: Símbolos de cuidado estándar (ISO o GINETEX) para usar en la etiqueta

### Embalaje y Logística

**Tipo de Embalaje (Embalaje Unitario):**
`unit_packaging_description`: Descripción del embalaje individual por unidad (ej., Bolsa de polietileno (especificar tipo/grosor), Caja de cartón (especificar tipo/flauta), Blíster, Film retráctil, Caja de regalo, Tubo, Bolsa tipo pouch, Faja de cartón, Sin embalaje individual)
`unit_packaging_material`: Material del embalaje unitario (ej., LDPE, papel Kraft, PET)
`unit_packaging_dimensions`: Dimensiones del embalaje unitario (L x An x Al)
`unit_packaging_branding`: Marca/impresión en el embalaje unitario (ej., sin impresión, impresión a 1 color, impresión full color)
`unit_packaging_closure`: Tipo de cierre para el embalaje unitario (ej., autosellante, solapa de inserción, termosellado)

**Embalaje Interior (si aplica):**
`units_per_inner_pack`: Número de unidades individuales por empaque/caja interior
`inner_packaging_type`: Tipo de embalaje interior (ej., caja corrugada más pequeña, paquete en bolsa de polietileno, envoltura de papel)
`inner_pack_dimensions`: Dimensiones del empaque interior (L x An x Al) y peso bruto
`inner_pack_labeling`: Etiquetado o marcas en el empaque interior (ej., código de ítem, cantidad)

**Embalaje Master (Caja Master / de Despacho):**
`units_per_master_carton`: Número de unidades individuales o empaques interiores por caja master
`master_carton_material`: Especificaciones del material de la caja master (ej., tipo de cartón corrugado: pared simple/doble/triple, clasificación ECT o Resistencia al Estallido)
`master_carton_dimensions`: Dimensiones de la caja master (externas L x An x Al)
`master_carton_gross_weight`: Peso bruto de la caja master completamente llena (real y/o máximo)
`master_carton_net_weight`: Peso neto de los productos en la caja master
`master_carton_markings`: Marcas en la caja master (ej., marcas de despacho, códigos de barras (EAN/UPC/ITF-14), símbolos de manejo, país de origen, número de OC, descripción del ítem, cantidad)
`palletization_info`: Información de paletizado (si aplica: unidades por pallet, tipo de pallet, configuración)

**Cantidad por Unidad / UDM (Unidad de Medida):**
`unit_of_measure`: La unidad estándar por la cual el producto se vende o inventaría (ej., Cada uno, Pieza, Set de X, Pack de Y, Docena)
`pricing_ordering_base`: Esto define la base para la fijación de precios y pedidos

**Dims. y Peso de Despacho Est. (Caja Master):**
`estimated_shipping_dimensions`: Dimensiones externas estimadas de la caja master para el cálculo del flete
`estimated_shipping_weight`: Peso bruto estimado de la caja master para el cálculo del flete
`volumetric_weight`: Peso volumétrico/dimensional calculado o estimado

### Uso y Ciclo de Vida

**Compatibilidad de Uso / Uso Previsto:**
`primary_intended_use`: Uso o aplicación principal previsto (ej., regalo promocional, venta minorista, señalética para eventos, uniforme de personal)
`intended_environment`: Ambiente previsto (ej., solo para uso interior, duradero en exteriores, apto para contacto con alimentos)
`system_compatibility`: Compatibilidad con otros productos o sistemas (ej., compatibilidad de soporte para pendones, tipo de recarga para lápices)
`target_user_group`: Consideraciones sobre el público o grupo de usuarios objetivo (ej., seguro para niños, uso profesional)
`expected_lifespan`: Vida útil o durabilidad esperada en condiciones de uso normal

**Vida Útil / Caducidad:**
`applicable_shelf_life`: Vida útil aplicable si el producto se degrada con el tiempo (ej., para artículos con baterías, tintas, adhesivos, componentes de alimentos/confitería)
`expiry_date_requirements`: Requisitos y formato de la fecha de caducidad (si aplica)
`batch_coding_requirements`: Requisitos de seguimiento de fecha de fabricación o codificación de lotes

**Requisitos de Almacenamiento:**
`storage_temperature`: Condiciones de almacenamiento recomendadas (ej., rango de temperatura, niveles de humedad, lejos de la luz solar directa)
`stacking_limitations`: Limitaciones de apilamiento para productos empaquetados
`storage_sensitivities`: Cualquier sensibilidad específica (ej., a la humedad, luz, temperaturas extremas)

### Comercial y Abastecimiento

**Costo Unitario / Escalas de Precios:**
`exw_unit_cost`: Costo unitario Ex-Works (EXW) del proveedor
`fob_unit_cost`: Puerto y costo unitario Free on Board (FOB)
`cif_unit_cost`: Costo, Seguro y Flete (CIF) o Costo con Entrega y Derechos Pagados (DDP) (si aplica)
`volume_price_tiers`: Escalas de precios por volumen (ej., precio por 100 unidades, 500 unidades, 1000 unidades)
`setup_charges`: Cargos de configuración o de moldes (si aplica, y cómo se amortizan o cobran)

**Cantidad Mínima de Pedido (MOQ):**
`moq_per_sku`: MOQ por producto/SKU/color
`moq_per_order`: MOQ por pedido total a un proveedor

**Información del Proveedor:**
`preferred_supplier`: Nombre(s) y contacto(s) del proveedor preferido
`supplier_part_number`: Número de parte o código interno del proveedor
`country_of_origin`: País de Origen (COO) para la fabricación





**Código del Sistema Armonizado (HS) / Código Arancelario:**
`hs_code`: Código HS para la clasificación aduanera en despachos internacionales
`country_specific_codes`: Códigos de mercancía específicos del país si se conocen

---

### Descripciones Detalladas de Especificaciones por Categoría

#### Atributos Centrales del Producto
| Dimensión de Especificación      | Merchandising | Material PDV y Exhibición | Textiles |
|----------------------------------|---------------|---------------------------|----------|
| **Material / Composición** | `primary_material`: Materias primas como algodón, ABS, acero inoxidable, aluminio, vidrio, cerámica, papel, cartón, PVC, PET u otros sustratos usados para crear el producto. `secondary_materials`: Componentes adicionales y detalles de composición del material (ej., porcentaje de Spandex en la tela, composición de la aleación, tipo de forro). `recycled_content_percentage`: Porcentaje de contenido reciclado (pre-consumo, post-consumo). `specific_material_grade`: Grado, tipo o variante específica del material (ej., Acero Inoxidable 304, Algodón 100% Ring-Spun, Cartulina Couché 250 g/m², Polipropileno (PP) #5). `material_origin`: Origen de los materiales clave (si es relevante para declaraciones de sostenibilidad o calidad). | `primary_material`: Materiales como cartón, acrílico, foam, PVC, poliéster, nylon o sustratos similares usados para displays o señalética. `secondary_materials`: Materiales de soporte y componentes estructurales (ej., marcos metálicos, bases de peso, sistemas de fijación). `recycled_content_percentage`: Porcentaje de contenido reciclado en materiales de exhibición. `specific_material_grade`: Especificaciones técnicas del material (ej., cartón corrugado ECT, acrílico de alto impacto, foam de densidad específica). `material_origin`: Origen de materiales para cumplimiento regulatorio. | `primary_material`: Tipos de tela como algodón, mezclas de poliéster, lana, lino, seda o materiales reciclados. `secondary_materials`: Componentes adicionales de la tela y porcentajes de mezcla (ej., 95% algodón, 5% elastano). `recycled_content_percentage`: Porcentaje de fibras recicladas en la composición. `specific_material_grade`: Especificaciones técnicas de la tela (ej., algodón pima, poliéster de alto rendimiento). `material_origin`: Origen de las fibras para trazabilidad. |
| **Código Arancelario (HS Code)** | `hs_code`: Código HS para la clasificación aduanera en despachos internacionales. `country_specific_codes`: Códigos de mercancía específicos del país si se conocen. | `hs_code`: Código HS para materiales de PDV. `country_specific_codes`: Códigos específicos del país para exhibidores. | `hs_code`: Código HS para textiles. `country_specific_codes`: Códigos específicos del país para textiles. |
| **Tamaño / Dimensiones** | `overall_external_dimensions`: El tamaño físico del producto (ej., 10x15 cm, 500 ml). `internal_dimensions`: Dimensiones internas (si aplica, ej., para contenedores). `diameter`: Para artículos cilíndricos o redondos. `capacity_volume`: Especificaciones de volumen (ej., ml, oz, litros para artículos de beber, bolsos). `thickness`: Espesor (para artículos planos como posavasos, mousepads, paneles de señalética). `specific_part_dimensions`: Dimensiones de partes específicas (ej., largo del asa, ancho de la correa, tamaño del ala). `dimensional_tolerances`: Tolerancias dimensionales (ej., +/- 1mm, +/- 5%). `folded_collapsed_dimensions`: Dimensiones plegado o colapsado (si aplica). | `overall_external_dimensions`: El tamaño físico o la huella de los exhibidores (ej., 60x160 cm para pendones). `internal_dimensions`: Dimensiones internas para displays con cavidades o espacios. `diameter`: Para elementos circulares o cilíndricos. `capacity_volume`: Capacidad de almacenamiento o exhibición. `thickness`: Espesor de paneles, láminas o materiales. `specific_part_dimensions`: Dimensiones de componentes específicos (ej., altura del mástil de la bandera, ancho del soporte). `dimensional_tolerances`: Tolerancias para ensamblaje y montaje. `folded_collapsed_dimensions`: Dimensiones de almacenamiento y transporte (crítico para exhibidores plegables). | `overall_external_dimensions`: Las dimensiones de los artículos textiles (ej., S–XXL para poleras, o medidas específicas para textiles del hogar). `internal_dimensions`: Dimensiones internas para bolsos o contenedores textiles. `diameter`: Para elementos circulares textiles. `capacity_volume`: Capacidad de bolsos o contenedores textiles. `thickness`: Espesor de telas o materiales textiles. `specific_part_dimensions`: Dimensiones de elementos específicos (ej., largo del asa, ancho de la correa). `dimensional_tolerances`: Tolerancias para confección y acabado. `folded_collapsed_dimensions`: Dimensiones plegadas para almacenamiento. |
| **Peso** | `net_weight`: Especificaciones de peso del producto (ej., gramos, kilogramos, onzas, libras). | `net_weight`: Peso de la unidad de exhibición. | `net_weight`: Especificaciones de peso del artículo textil. |
| **Capacidad de Carga** | `maximum_load_capacity`: Requisitos de durabilidad bajo uso (ej., para bolsos, soportes de exhibición, repisas). | `maximum_load_capacity`: Capacidad de peso para unidades de exhibición o soportes, como límites de carga en repisas, capacidad de soporte de stands. | `maximum_load_capacity`: No aplicable para textiles estándar. |
| **Gramaje de la Tela (GSM)** | `gsm_value`: No aplicable a merchandising a menos que se use tela en el producto (ej., bolsas de algodón). `gsm_tolerance`: Tolerancia para GSM (ej., +/- 5%). `thread_count`: Cantidad de hilos (TC) para ciertos textiles. | `gsm_value`: Típicamente no aplicable a artículos de PDV a menos que se use tela (ej., pendones de tela, banderas). `gsm_tolerance`: Tolerancia para materiales textiles en PDV. `thread_count`: Especificaciones de hilos para telas de exhibición. | `gsm_value`: Peso de la tela utilizada (ej., 150 GSM para poleras livianas, 320 GSM para polerones gruesos, Poliéster 600D para bolsos). `gsm_tolerance`: Tolerancia para GSM (ej., +/- 5%). `thread_count`: Cantidad de hilos (TC) para ciertos textiles (ej., ropa de cama, camisas de alta calidad). |


#### Especificaciones de Branding
| Dimensión de Especificación      | Merchandising | Material PDV y Exhibición | Textiles |
|----------------------------------|---------------|---------------------------|----------|
| **Método / Técnica de Impresión**| `printing_methods`: El método usado para imprimir logos, texto o gráficos en el producto (ej., Serigrafía, Impresión Digital (DTG, UV), Bordado, Grabado Láser, Bajorrelieve (Debossing), Altorrelieve (Embossing), Tampografía, Sublimación, Vinilo Termotransferible (HTV), Litografía Offset, Flexografía). `colors_per_print_location`: Número de colores por ubicación de impresión (ej., 1 color, proceso de 4 colores (CMYK), colores planos/spot). `maximum_printable_area`: Dimensiones máximas del área imprimible (por ubicación). `ink_type`: Tipo o propiedades de la tinta (ej., a base de agua, plastisol, eco-solvente, curado UV, tintas comestibles). `specialty_inks`: Tintas o acabados especiales (ej., metálicas, fluorescentes, que brillan en la oscuridad, puff, alta densidad, glitter). `embroidery_details`: Cantidad de puntadas y tipo de hilo para bordado. | `printing_methods`: Técnicas de impresión para elementos de exhibición (ej., impresión offset, UV digital, serigrafía, impresión gran formato, plotter de corte). `colors_per_print_location`: Número de colores por ubicación de impresión. `maximum_printable_area`: Dimensiones máximas del área imprimible para displays. `ink_type`: Tipo o propiedades de la tinta para materiales de exhibición. `specialty_inks`: Requisitos de acabados especiales (ej., metálicas, fluorescentes, filtros UV). `embroidery_details`: Especificaciones de puntadas para elementos textiles en PDV. | `printing_methods`: Métodos para imprimir o bordar diseños en textiles (ej., sublimación, DTG, bordado, serigrafía textil, transferencia térmica). `colors_per_print_location`: Número de colores por ubicación de impresión. `maximum_printable_area`: Dimensiones máximas del área imprimible en textiles. `ink_type`: Tipo o propiedades de la tinta para textiles (ej., tintas de sublimación, plastisol textil). `specialty_inks`: Tintas especiales para textiles (ej., puff, glitter, metálicas). `embroidery_details`: Cantidad de puntadas y tipo de hilo para bordado (ej., 12,000 puntadas, hilo poliéster). |
| **Acabado / Tratamiento de Superficie** | `surface_texture`: La textura o recubrimiento final aplicado al producto (ej., Mate, Brillante, Satinado, Cepillado, Pulido, Arenado, Recubrimiento en Polvo (Powder Coated), Anodizado, Galvanizado, Lacado, Barnizado) para mejorar la apariencia o durabilidad. `lamination_type`: Tipo de laminado (ej., mate, brillante, soft-touch/aterciopelado). `protective_coatings`: Recubrimientos protectores (ej., filtro UV, anti-rayas, anti-huellas, repelente al agua, anti-graffiti). `specific_treatments`: Tratamientos específicos (ej., anti-pilling para textiles, anti-reflejo para pantallas, tratamiento ignífugo). | `surface_texture`: Opciones de acabado para artículos de exhibición (ej., laminado brillante, mate, barniz, filtro UV, recubrimiento anti-graffiti). `lamination_type`: Tipo de laminado para protección de superficie (ej., mate, brillante, soft-touch, anti-UV). `protective_coatings`: Recubrimientos protectores para exhibidores (ej., filtro UV, anti-rayas, repelente al agua). `specific_treatments`: Tratamientos específicos para materiales de exhibición (ej., ignífugo, anti-estático). | `surface_texture`: Tratamientos superficiales para tela (ej., pre-encogido, tratamiento antiarrugas, acabado suave, acabado resistente al agua). `lamination_type`: Laminado para textiles si aplica. `protective_coatings`: Recubrimientos protectores para textiles (ej., repelente al agua, anti-manchas). `specific_treatments`: Acabados específicos para telas (ej., anti-pilling, anti-UV, anti-bacteriano, anti-olor). |
| **Opciones de Color / Concordancia Pantone** | `standard_base_colors`: Las opciones de color disponibles, a menudo igualadas a estándares Pantone para consistencia. `custom_color_capability`: Capacidad de color personalizado (ej., "Teñido para igualar Pantone"). `pantone_numbers`: Números específicos del Pantone Matching System (PMS) para colores (producto e impresión). `color_consistency_requirements`: Requisitos de consistencia de color (ej., tolerancia Delta E, proceso de aprobación de lab dip). `colorfastness_rating`: Calificación de la solidez del color (para textiles, artículos impresos). | `standard_base_colors`: El esquema de color de los materiales de exhibición o señalética, a menudo usando igualación exacta de Pantone. `custom_color_capability`: Capacidad de color personalizado para displays. `pantone_numbers`: Números específicos del Pantone Matching System (PMS) para colores de exhibición. `color_consistency_requirements`: Requisitos de precisión de color para materiales de PDV. `colorfastness_rating`: Calificación de la solidez del color para materiales de exhibición. | `standard_base_colors`: Opciones de color de la tela, potencialmente igualadas a códigos Pantone u otros estándares de color. `custom_color_capability`: Capacidad de color personalizado (ej., "Teñido para igualar Pantone"). `pantone_numbers`: Números específicos del Pantone Matching System (PMS) para colores de tela. `color_consistency_requirements`: Requisitos de consistencia de color (ej., tolerancia Delta E, proceso de aprobación de lab dip). `colorfastness_rating`: Especificaciones de durabilidad del color (ej., lavado, luz, fricción). |
| **Ubicación de Logo** | `imprint_locations`: Dónde se aplica la marca en el merchandising, ej., panel frontal, espalda, manga. `imprint_area_dimensions`: Dimensiones de cada área de impresión. `placement_restrictions`: Restricciones o directrices de ubicación (ej., distancia desde las costuras, consideraciones de curvatura). `multiple_imprint_locations`: Se permiten múltiples ubicaciones de impresión (Sí/No, y si es así, cuántas). | `imprint_locations`: La ubicación de la marca o logos en artículos de PDV o exhibición (ej., frente, costados, panel superior). `imprint_area_dimensions`: Dimensiones de cada área de impresión. `placement_restrictions`: Directrices de ubicación del diseño (ej., distancia desde bordes, consideraciones de visibilidad). `multiple_imprint_locations`: Opciones de decoración en múltiples ubicaciones. | `imprint_locations`: La posición de logos o gráficos en prendas (ej., pecho, manga, dobladillo, espalda). `imprint_area_dimensions`: Dimensiones de cada área de impresión. `placement_restrictions`: Restricciones de ubicación (ej., distancia desde costuras, consideraciones de calce). `multiple_imprint_locations`: Opciones de decoración en múltiples ubicaciones (ej., pecho y espalda). |
| **Etiquetado (Tejido / Impreso)**| `label_type`: Etiquetas usadas para la marca o instrucciones de cuidado (ej., etiquetas de cuello tejidas, etiquetas de cuidado, hang tag, sticker). `label_material`: Material de la etiqueta (ej., poliéster, algodón, satín, tyvek, cartulina). `label_dimensions`: Dimensiones y forma de la etiqueta. `label_information_content`: Contenido de información en la etiqueta (ej., logo de la marca, talla, instrucciones de cuidado, país de origen, composición del material, número RN). `label_attachment_method`: Método de fijación (ej., cosido, termoprensado, adhesivo, atado con cordel). | `label_type`: Etiquetas o señalética para exhibidores, incluyendo marca, precios o etiquetas instructivas. `label_material`: Material de la etiqueta para PDV. `label_dimensions`: Dimensiones y forma de la etiqueta de exhibición. `label_information_content`: Contenido requerido en la etiqueta (ej., información del producto, precios, instrucciones de montaje). `label_attachment_method`: Método de fijación para etiquetas de exhibición. | `label_type`: El tipo y ubicación de las etiquetas textiles, como etiquetas tejidas o de cuidado. `label_material`: Material de la etiqueta (ej., poliéster, algodón, satín, tyvek). `label_dimensions`: Dimensiones y forma de la etiqueta textil. `label_information_content`: Contenido de información en la etiqueta (ej., logo de la marca, talla, instrucciones de cuidado, composición del material). `label_attachment_method`: Método de fijación (ej., cosido, termoprensado). `care_symbols`: Símbolos estándar de instrucciones de cuidado (ISO o GINETEX). |
| **Requisitos de Archivos de Arte/Diseño** | `accepted_file_formats`: Formatos requeridos (ej., .AI, .EPS, .PDF (vectorial), .PSD, .TIFF, .SVG), resolución, modos de color (CMYK, Pantone) para diseños personalizados. `minimum_resolution`: Resolución mínima para imágenes rasterizadas (ej., 300 DPI a tamaño de impresión). `color_mode`: Modo de color (ej., CMYK, RGB con referencias Pantone, Escala de grises). `vector_format_required`: Formato vectorial preferido/requerido para logos y arte lineal. `bleed_specifications`: Especificaciones de sangrado, corte y zona segura (ej., 3mm de sangrado). `fonts_outlined`: Fuentes trazadas/incrustadas. `template_available`: Disponibilidad de plantilla para la configuración del arte. | `accepted_file_formats`: Especificaciones para archivos de diseño para elementos de PDV impresos o con formas personalizadas. `minimum_resolution`: Resolución mínima para materiales de exhibición. `color_mode`: Modo de color para displays. `vector_format_required`: Formato vectorial para elementos de PDV. `bleed_specifications`: Requisitos de preparación para impresión de exhibición. `fonts_outlined`: Fuentes trazadas para materiales de PDV. `template_available`: Disponibilidad de plantilla para displays. | `accepted_file_formats`: Directrices para el envío de arte para impresión textil o bordado. `minimum_resolution`: Resolución mínima para impresión textil. `color_mode`: Modo de color para textiles. `vector_format_required`: Especificaciones del archivo de diseño vectorial. `bleed_specifications`: Especificaciones de sangrado para impresión textil. `fonts_outlined`: Fuentes trazadas para textiles. `template_available`: Disponibilidad de plantilla para impresión textil. |



#### Embalaje y Detalles Logísticos
| Dimensión de Especificación      | Merchandising | Material PDV y Exhibición | Textiles |
|----------------------------------|---------------|---------------------------|----------|
| **Tipo de Embalaje** | `unit_packaging_description`: Cómo se empaqueta el merchandising para la distribución (ej., Bolsa de polietileno (especificar tipo/grosor), Caja de cartón (especificar tipo/flauta), Blíster, Film retráctil, Caja de regalo, Tubo, Bolsa tipo pouch, Faja de cartón, Sin embalaje individual). `unit_packaging_material`: Material del embalaje unitario (ej., LDPE, papel Kraft, PET). `unit_packaging_dimensions`: Dimensiones del embalaje unitario (L x An x Al). `unit_packaging_branding`: Marca/impresión en el embalaje unitario (ej., sin impresión, impresión a 1 color, impresión full color). `unit_packaging_closure`: Tipo de cierre para el embalaje unitario (ej., autosellante, solapa de inserción, termosellado). | `unit_packaging_description`: Embalaje para el despacho de artículos de exhibición, como cajas planas o envolturas protectoras (ej., Bolsa de polietileno, Caja de cartón, Film retráctil, Tubo, Sin embalaje individual). `unit_packaging_material`: Material del embalaje unitario para PDV. `unit_packaging_dimensions`: Dimensiones del embalaje unitario para exhibidores. `unit_packaging_branding`: Requisitos de marca en el embalaje de exhibición. `unit_packaging_closure`: Tipo de cierre para embalaje de PDV. | `unit_packaging_description`: Embalaje para prendas o textiles (ej., Bolsa de polietileno, Caja de cartón, Film retráctil, Caja de regalo, Tubo, Bolsa tipo pouch, Faja de cartón). `unit_packaging_material`: Material del embalaje unitario para textiles. `unit_packaging_dimensions`: Dimensiones del embalaje unitario para textiles. `unit_packaging_branding`: Marca/impresión en el embalaje textil. `unit_packaging_closure`: Especificaciones del tipo de cierre para textiles. |
| **Embalaje Interior** | `units_per_inner_pack`: El embalaje primario que protege una unidad individual de merchandising (ej., caja de producto, blíster). `inner_packaging_type`: Tipo de embalaje interior (ej., caja corrugada más pequeña, paquete en bolsa de polietileno, envoltura de papel). `inner_pack_dimensions`: Dimensiones del empaque interior (L x An x Al) y peso bruto. `inner_pack_labeling`: Etiquetado o marcas en el empaque interior (ej., código de ítem, cantidad). | `units_per_inner_pack`: Embalaje inmediato para componentes de un exhibidor antes del ensamblaje o para artículos de PDV más pequeños (ej., portaseñaléticas individuales en una bolsa). `inner_packaging_type`: Tipo de embalaje interior para componentes de PDV. `inner_pack_dimensions`: Dimensiones del empaque interior para exhibidores. `inner_pack_labeling`: Identificación de componentes de exhibición. | `units_per_inner_pack`: El embalaje directo para un solo artículo textil (ej., bolsa de polietileno para una polera, faja de marca para una toalla). `inner_packaging_type`: Tipo de embalaje interior para textiles. `inner_pack_dimensions`: Especificaciones del tamaño del paquete textil. `inner_pack_labeling`: Etiquetado para empaques interiores de textiles. |
| **Embalaje Master** | `units_per_master_carton`: La caja o embalaje exterior usado para despachar múltiples unidades de merchandising con empaque interior (ej., una caja que contiene 24 cajas de producto). `master_carton_material`: Especificaciones del material de la caja master (ej., tipo de cartón corrugado: pared simple/doble/triple, clasificación ECT o Resistencia al Estallido). `master_carton_dimensions`: Dimensiones de la caja master (externas L x An x Al). `master_carton_gross_weight`: Peso bruto de la caja master completamente llena (real y/o máximo). `master_carton_net_weight`: Peso neto de los productos en la caja master. `master_carton_markings`: Marcas en la caja master (ej., marcas de despacho, códigos de barras (EAN/UPC/ITF-14), símbolos de manejo, país de origen, número de OC, descripción del ítem, cantidad). `palletization_info`: Información de paletizado (si aplica: unidades por pallet, tipo de pallet, configuración). | `units_per_master_carton`: Caja de despacho a granel para múltiples unidades de exhibición o componentes más grandes (ej., una caja grande con varios pendones planos). `master_carton_material`: Especificaciones del material de la caja master para PDV. `master_carton_dimensions`: Dimensiones de la caja master para exhibidores. `master_carton_gross_weight`: Peso bruto de la caja master de PDV. `master_carton_net_weight`: Peso neto de exhibidores en la caja master. `master_carton_markings`: Requisitos de identificación para el despacho de PDV. `palletization_info`: Información de paletizado para exhibidores. | `units_per_master_carton`: La caja de despacho o contenedor para múltiples artículos textiles, a menudo conteniendo varias unidades con empaque interior (ej., una caja con 50 poleras en bolsas de polietileno). `master_carton_material`: Especificaciones del material de la caja master para textiles. `master_carton_dimensions`: Dimensiones de la caja master para textiles. `master_carton_gross_weight`: Peso bruto de la caja master de textiles. `master_carton_net_weight`: Peso neto de textiles en la caja master. `master_carton_markings`: Marcas en la caja master de textiles. `palletization_info`: Especificaciones de despacho a granel para textiles. |
| **Cantidad por Unidad / UDM** | `unit_of_measure`: La unidad estándar por la cual el producto se vende o inventaría (ej., Cada uno, Pieza, Set de X, Pack de Y, Docena). `pricing_ordering_base`: Esto define la base para la fijación de precios y pedidos. | `unit_of_measure`: La unidad estándar por la cual el exhibidor se vende o inventaría (ej., Cada uno, Set de X, Pack de Y). `pricing_ordering_base`: Base para la fijación de precios y pedidos de PDV. | `unit_of_measure`: La unidad estándar por la cual el textil se vende o inventaría (ej., Cada uno, Pieza, Set de X, Pack de Y, Docena). `pricing_ordering_base`: Especificaciones por unidad textil para precios y pedidos. |
| **Dims. y Peso de Despacho Est. (Caja Master)** | `estimated_shipping_dimensions`: Dimensiones externas estimadas de la caja master para el cálculo del flete. `estimated_shipping_weight`: Peso bruto estimado de la caja master para el cálculo del flete. `volumetric_weight`: Peso volumétrico/dimensional calculado o estimado. | `estimated_shipping_dimensions`: Dimensiones y peso bruto estimados de las cajas master para artículos de PDV, críticos para cotizaciones de flete. `estimated_shipping_weight`: Cálculos de peso para el despacho de exhibidores. `volumetric_weight`: Peso volumétrico para materiales de PDV. | `estimated_shipping_dimensions`: Dimensiones y peso bruto estimados de las cajas master para despachos de textiles a granel. `estimated_shipping_weight`: Peso bruto estimado para textiles. `volumetric_weight`: Cálculos de costos de despacho para textiles. |


#### Certificaciones y otros requerimientos adicionales
| Dimensión de Especificación      | Merchandising | Material PDV y Exhibición | Textiles |
|----------------------------------|---------------|---------------------------|----------|
| **Requerimientos del Cliente** | `client_specific_certifications`: Certificaciones específicas requeridas por el cliente (ej., ISO 9001 para Nestlé, auditoría Coca-Cola, certificaciones de marca). `client_audit_requirements`: Requisitos de auditoría específicos del cliente (ej., auditoría de fábrica, auditoría de calidad, auditoría de sostenibilidad). `client_quality_standards`: Estándares de calidad específicos del cliente (ej., estándares de Nestlé, estándares de Coca-Cola). `client_compliance_documents`: Documentación de cumplimiento requerida por el cliente. | `client_specific_certifications`: Certificaciones específicas requeridas por el cliente para materiales de PDV (ej., ISO 9001, auditorías de marca). `client_audit_requirements`: Requisitos de auditoría específicos del cliente para exhibidores. `client_quality_standards`: Estándares de calidad específicos del cliente para materiales de exhibición. `client_compliance_documents`: Documentación de cumplimiento requerida por el cliente para PDV. | `client_specific_certifications`: Certificaciones específicas requeridas por el cliente para textiles (ej., ISO 9001, auditorías de marca). `client_audit_requirements`: Requisitos de auditoría específicos del cliente para textiles. `client_quality_standards`: Estándares de calidad específicos del cliente para textiles. `client_compliance_documents`: Documentación de cumplimiento requerida por el cliente para textiles. |
| **Requerimientos Regulatorios por País** | `country_specific_regulations`: Regulaciones específicas por país (ej., MDS para baterías de litio en Chile, registro sanitario para juguetes en Colombia, ICP, SEC). `dangerous_goods_classification`: Clasificación de mercancías peligrosas si aplica (ej., baterías de litio, productos químicos). `safety_certifications`: Certificaciones de seguridad requeridas (ej., certificados de la SEC para artículos electrónicos, certificados ICP). `sanitary_registrations`: Registros sanitarios requeridos (ej., registro de juguetes, registro de productos alimenticios). | `country_specific_regulations`: Regulaciones específicas por país para materiales de PDV (ej., certificados de seguridad, registros sanitarios). `dangerous_goods_classification`: Clasificación de mercancías peligrosas para exhibidores si aplica. `safety_certifications`: Certificaciones de seguridad requeridas para materiales de exhibición. `sanitary_registrations`: Registros sanitarios requeridos para exhibidores si aplica. | `country_specific_regulations`: Regulaciones específicas por país para textiles (ej., certificados de seguridad, registros sanitarios). `dangerous_goods_classification`: Clasificación de mercancías peligrosas para textiles si aplica. `safety_certifications`: Certificaciones de seguridad requeridas para textiles. `sanitary_registrations`: Registros sanitarios requeridos para textiles si aplica. |
| **Requerimientos Internos** | `internal_quality_standards`: Estándares de calidad internos de la empresa. `internal_compliance_requirements`: Requisitos de cumplimiento internos. `internal_documentation_requirements`: Requisitos de documentación interna. `internal_audit_requirements`: Requisitos de auditoría interna. | `internal_quality_standards`: Estándares de calidad internos para materiales de PDV. `internal_compliance_requirements`: Requisitos de cumplimiento internos para exhibidores. `internal_documentation_requirements`: Requisitos de documentación interna para PDV. `internal_audit_requirements`: Requisitos de auditoría interna para materiales de exhibición. | `internal_quality_standards`: Estándares de calidad internos para textiles. `internal_compliance_requirements`: Requisitos de cumplimiento internos para textiles. `internal_documentation_requirements`: Requisitos de documentación interna para textiles. `internal_audit_requirements`: Requisitos de auditoría interna para textiles. |
| **Vida Útil / Caducidad** | `applicable_shelf_life`: Vida útil aplicable si el producto se degrada con el tiempo (ej., para artículos con baterías, tintas, adhesivos, componentes de alimentos/confitería). `expiry_date_requirements`: Requisitos y formato de la fecha de caducidad (si aplica). `batch_coding_requirements`: Requisitos de seguimiento de fecha de fabricación o codificación de lotes. | `applicable_shelf_life`: Vida útil aplicable si los materiales de exhibición se degradan con el tiempo (ej., adhesivos, tintas, materiales sensibles a la luz). `expiry_date_requirements`: Requisitos de fecha de caducidad para materiales de PDV. `batch_coding_requirements`: Requisitos de codificación de lotes para exhibidores. | `applicable_shelf_life`: Vida útil aplicable si el producto tiene una vida útil limitada (ej., textiles ecológicos, productos con tratamientos especiales). `expiry_date_requirements`: Especificaciones de vida útil si aplica. `batch_coding_requirements`: Requisitos de seguimiento para textiles. |
| **Requisitos de Almacenamiento Específicos** | `client_storage_requirements`: Requisitos específicos de almacenamiento del cliente (ej., altura máxima de pallet, capacidad máxima de carga de caja). `storage_temperature`: Condiciones de almacenamiento recomendadas (ej., rango de temperatura, niveles de humedad, lejos de la luz solar directa). `stacking_limitations`: Limitaciones de apilamiento para productos empaquetados. `storage_sensitivities`: Cualquier sensibilidad específica (ej., a la humedad, luz, temperaturas extremas). | `client_storage_requirements`: Requisitos específicos de almacenamiento del cliente para materiales de PDV (ej., altura máxima de pallet, capacidad máxima de carga). `storage_temperature`: Condiciones de almacenamiento recomendadas para materiales de PDV (ej., rango de temperatura, niveles de humedad, protección UV). `stacking_limitations`: Limitaciones de apilamiento para exhibidores empaquetados. `storage_sensitivities`: Consideraciones ambientales (ej., a la humedad, luz, temperaturas extremas, presión). | `client_storage_requirements`: Requisitos específicos de almacenamiento del cliente para textiles (ej., altura máxima de pallet, capacidad máxima de carga). `storage_temperature`: Condiciones de almacenamiento recomendadas para textiles (ej., rango de temperatura, niveles de humedad, colgar para evitar arrugas). `stacking_limitations`: Limitaciones de apilamiento para textiles empaquetados. `storage_sensitivities`: Requisitos de cuidado de la tela (ej., a la humedad, luz, polillas, presión). |



#### Certificaciones y requerimientos adicionales
| Dimensión de Especificación      | Merchandising | Material PDV y Exhibición | Textiles |
|----------------------------------|---------------|---------------------------|----------|
| **Certificaciones de Materiales** | `material_certifications`: GOTS para textiles orgánicos, FSC para papel/madera, OEKO-TEX, libre de BPA, grado alimenticio, certificaciones de materiales reciclados, certificaciones de sostenibilidad. | `material_certifications`: Certificaciones para uso en interiores/exteriores, resistencia al fuego, certificaciones de estabilidad estructural, certificaciones de materiales ignífugos. | `material_certifications`: OEKO-TEX, GOTS, Fair Trade, Bluesign, certificaciones de fibras recicladas, certificaciones de comercio justo. |
| **Certificaciones de Cumplimiento y Seguridad** | `required_standards`: CE, FCC, RoHS, CPSIA, FDA, REACH, certificaciones de seguridad para productos infantiles, certificaciones de contacto con alimentos. | `required_standards`: Retardante de fuego, CE, RoHS, certificaciones de uso en exteriores. | `required_standards`: OEKO-TEX Standard 100 (libre de sustancias nocivas), GOTS (Global Organic Textile Standard), Fair Trade Certified, Bluesign (sostenibilidad en la cadena textil). |
| **Documentación de Certificación** | `testing_documents`: Documentos de prueba y certificación requeridos (ej., informes de prueba de laboratorios acreditados). `msds_required`: Hojas de datos de seguridad de materiales (MSDS/SDS) si aplica (ej., para baterías, componentes químicos). | `testing_documents`: Documentación de certificación requerida para exhibidores. `msds_required`: Requisitos de documentación de seguridad para materiales de PDV. | `testing_documents`: Documentación de certificación requerida para textiles. `msds_required`: Hojas de datos de seguridad para textiles (si aplica). |
| **Certificaciones de Fábrica** | `factory_audit_status`: Estado de auditoría de la fábrica o certificaciones (ej., ISO 9001, BSCI, Sedex). | `factory_audit_status`: Estado de auditoría de la fábrica para materiales de exhibición. | `factory_audit_status`: Estado de auditoría de la fábrica para textiles. |
| **Certificación IMO (International Maritime Organization)** | `imo_certification`: Certificación IMO si el producto está destinado para uso marítimo o transporte internacional por mar (ej., embalajes peligrosos, materiales de seguridad marítima). | `imo_certification`: Certificación IMO para materiales de exhibición destinados a uso marítimo o transporte internacional por mar. | `imo_certification`: Certificación IMO para textiles destinados a uso marítimo o transporte internacional por mar. |
| **Productos para Niños** | `children_product`: Indicación si el producto está destinado para niños (ej., juguetes, ropa infantil, accesorios para niños). `children_safety_standards`: Estándares de seguridad específicos para productos infantiles (ej., CPSIA, EN71, ASTM F963). `age_appropriateness`: Rango de edad apropiado para el producto (ej., 0-3 años, 3-6 años, 6-12 años). | `children_product`: Indicación si el material de exhibición está destinado para uso en áreas infantiles o por niños. `children_safety_standards`: Estándares de seguridad para exhibidores en áreas infantiles. `age_appropriateness`: Rango de edad apropiado para el uso del exhibidor. | `children_product`: Indicación si el textil está destinado para niños (ej., ropa infantil, textiles para cuna). `children_safety_standards`: Estándares de seguridad específicos para textiles infantiles (ej., OEKO-TEX Standard 100 Class I, EN71). `age_appropriateness`: Rango de edad apropiado para el textil (ej., 0-3 años, 3-6 años, 6-12 años). |
| **Productos de Consumo (Alimentos)** | `food_contact_product`: Indicación si el producto está destinado para contacto con alimentos (ej., vajilla, utensilios de cocina, envases de alimentos). `food_grade_certification`: Certificaciones de grado alimenticio (ej., FDA Food Grade, EU Food Contact, grado alimenticio). `food_safety_standards`: Estándares de seguridad alimentaria (ej., HACCP, ISO 22000, BRC). | `food_contact_product`: Indicación si el material de exhibición está destinado para uso en áreas de alimentos o contacto con alimentos. `food_grade_certification`: Certificaciones de grado alimenticio para exhibidores en áreas de alimentos. `food_safety_standards`: Estándares de seguridad alimentaria para materiales de PDV en áreas de alimentos. | `food_contact_product`: Indicación si el textil está destinado para contacto con alimentos (ej., delantales, paños de cocina, textiles para restaurantes). `food_grade_certification`: Certificaciones de grado alimenticio para textiles (ej., OEKO-TEX Standard 100, grado alimenticio). `food_safety_standards`: Estándares de seguridad alimentaria para textiles de cocina. |
| **Productos Tópicos (Piel, Pelo, etc.)** | `topical_product`: Indicación si el producto está destinado para uso tópico (ej., productos de cuidado personal, cosméticos, productos para el pelo). `dermatological_testing`: Pruebas dermatológicas realizadas (ej., hipoalergénico, testado dermatológicamente, pH neutro). `skin_safety_standards`: Estándares de seguridad para la piel (ej., Cosmos, Natrue, dermatológicamente testado). | `topical_product`: Indicación si el material de exhibición está destinado para exhibir productos tópicos o cosméticos. `dermatological_testing`: No aplicable para materiales de exhibición. `skin_safety_standards`: Estándares de seguridad para exhibidores de productos tópicos. | `topical_product`: Indicación si el textil está destinado para uso tópico (ej., toallas faciales, textiles para spa, ropa de cama para piel sensible). `dermatological_testing`: Pruebas dermatológicas para textiles (ej., hipoalergénico, pH neutro, testado dermatológicamente). `skin_safety_standards`: Estándares de seguridad para textiles en contacto con la piel (ej., OEKO-TEX Standard 100, algodón orgánico certificado). |
| **Niveles de AQL (Acceptable Quality Level)** | `aql_level`: Nivel de AQL específico para el producto (ej., AQL 2.5 para productos estándar, AQL 1.0 para productos premium, AQL 4.0 para productos básicos). `aql_inspection_level`: Nivel de inspección AQL (ej., Nivel II estándar, Nivel I reducido, Nivel III aumentado). `aql_sample_size`: Tamaño de muestra para inspección AQL según el lote. `quality_inspection_frequency`: Frecuencia de inspección de calidad (ej., 100% para productos críticos, muestreo para productos estándar). | `aql_level`: Nivel de AQL para materiales de exhibición (ej., AQL 2.5 para displays estándar, AQL 1.0 para exhibidores premium). `aql_inspection_level`: Nivel de inspección AQL para exhibidores (ej., Nivel II estándar, Nivel I para displays simples). `aql_sample_size`: Tamaño de muestra para inspección AQL de exhibidores. `quality_inspection_frequency`: Frecuencia de inspección para materiales de PDV (ej., 100% para exhibidores críticos, muestreo para displays estándar). | `aql_level`: Nivel de AQL para textiles (ej., AQL 2.5 para textiles estándar, AQL 1.0 para textiles premium, AQL 4.0 para textiles básicos). `aql_inspection_level`: Nivel de inspección AQL para textiles (ej., Nivel II estándar, Nivel I para textiles básicos). `aql_sample_size`: Tamaño de muestra para inspección AQL de textiles. `quality_inspection_frequency`: Frecuencia de inspección para textiles (ej., 100% para textiles premium, muestreo para textiles estándar). |
| **Margen de Error y Saldos de Producción** | `defect_percentage`: Porcentaje de error esperado en producción (ej., 2.5% para lápices, 1% para vasos de cristal, 5% para productos complejos). `overage_quantity`: Cantidad adicional a fabricar para compensar defectos (ej., 3% para lápices, 5% para vasos de cristal, 8% para productos frágiles). `production_buffer`: Buffer de producción adicional (ej., 2% para productos simples, 5% para productos complejos, 10% para productos críticos). `rework_percentage`: Porcentaje de productos que requieren re-trabajo (ej., 1% para productos estándar, 3% para productos complejos). | `defect_percentage`: Porcentaje de error esperado en exhibidores (ej., 3% para displays simples, 1.5% para exhibidores complejos). `overage_quantity`: Cantidad adicional para compensar defectos en exhibidores (ej., 4% para displays, 6% para exhibidores complejos). `production_buffer`: Buffer de producción para exhibidores (ej., 3% para displays simples, 7% para exhibidores complejos). `rework_percentage`: Porcentaje de exhibidores que requieren re-trabajo (ej., 2% para displays estándar, 4% para exhibidores complejos). | `defect_percentage`: Porcentaje de error esperado en textiles (ej., 2% para poleras básicas, 1% para textiles premium, 3% para textiles complejos). `overage_quantity`: Cantidad adicional para compensar defectos en textiles (ej., 3% para poleras, 5% para textiles premium, 7% para textiles complejos). `production_buffer`: Buffer de producción para textiles (ej., 2% para textiles básicos, 5% para textiles premium, 8% para textiles complejos). `rework_percentage`: Porcentaje de textiles que requieren re-trabajo (ej., 1% para textiles estándar, 3% para textiles complejos). |



#### Comercial y Abastecimiento
| Dimensión de Especificación      | Merchandising | Material PDV y Exhibición | Textiles |
|----------------------------------|---------------|---------------------------|----------|
| **Precio Unitario** | `exw_unit_cost`: Costo unitario Ex-Works (EXW) del proveedor. `fob_unit_cost`: Puerto y costo unitario Free on Board (FOB). `cif_unit_cost`: Costo, Seguro y Flete (CIF) o Costo con Entrega y Derechos Pagados (DDP) (si aplica). `volume_price_tiers`: Escalas de precios por volumen (ej., precio por 100 unidades, 500 unidades, 1000 unidades). `setup_charges`: Cargos de configuración o de moldes (si aplica, y cómo se amortizan o cobran). | `exw_unit_cost`: Costo unitario Ex-Works (EXW) del proveedor para exhibidores. `fob_unit_cost`: Puerto y costo unitario Free on Board (FOB) para PDV. `cif_unit_cost`: Costo, Seguro y Flete (CIF) para materiales de exhibición. `volume_price_tiers`: Escalas de precios por volumen para exhibidores. `setup_charges`: Costos de configuración y matricería del display. | `exw_unit_cost`: Costo unitario Ex-Works (EXW) del proveedor para textiles. `fob_unit_cost`: Puerto y costo unitario Free on Board (FOB) para textiles. `cif_unit_cost`: Costo, Seguro y Flete (CIF) para textiles. `volume_price_tiers`: Especificaciones de precios por volumen para textiles. `setup_charges`: Cargos de configuración para textiles (si aplica). |
| **Incoterms** | `fob_unit_cost`: Términos de comercio estandarizados que definen las responsabilidades del comprador/vendedor (ej., FOB, EXW, DDP). `cif_unit_cost`: Especificaciones de términos de despacho. `country_of_origin`: País de Origen (COO) para la fabricación. | `fob_unit_cost`: Incoterms aplicables para la importación/exportación de materiales de PDV. `cif_unit_cost`: Términos de despacho internacional para exhibidores. `country_of_origin`: País de Origen (COO) para materiales de PDV. | `fob_unit_cost`: Incoterms acordados para despachos internacionales de textiles. `cif_unit_cost`: Especificaciones de costo entregado para textiles. `country_of_origin`: País de Origen (COO) para textiles. |
| **MOQ (Cantidad Mínima de Pedido)** | `moq_per_sku`: MOQ por producto/SKU/color. `moq_per_order`: MOQ por pedido total a un proveedor. `preferred_supplier`: Nombre(s) y contacto(s) del proveedor preferido. `supplier_part_number`: Número de parte o código interno del proveedor. | `moq_per_sku`: MOQ basado en el volumen de pedido del exhibidor o material de PDV. `moq_per_order`: Mínimos de proyectos de exhibición. `preferred_supplier`: Proveedor preferido para materiales de PDV. `supplier_part_number`: Número de parte del proveedor para exhibidores. | `moq_per_sku`: MOQ para ropa o textiles, potencialmente basado en el desglose por color/talla. `moq_per_order`: Mínimos de pedido textil. `preferred_supplier`: Proveedor preferido para textiles. `supplier_part_number`: Número de parte del proveedor para textiles. |
| **Plazo de Entrega (Lead Times)** | `sampling_lead_time`: Plazo de entrega para muestreo (para muestras de pre-producción). `sample_approval_type`: Tipo de aprobación de muestra (ej., física, digital, híbrida). `production_lead_time`: Plazo de entrega para producción en masa (después de la aprobación de la muestra, desde la emisión de la OC hasta la salida de fábrica). `peak_season_variations`: Variaciones en los plazos de entrega en temporada alta. | `sampling_lead_time`: Proceso para aprobar maquetas o muestras de materiales de PDV antes de la producción en masa. `sample_approval_type`: Tipo de aprobación de muestra para exhibidores (ej., física, digital, maqueta 3D). `sample_approval_process`: Proceso de aprobación para displays (ej., revisión de prototipos físicos, renderizados digitales, muestras a escala). `production_lead_time`: Cronograma desde la aprobación hasta la entrega de exhibidores. `peak_season_variations`: Variaciones estacionales para materiales de PDV. | `sampling_lead_time`: Procedimiento para aprobar muestras de color (strike-offs) o muestras de pre-producción para textiles. `sample_approval_type`: Tipo de aprobación de muestra para textiles (ej., física, digital, strike-offs de color). `sample_approval_process`: Proceso de aprobación para textiles (ej., muestras de tela física, strike-offs de color, muestras de confección). `production_lead_time`: Tiempo requerido para la producción y entrega de textiles. `peak_season_variations`: Consideraciones de tiempo estacionales para textiles. |


#### Atributos Particulares por Categoría
| Dimensión de Especificación      | Merchandising | Material PDV y Exhibición | Textiles |
|----------------------------------|---------------|---------------------------|----------|
| **Capacidad de Carga y Distribución de Peso** | `maximum_load_capacity`: Requisitos de durabilidad bajo uso (ej., para bolsos, soportes de exhibición, repisas). `weight_distribution`: Consideraciones sobre la distribución del peso (para la estabilidad de los exhibidores). | `maximum_load_capacity`: Capacidad de peso para unidades de exhibición o soportes, como límites de carga en repisas, capacidad de soporte de stands. `weight_distribution`: Distribución del peso para estabilidad y seguridad. | `maximum_load_capacity`: Generalmente no es relevante a menos que la tela se use para aplicaciones de alta resistencia (ej., bolsos de carga pesada, textiles técnicos). `weight_distribution`: Consideraciones de peso para prendas o accesorios. |
| **Tipo de Ensamblaje y Estructura** | `assembly_required`: Típicamente no aplicable a merchandising a menos que se requiera ensamblaje. `assembly_type`: Tipo de ensamblaje (ej., autoensamblaje con instrucciones, pre-ensamblado, parcialmente ensamblado, componentes modulares). `joining_mechanisms`: Herrajes o componentes de ensamblaje si es necesario (ej., tornillos, pernos, encaje a presión, piezas interconectables, tiras adhesivas). `structural_design`: Diseño estructural (ej., pop-up, roller, tipo A, voladizo, inflable, marco rígido). `number_of_parts`: Número de partes o componentes. | `assembly_required`: Cómo se ensamblan o estructuran los materiales de exhibición (ej., empaque plano, pre-ensamblado, plegable). `assembly_type`: Tipo de ensamblaje para exhibidores (ej., autoensamblaje, profesional, modular). `joining_mechanisms`: Mecanismos de unión (ej., tornillos, pernos, encaje a presión, tiras adhesivas). `structural_design`: Marco y tipo de construcción del display (ej., pop-up, roller, tipo A, voladizo, inflable, marco rígido). `number_of_parts`: Número de componentes del exhibidor. | `assembly_required`: No aplicable para textiles a menos que tengan un embalaje integrado o ensamblaje para la venta minorista. `assembly_type`: Tipo de ensamblaje si aplica. `joining_mechanisms`: Mecanismos de unión para textiles técnicos. `structural_design`: Diseño estructural para textiles especializados. `number_of_parts`: Componentes de prendas o textiles complejos. |
| **Corte / Calce / Patrón (Textiles)** | `fit_style`: Típicamente no relevante para merchandising a menos que el producto sea vestuario o productos blandos. `garment_construction`: Detalles de confección si aplica. `fabric_pattern`: Patrón de la tela (ej., Sólido, Jaspeado (Heather), Melange, Rayado, a Cuadros, Dobby, Jacquard, Impresión completa personalizada). `neckline_style`: Estilo del cuello si aplica. `sleeve_style`: Largo/estilo de manga si aplica. `hem_style`: Estilo del dobladillo si aplica. `specific_features`: Características específicas (ej., bolsillos, cierres, botones, cordones). | `fabric_pattern`: No aplicable a materiales de PDV a menos que involucre elementos textiles o de tela. `structural_design`: Patrones del marco del exhibidor (ej., pop-up, roller, tipo A, voladizo, inflable, marco rígido). `assembly_type`: Tipo de ensamblaje para exhibidores. `joining_mechanisms`: Mecanismos de unión para displays. `number_of_parts`: Número de componentes del exhibidor. | `fit_style`: El corte, calce y estilo del textil (ej., Corte Clásico, Corte Regular, Corte Ajustado (Slim Fit), Corte Atlético, Corte Suelto, Oversized, Unisex, Corte de Hombre, Corte de Mujer, Juvenil). `garment_construction`: Detalles de confección de la prenda (ej., mangas montadas, mangas raglán, costuras laterales, tejido tubular, costura de doble aguja, costuras planas/flatlock). `fabric_pattern`: Patrón de la tela (ej., Sólido, Jaspeado (Heather), Melange, Rayado, a Cuadros, Dobby, Jacquard, Impresión completa personalizada). `neckline_style`: Estilo del cuello (ej., Cuello Redondo, Cuello en V, Cuello Ancho, Cuello Polo, con Capucha). `sleeve_style`: Largo/estilo de manga (ej., Manga Corta, Manga Larga, Manga ¾, Sin Mangas, con Puño). `hem_style`: Estilo del dobladillo (ej., recto, curvo, con aberturas laterales). `specific_features`: Características específicas (ej., bolsillos, cierres, botones, cordones). |
| **Rango de Tallas (Ropa)** | `available_sizes`: No relevante a menos que el merchandising sea ropa. `sizing_chart`: Especificaciones de talla si aplica. `sizing_tolerance`: Tolerancia para las medidas de las tallas (ej., +/- 2.5 cm). `international_size_conversions`: Conversiones de tallas internacionales si aplica (ej., US, EU, UK). | `available_sizes`: No aplicable a materiales de exhibición a menos que incluyan variaciones de tamaño para diferentes montajes. `dimensional_tolerances`: Especificaciones de variación de tamaño (ej., +/- 1mm, +/- 5%). `specific_part_dimensions`: Dimensiones de componentes específicos del exhibidor. | `available_sizes`: Las tallas disponibles para prendas o productos a base de textiles (ej., XS, S, M, L, XL, XXL, 2XL, 3XL, etc.; o tallas numéricas como 8, 10, 12). `sizing_chart`: Tabla de tallas específica con medidas clave (ej., ancho de pecho, largo del cuerpo, largo de manga, cintura, entrepierna) por talla. `sizing_tolerance`: Tolerancia para las medidas de las tallas (ej., +/- 2.5 cm). `international_size_conversions`: Conversiones de tallas internacionales si aplica (ej., US, EU, UK). |
| **Instrucciones de Cuidado** | `washing_instructions`: Instrucciones para limpiar o mantener el producto (ej., lavar a máquina, lavar a mano, no lavar). `drying_instructions`: Instrucciones de secado (ej., secar en secadora a baja/media temperatura, colgar/secar al aire, secar en plano, no usar secadora). `ironing_instructions`: Instrucciones de planchado (ej., plancha fría/tibia/caliente, no planchar, planchar por el reverso). `bleaching_instructions`: Instrucciones de blanqueo (ej., no usar cloro, solo blanqueador sin cloro). `dry_cleaning_instructions`: Instrucciones de lavado en seco (ej., solo lavado en seco, solvente específico). `care_symbols`: Símbolos de cuidado estándar (ISO o GINETEX) para usar en la etiqueta. `storage_sensitivities`: Requisitos de mantención. | `storage_requirements`: Típicamente no necesario para materiales de PDV a menos que haya un requisito de mantención continua. `storage_temperature`: Condiciones de almacenamiento recomendadas (ej., rango de temperatura, niveles de humedad, lejos de la luz solar directa). `stacking_limitations`: Limitaciones de apilamiento para productos empaquetados. `storage_sensitivities`: Consideraciones ambientales (ej., a la humedad, luz, temperaturas extremas). | `washing_instructions`: Instrucciones para el cuidado de la tela (ej., lavar a máquina con agua fría/tibia, lavar a mano, no lavar). `drying_instructions`: Instrucciones de secado (ej., secar en secadora a baja/media temperatura, colgar/secar al aire, secar en plano, no usar secadora). `ironing_instructions`: Instrucciones de planchado (ej., plancha fría/tibia/caliente, no planchar, planchar por el reverso). `bleaching_instructions`: Instrucciones de blanqueo (ej., no usar cloro, solo blanqueador sin cloro). `dry_cleaning_instructions`: Instrucciones de lavado en seco (ej., solo lavado en seco, solvente específico). `care_symbols`: Símbolos estándar y requisitos de instrucciones de cuidado (ISO o GINETEX). |