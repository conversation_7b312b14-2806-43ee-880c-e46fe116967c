# Principales Decisiones de Arquitectura Laravel 12 en PSS

## 1. **Stack Tecnológico Base**
- Laravel 12 con PHP 8.4 como backend principal
- PostgreSQL como base de datos (puerto no estándar 55432)
- Pest 4 como framework de testing (no PHPUnit)
- Tailwind CSS 4 para styling

## 2. **Frontend y UI Architecture**
- Livewire 3 + Volt para interactividad (no Vue/React SPA)
- Flux UI component library para componentes base  
- Class-based Volt components (no functional API)
- Filament 4 como panel administrativo completo

## 3. **Autenticación y Autorización**
- Spatie <PERSON>vel Permission para RBAC
- Filament Shield para gestión de permisos en admin panel
- Roles definidos: `super_admin`, `admin`, `panel_user`
- `Gate::before` para super admin access bypass

## 4. **Database Design Conventions**
- **CRÍTICO**: Solo autoincremental `id()` PKs (NO UUIDs/ULIDs)
- `foreignId()->constrained()` para todas las foreign keys
- Validaciones FK: `integer|exists:table,id` pattern
- Command verificación: `rg -n "\buuid\b|HasUuids|ulid\("`

## 5. **Arquitectura de Dominio (DDD-Inspired)**
- Actions pattern para lógica de negocio compleja
- DTOs (Data Transfer Objects) para transferencia de datos
- Value Objects para conceptos de dominio (Money, Currency)
- Service classes para operaciones específicas

## 6. **Models y Relationships**
- Laravel 12 `casts()` method (no `$casts` property)
- Typed relationships con return type hints
- Eager loading obligatorio para prevenir N+1 queries
- Eloquent sobre raw queries siempre

## 7. **Filament Resources Architecture**
- Estructura altamente organizada por entidad:
  ```
  app/Filament/Resources/EntityName/
  ├── EntityNameResource.php
  ├── Pages/
  ├── Schemas/ (Forms + Infolists)
  ├── Tables/
  └── RelationManagers/
  ```

## 8. **Validation and Forms**
- Form Request classes obligatorias para validación
- Mensajes en español en `lang/es/validation.php`
- Patterns específicos array vs string validation rules

## 9. **Testing Strategy**
- Pest syntax exclusivamente (no PHPUnit syntax)
- Browser testing capabilities integradas
- Model factories extensivo uso
- Estructura: `tests/Feature/` y `tests/Unit/`

## 10. **Queue y Background Processing**
- Database queue connection (no Redis en desarrollo)
- `php artisan queue:listen --tries=1` para desarrollo
- Logs con `php artisan pail --timeout=0`

## 11. **Asset Building y Development**
- Vite para asset compilation
- CORS enabled en development
- `composer run dev` para ambiente completo
- `vendor/bin/pint` para code formatting

## 12. **Business Logic Patterns**
- Actions para operaciones complejas transaccionales
- DTOs para input/output data contracts
- Database transactions para operaciones críticas
- EventLog para auditoría de acciones importantes

## 13. **Money y Currency Handling**
- Custom Value Object `Money` para cálculos monetarios
- Custom Cast `MoneyCast` para persistencia
- DTO `Currency` con formateo localizado
- `Tax` DTO para cálculos fiscales por país

## 14. **Domain Model Structure**
- Jerarquía: Customer → Quote → Group → Product → Variant
- Product Taxonomy: Type → Category → Subcategory
- Purchase Order workflow separado de Customer Quote
- Production Batch tracking integrado

## 15. **Development Environment**
- Mail driver `log` para development
- Seeders para datos de prueba consistentes
- `migrate:fresh --seed` para reset completo
- Environment específico para desarrollo local

## 16. **Code Quality y Standards**
- Laravel Pint para formatting automático
- Spanish localization para usuario final
- Correlation IDs para trazabilidad
- Consistent naming patterns (Customer* vs legacy Client*)

## 17. **Component Organization**
- Livewire components en `resources/views/livewire/`
- Organized by feature: `quotes/`, `sourcing/`, `auth/`, `settings/`
- `php artisan make:volt ComponentName --test --pest` convention

## 18. **Error Handling y Logging**
- `ErrorReportingService` para manejo centralizado
- EventLog model para auditoría
- Correlation IDs para tracking cross-service
- Spanish error messages para UX

---

Estas 18 decisiones arquitectónicas forman el esqueleto técnico del proyecto PSS, estableciendo un framework moderno y escalable que combina las mejores prácticas de Laravel 12 con patrones de diseño robustos para el dominio de manufactura y sourcing.