## Plan de Implementación de Especificación de Producto (sin entidad Dimension)

1. Definición de modelo de datos (solo ajustes en existentes)
   1.1. Attribute
   - Agregar columna `key` (string 100, unique, indexed) para mapear a claves técnicas del documento (ej.: `official_product_name`).
   - Agregar columna `unit` (string 32, nullable) para atributos cuantitativos (ej.: `cm`, `mm`, `g`, `kg`, `ml`, `L`).
   - Mantener `type` como string controlado por aplicación (evitar enum DB) con valores esperados: `string`, `text`, `integer`, `decimal`, `boolean`, `date`, `enum`, `json`.
   - Índices: `unique('key')`, `index('type')`.
   
   1.2. AttributeGroup
   - Agregar columna `key` (string 100, unique) para uso interno (ej.: `basic_info`, `core_attributes`, `branding_specs`, `textile_specifics`, `packaging_logistics`, `usage_lifecycle`, `commercial_procurement`, `certifications_requirements`).
   - Agregar columna `dimension` (string 64, indexed) para etiquetar pertenencia a una dimensión raíz (sin FK a otra tabla).
   - Confirmar columnas existentes: `sort_order`, `is_required`, `status`.
   
   1.3. ProductTemplateAttribute
   - Agregar `unique` compuesto `unique(product_template_id, attribute_id)` para evitar duplicados.
   - Índices en FKs: `product_template_id`, `attribute_id`, `attribute_group_id`.

2. Migraciones
   2.1. Crear migración `alter_attributes_add_key_unit_type_indexes`:
   - `table('attributes')`: `key` unique, `unit` nullable, `index('type')`.
   - Data migration: backfill `key` inicial con `Str::slug(name, '_')` asegurando unicidad (añadir sufijo incremental si colisiona).
   
   2.2. Crear migración `alter_attribute_groups_add_key_dimension`:
   - `table('attribute_groups')`: `key` unique, `dimension` index.
   - Data migration: backfill `key` con `Str::slug(name, '_')`; `dimension` según heurística por nombre de grupo (ej.: si contiene "Nombre"/"Descripción" => `basic_info`, etc.) o dejar `null` y poblar vía seeder.
   
   2.3. Crear migración `update_pta_unique_indexes`:
   - `table('product_template_attributes')`: crear índice único `(product_template_id, attribute_id)` y agregar índices a FKs faltantes.

3. Modelos (edits)
   3.1. Attribute
   - `protected $fillable` incluir `key`, `unit`.
   - `casts()` sin cambios; mantener arrays para `validation_rules`, `options`, `is_required` boolean.
   - Scopes: `scopeByKey(string $key)`, `scopeOfType(string $type)`.
   
   3.2. AttributeGroup
   - `protected $fillable` incluir `key`, `dimension`.
   - Scopes: `scopeByKey(string $key)`, `scopeInDimension(?string $dimension)`.
   
   3.3. ProductTemplate
   - Eager loading helpers para ficha: método `loadSpecification()` que haga `load(['attributeGroups.attributes'])`.
   
   3.4. ProductVariant
   - Ya existe `attributeValuesWithAttributes()`; usarlo para lecturas sin N+1.

4. Seeders y datos iniciales
   4.1. Extender `ProductTaxonomySeeder`
   - Insertar/actualizar `AttributeGroup` por cada dimensión raíz usando `key` y `dimension` (una por dimensión o las necesarias según diseño actual).
   - Insertar/actualizar `Attribute` usando `key` exactas desde `/_docus/_clean/listado_dimensiones_especificacion.md` (mantener nombre legible en `name`).
   - Asociar atributos a grupos mediante `ProductTemplateAttribute` pivotando por `ProductType` (y permitiendo excepciones por plantilla cuando aplique), no solo por subcategoría.
   - Táctica idempotente: usar `upsert` por `key`.
   
   4.2. Estrategia de mapeo
   - Regla: atributos “constantes” de ficha del producto van en `Attribute`; valores por variante van en `ProductVariantAttributeValue`.
   - Campos puramente transaccionales (precios por proveedor, incoterms, lead times) permanecen en `SupplierQuote`/`ProcurementLot`.

5. Validación y tipado en aplicación
   5.1. Reglas por atributo
   - Mantener `validation_rules` (array) por `Attribute`; aplicar en UI (Filament) al renderizar campos.
   
   5.2. Coherencia `type`/`unit`
   - En creación/edición de `Attribute`, validar que `unit` no se defina si `type` ∉ {`integer`,`decimal`}.
   
   5.3. Atributos `enum`
   - Exigir `options` no vacías cuando `type` = `enum`.

6. UI (Filament) – alto nivel
   6.1. `ProductTemplateForm`
   - Construir schema a partir de `attributeGroups()->with('attributes')` respetando `sort_order`.
   - Para cada `ProductTemplateAttribute`: seleccionar componente según `Attribute::type` (`TextInput`, `Textarea`, `Select`, `Toggle`, `DatePicker`, `KeyValue`, `NumberInput` con `suffix` `unit`).
   - Aplicar `->required()` si `is_required` en PTA o por regla del grupo.
   - Mostrar `ProductType.hs_code` como metadato de solo lectura (badge/field) en el contexto de la plantilla; editarlo únicamente en `ProductType`.
   
   6.2. Performance
   - Eager load en páginas de edición/creación de plantillas y variantes.

7. Pruebas (Pest)
   7.1. Migraciones y modelos
   - Test de unicidad `Attribute.key` y `AttributeGroup.key`.
   - Test de `ProductTemplateAttribute` unique `(product_template_id, attribute_id)`.
   
   7.2. Semántica de tipos/unidades
   - Casos negativos: `unit` con `type` no numérico → error.
   - `enum` sin `options` → error.
   
   7.3. Renderizado de formulario (solo lógica personalizada)
   - Verificar que campos se generan con el componente correcto según `type` y que `unit` aparece como `suffix`.

8. Despliegue y migración de datos
   8.1. Orden de ejecución
   - Ejecutar migraciones.
   - Ejecutar seeder extendido (`ProductTaxonomySeeder`).
   - Verificar colisiones de `key` en datos existentes; resolver con sufijos.
   
   8.2. Retrocompatibilidad
   - Mantener nombres legibles en `name` y usar `key` para referencias internas/automatizaciones.

9. Observabilidad y mantenimiento
   9.1. Logs en seeder para reportar atributos no mapeados.
   9.2. Documentar nuevas `key` y convenciones de `unit` en README de desarrollo.

10. Checklist de aceptación
   - Atributos/grupos con `key` únicas y búsquedas por `key`.
   - Formulario de plantilla construido dinámicamente por grupos/atributos.
   - Validaciones aplicadas por `type`/`validation_rules`/`is_required`.
   - Tests de unicidad, tipos/unidades y generación de UI pasados.
