# Convenciones de Git - Agente Cursor

## Prefijo para Commits del Agente Cursor

**IMPORTANTE**: To<PERSON> los commits realizados por el agente Cursor deben comenzar con el prefijo `agente-cursor:` seguido de un espacio y luego el mensaje descriptivo del commit.

### Formato Requerido:
```
agente-cursor: [tipo]: [descripción del cambio]
```

### Ejemplos Correctos:
- `agente-cursor: Fix: Use correct Filament 4 schema components`
- `agente-cursor: feat: Add new SourcingPlanPage for customer quotes`
- `agente-cursor: refactor: Migrate Livewire components to Filament`
- `agente-cursor: docs: Update migration documentation`

### Tipos de Commit Recomendados:
- `feat:` - Nueva funcionalidad
- `fix:` - Corrección de bugs
- `refactor:` - Refactorización de código
- `docs:` - Cambios en documentación
- `style:` - Cambios de formato/estilo
- `test:` - Agregar o modificar tests
- `chore:` - <PERSON><PERSON><PERSON> de mantenimiento

### Propósito:
Esta convención permite:
1. **Identificar fácilmente** qué commits fueron realizados por el agente de IA
2. **Distinguir** entre commits automáticos y manuales del usuario
3. **Mantener un historial claro** de cambios automatizados
4. **Facilitar la revisión** de código y seguimiento de cambios

### Implementación:
- Aplicar esta convención a **todos los commits futuros** realizados por el agente
- Mantener consistencia en el formato del mensaje
- Incluir descripción clara y concisa del cambio realizado

### Nota sobre Commits Anteriores:
Los commits realizados antes de la implementación de esta convención (commits anteriores al `4a3e373`) no siguieron esta regla, pero a partir del commit `4a3e373` se aplicará consistentemente.

## Prefijo para Ramas del Agente Cursor

**IMPORTANTE**: Todas las ramas creadas por el agente Cursor deben comenzar con el prefijo `ag_cursor-` seguido del nombre descriptivo de la rama.

### Formato Requerido:
```
ag_cursor-[descripción-de-la-rama]
```

### Ejemplos Correctos:
- `ag_cursor-livewire-to-filament-migration`
- `ag_cursor-fix-schema-components`
- `ag_cursor-add-new-features`
- `ag_cursor-refactor-database-models`

### Propósito:
Esta convención permite:
1. **Identificar fácilmente** qué ramas fueron creadas por el agente de IA
2. **Distinguir** entre ramas automáticas y manuales del usuario
3. **Mantener un historial claro** de ramas de trabajo automatizadas
4. **Facilitar la gestión** de ramas y seguimiento de cambios

### Commits con Convención Aplicada:
- `4a3e373` - agente-cursor: docs: Add Git commit message conventions documentation
- `4dabcf8` - agente-cursor: fix: Remove unsupported methods from Text components
- `a2bf6c8` - agente-cursor: docs: Update Git conventions with implementation notes

### Ramas con Convención Aplicada:
- `ag_cursor-livewire-to-filament-migration` - Migración completa de Livewire a Filament

---
*Documento creado: 2025-09-14*
*Última actualización: 2025-09-14*
