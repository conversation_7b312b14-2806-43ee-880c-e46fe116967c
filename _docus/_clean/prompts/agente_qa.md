# Guía del Agente QA — Diseño de Tests (Laravel 12 + Filament 4 + Pest 4)

Esta guía define criterios y prácticas para el diseño, alcance y ejecución de pruebas en proyectos Laravel 12 con Filament 4 y Pest 4. Combina el enfoque de `_docs/prompts/qa_filament.md` con capacidades de Pest 4 (incluido browser testing con Playwright) y patrones de Filament 4.

## Principios
- <PERSON> (acotado):
  - No probamos Filament 4 ni Laravel en sí. Confiamos en sus componentes tal como están documentados.
  - Sí probamos comportamiento de aplicación cuando extendemos/alteramos componentes (overrides, `->reactive()`, `afterStateUpdated`, esquemas dinámicos, acciones con efectos laterales).
- Negativos primero: priorizar caminos de error, bordes y validación.
- Determinismo: base de datos limpia por test, datos vía factories, reloj congelado.
- Código en inglés, descripciones/etiquetas en español: nombres de clases/tablas/columnas en inglés; copy visible en español.
- Base de datos: usa el motor real del proyecto en testing. Si es PostgreSQL, puedes cubrir JSONB/GIN y `CHECK`; si es MySQL/MariaDB/SQLite, adapta las pruebas a sus capacidades.

## Tecnología y Setup
- Runner: Pest 4 (Pest v4).
- Component tests: Livewire/Filament con helpers oficiales de Filament 4.
- Browser tests: Pest Browser (Playwright), uso mínimo para journeys críticos.
- PostgreSQL en testing (`.env.testing`): usar JSONB real y constraints.
- Fakes/tiempo: `Bus::fake()`, `Mail::fake()`, `Notification::fake()`, `Queue::fake()`, `Storage::fake()`, `Carbon::setTestNow(now())`.
- `uses(RefreshDatabase::class)` global en suites de Feature.

## Alcance por tipo de prueba
- Dominio/Eloquent (Feature/Domain):
  - Reglas de negocio, máquinas de estados, unicidad/consistencia, constraints lógicas, consultas sobre columnas JSON cuando apliquen, generación de identificadores o códigos.
- Component/Livewire/Filament (Feature/Filament):
  - Esquemas dinámicos (agrupación/orden/visibilidad), validación combinada (reglas por tipo + reglas del contexto), acciones personalizadas con efectos laterales.
  - No verificar rendering genérico; sí la presencia/configuración de componentes/props cuando impactan reglas.
- Navegador (Feature/Browser):
  - Sólo journeys críticos end‑to‑end: creación/edición de entidades con formularios dinámicos, aplicación de filtros en listados, ejecución de un flujo transaccional con cambios de estado, y verificación de autorización básica.

## Cuándo probar componentes de Filament
Escribir tests de componentes si hay:
- Overrides/extensiones de componentes o macros.
- Campos `->reactive()`/`afterStateUpdated()` con efectos (p. ej., autogeneración de identificadores, reseteo de campos dependientes).
- Esquemas generados desde catálogo/pivot que impactan validación/visibilidad/orden.
- Acciones con efectos transaccionales (snapshots, unicidad condicionada por criterios de negocio).

## Reglas no negociables
- Autorización: cubrir `assertForbidden()` y caminos permitidos.
- Atomicidad: acciones críticas envueltas en transacción; probar rollback ante fallos (p. ej., índice parcial de `purchase_conditions`).
- Unicidad/consistencia: garantizar índices lógicos (por app si SQLite; por motor cuando aplique), validar errores de duplicidad (claves únicas, pivots, posiciones/orden).
- Datos: siempre via factories; sin fixtures estáticos.

-## Convenciones de nombres y estructura
- Código en inglés (clases/tablas/columnas); descripciones y UI en español si aplica.
- Árbol sugerido:
  - `tests/Feature/Domain/*.php` (reglas de negocio y Eloquent)
  - `tests/Feature/Filament/*.php` (resources, schemas, actions)
  - `tests/Feature/Browser/*.php` (journeys Playwright)
  - `tests/Feature/Auth/AuthorizationPoliciesTest.php`

## Browser Testing (Pest 4 + Playwright)
- Instalación:
  - `composer require pestphp/pest-plugin-browser --dev`
  - `npm install playwright@latest && npx playwright install`
- Ejecución:
  - `./vendor/bin/pest` | `--parallel` | `--browser=firefox` | `--headed` | `--debug`
- Convenciones:
  - `visit('/')` → `$page` para interactuar (`click`, `type`, `press`, `assertSee`, `assertUrlIs`, `assertNoSmoke`).
  - Multipágina: `visit(['/', '/about'])->assertNoSmoke()`.
  - Viewports: `$page->on()->mobile()` / `->iPhone14Pro()`; `->inDarkMode()`.
- CI: instalar Node LTS, `npm ci`, `npx playwright install --with-deps`, ejecutar `./vendor/bin/pest --ci --parallel`.

## Diseño de suites (mínimas)
- Dominio (reglas y persistencia):
  - Validación de enums/opciones; unicidad en pivots; orden/posición única por agrupación; transiciones de estado válidas/ inválidas.
- Filament (esquemas y acciones):
  - Construcción de formularios dinámicos por grupos/orden/visibilidad; combinación de reglas de validación por tipo + contexto; acciones con efectos (duplicación, publicación, archivado) y sus side-effects.
- Listados y filtros:
  - Columnas derivadas (incluyendo JSON cuando exista), filtros múltiples y persistencia de estado de filtros.
- Autorización (Auth):
  - Accesos a recursos/acciones restringidas por roles/policies; caminos prohibidos vs permitidos.
- Browser (journeys mínimos):
  - Smoke de panel y listados clave (`assertNoSmoke`).
  - Alta/edición con formulario dinámico (creación exitosa y errores de validación).
  - Aplicación de filtros y verificación de resultados.
  - Ejecución de un flujo transaccional con cambio de estado y verificación de resultados finales.
  - Acceso restringido vs autorizado.

## Prácticas de implementación
- `uses(RefreshDatabase::class); beforeEach(Carbon::setTestNow(now()));`
- Fakes según necesidad de la prueba (no fakes “globales” si no hay efectos).
- Descripción BDD: `it('rechaza enum fuera de options_json', …)`.
- Asserts de esquema Filament: usar helpers de testing 4.x (p. ej., `assertSchemaComponentExists`, `assertHasFormErrors`).
- JSON/estructuras anidadas: cuando existan, usar rutas de clave/selector soportadas por la base de datos y por las tablas/columnas.

## Criterios de salida
- Cobertura orientativa: 80–85% líneas/mutación para módulos críticos.
- Journeys browser: pocos y estables; usar `assertNoSmoke()` y asserts de resultado finales.
- Revisión de PR: checklist con autorización, negativos, transacción/rollback, unicidad, Postgres-real.

---

Sigue esta guía como complemento operativo de `_docs/prompts/qa_filament.md` para que las pruebas sean precisas, deterministas y enfocadas en el valor de negocio.
