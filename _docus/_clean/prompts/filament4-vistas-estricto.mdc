# Reglas Estrictas para Implementar Vistas con Filament 4

## 🎯 Principios Fundamentales

### 1. **Usar Componentes Oficiales Exclusivamente**
- **NUNCA** crear HTML personalizado sin usar componentes nativos de Filament 4
- **SIEMPRE** preferir `RepeatableEntry`, `TextEntry`, `Section`, `Grid` sobre HTML crudo
- **EXCEPCIÓN**: Solo usar HTML personalizado cuando es imposible lograr el diseño con componentes nativos

### 2. **Regla de Oro: Campos Existentes**
```php
// ❌ INCORRECTO - Campo inexistente
TextEntry::make('mi_campo_personalizado')
    ->formatStateUsing(fn () => 'contenido')

// ✅ CORRECTO - Campo existente en el modelo
TextEntry::make('id') // o cualquier campo que exista
    ->formatStateUsing(fn ($state, $record) => 'contenido')
```

**CRÍTICO**: `formatStateUsing` solo se ejecuta si Filament encuentra el campo en el modelo.

## 🔧 Implementación de TextEntry

### 3. **Renderizado HTML Seguro**
```php
// ❌ INCORRECTO - HTML escapado
TextEntry::make('id')
    ->formatStateUsing(fn () => '<div>HTML</div>')
    ->html()

// ✅ CORRECTO - HtmlString explícito
TextEntry::make('id')
    ->formatStateUsing(fn ($state, $record) => new HtmlString('<div>HTML</div>'))
```

### 4. **Parámetros de formatStateUsing**
```php
// ✅ SIEMPRE usar ambos parámetros
TextEntry::make('id')
    ->formatStateUsing(fn ($state, $record) => {
        // $state: valor del campo del modelo
        // $record: modelo completo
        return new HtmlString($this->renderContent($record));
    })
```

### 5. **Acceso al Record**
```php
// ❌ MALA PRÁCTICA - Depender de $this->record
TextEntry::make('id')
    ->formatStateUsing(fn () => $this->record->name)

// ✅ BUENA PRÁCTICA - Usar parámetro $record
TextEntry::make('id')
    ->formatStateUsing(fn ($state, $record) => $record->name)
```

## 📐 Layout y Estructura

### 6. **Layout de Una Columna**
```php
// ✅ Para forzar layout de una columna
Section::make('Mi Sección')
    ->schema([
        TextEntry::make('id')
            ->columnSpanFull(), // Forzar ancho completo
    ])
    ->columnSpanFull() // Forzar sección de ancho completo
```

### 7. **Estructura de Secciones**
```php
// ✅ Estructura recomendada
Section::make('Información Principal')
    ->description('Datos básicos')
    ->schema([
        TextEntry::make('name'),
        TextEntry::make('email'),
    ])
    ->columnSpanFull(),

// ✅ Secciones condicionales
...($record->groups->count() > 0 ? [
    Section::make('Grupos')
        ->schema([...])
        ->columnSpanFull(),
] : []),
```

## 🎨 Componentes para Listas

### 8. **RepeatableEntry vs TextEntry con HTML**
```php
// ✅ PREFERIR - Para listas de relaciones
RepeatableEntry::make('products') // Relación HasMany
    ->schema([
        TextEntry::make('name'),
        TextEntry::make('price'),
    ])
    ->columns(2)

// ✅ USAR SOLO CUANDO - Necesitas diseño muy específico
TextEntry::make('id')
    ->formatStateUsing(fn ($state, $record) => new HtmlString($this->renderCustomList($record)))
```

### 9. **CRÍTICO: APIs Separadas de Filament 4**
```php
// ❌ INCORRECTO - Mezclar APIs
use Filament\Schemas\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;

RepeatableEntry::make('groups')
    ->schema([
        InfolistGrid::make(2)->schema([...]) // ❌ No existe
    ])

// ✅ CORRECTO - Solo componentes Infolists
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\TextEntry;

RepeatableEntry::make('groups')
    ->schema([
        TextEntry::make('name'),
        TextEntry::make('id'),
        // Sin Grid anidado - RepeatableEntry solo acepta TextEntry
    ])
```

### 10. **Estructura Correcta de RepeatableEntry**
```php
// ✅ Estructura correcta - Solo TextEntry dentro de RepeatableEntry
RepeatableEntry::make('groups')
    ->schema([
        TextEntry::make('name')->label('Nombre'),
        TextEntry::make('id')->label('ID'),
        TextEntry::make('total_amount')->formatStateUsing(...),
        
        // ✅ Anidar otro RepeatableEntry si es necesario
        RepeatableEntry::make('products')
            ->label('Composición del Kit')
            ->schema([
                TextEntry::make('name'),
                TextEntry::make('quantity'),
            ])
            ->columns(2) // ✅ Esto sí funciona
            ->contained(false), // ✅ Para quitar el card wrapper
    ])
    ->columns(1) // ✅ Layout principal
```

### 11. **Datos Correctos para RepeatableEntry**
```php
// ✅ Para relaciones directas
RepeatableEntry::make('groups') // $record->groups (relación HasMany)

// ✅ Para colecciones personalizadas - Necesita métodos de acceso
RepeatableEntry::make('individual_products') // Necesita getIndividualProductsAttribute()
RepeatableEntry::make('all_variants') // Necesita getAllVariantsAttribute()

// ✅ Método que devuelve objetos con propiedades
public function getAllVariants()
{
    $allVariants = collect();
    
    foreach ($this->record->groups as $group) {
        foreach ($group->products as $product) {
            foreach ($product->variants as $variant) {
                $allVariants->push((object) [ // ✅ (object) para crear stdClass
                    'label' => $variant->label,
                    'id' => $variant->id,
                    'product_name' => $product->name,
                    'context' => 'kit',
                    'quantity' => $variant->quantity,
                    'unit_price' => $variant->unit_price,
                ]);
            }
        }
    }
    
    return $allVariants;
}

// ✅ Método de acceso para RepeatableEntry
public function getAllVariantsAttribute()
{
    return $this->getAllVariants();
}
```

### 12. **Anidación de RepeatableEntry**
```php
// ✅ Para relaciones anidadas
RepeatableEntry::make('products')
    ->schema([
        TextEntry::make('name'),
        RepeatableEntry::make('variants')
            ->schema([
                TextEntry::make('color'),
                TextEntry::make('size'),
            ])
            ->columns(2)
    ])
```

## 🚀 Performance y Optimización

### 13. **Eager Loading Obligatorio**
```php
// ✅ En resolveRecord o en el Resource
protected function resolveRecord(string|int $key): Model
{
    $record = parent::resolveRecord($key);
    $record->loadMissing([
        'products.variants',
        'groups.products.variants',
    ]);
    return $record;
}
```

### 14. **Evitar N+1 Queries**
```php
// ❌ INCORRECTO - N+1 queries
TextEntry::make('id')
    ->formatStateUsing(fn ($state, $record) => 
        $record->products->map(fn($p) => $p->category->name)->join(', ')
    )

// ✅ CORRECTO - Con eager loading
TextEntry::make('id')
    ->formatStateUsing(fn ($state, $record) => 
        $record->products->map(fn($p) => $p->category->name)->join(', ')
    )
```

## 🐛 Debugging y Diagnóstico

### 15. **Debugging de formatStateUsing**
```php
// ✅ Para diagnosticar problemas
TextEntry::make('id')
    ->formatStateUsing(function ($state, $record) {
        // Debug básico
        dd($state, $record);
        
        // O logging
        Log::info('Debug formatStateUsing', [
            'state' => $state,
            'record_id' => $record->id,
            'record_class' => get_class($record)
        ]);
        
        return 'Debug content';
    })
```

### 16. **Verificación de Datos**
```php
// ✅ Verificar datos antes de renderizar
TextEntry::make('id')
    ->formatStateUsing(fn ($state, $record) => {
        if ($record->products->count() === 0) {
            return new HtmlString('<div class="text-gray-500">No hay productos</div>');
        }
        
        return new HtmlString($this->renderProducts($record));
    })
```

## 🎯 Patrones Específicos

### 17. **Patrón para Listas con Diseño Personalizado**
```php
// ✅ Patrón recomendado
TextEntry::make('id')
    ->label('') // Sin label
    ->formatStateUsing(fn ($state, $record) => new HtmlString($this->renderCustomList($record)))
    ->columnSpanFull()

// Método helper
private function renderCustomList($record): HtmlString
{
    $html = '<div class="space-y-4">';
    
    foreach ($record->items as $item) {
        $html .= '<div class="bg-blue-50 rounded-lg p-4">';
        $html .= '<h4>' . e($item->name) . '</h4>';
        $html .= '<p>' . e($item->description) . '</p>';
        $html .= '</div>';
    }
    
    $html .= '</div>';
    
    return new HtmlString($html);
}
```

### 18. **Patrón para Cálculos Financieros**
```php
// ✅ Para mostrar totales y cálculos
TextEntry::make('id')
    ->formatStateUsing(fn ($state, $record) => 
        new HtmlString($this->renderFinancialSummary($record))
    )

private function renderFinancialSummary($record): HtmlString
{
    $subtotal = $record->items->sum('amount');
    $tax = $subtotal * 0.19;
    $total = $subtotal + $tax;
    
    $html = '<div class="space-y-2">';
    $html .= '<div class="flex justify-between"><span>Subtotal:</span><span>' . $this->formatMoney($subtotal) . '</span></div>';
    $html .= '<div class="flex justify-between"><span>Impuestos:</span><span>' . $this->formatMoney($tax) . '</span></div>';
    $html .= '<div class="flex justify-between font-bold"><span>Total:</span><span>' . $this->formatMoney($total) . '</span></div>';
    $html .= '</div>';
    
    return new HtmlString($html);
}
```

## ⚠️ Errores Comunes y Soluciones

### 19. **Error: "No se ve contenido"**
**Causas más comunes:**
1. **HTML escapado**: No usar `HtmlString`
2. **Campo inexistente**: Usar campo que no existe en el modelo
3. **Error silencioso**: Error PHP dentro de `formatStateUsing`

**Solución:**
```php
// ✅ Debugging completo
TextEntry::make('id')
    ->formatStateUsing(function ($state, $record) {
        try {
            $content = $this->generateContent($record);
            return new HtmlString($content);
        } catch (\Exception $e) {
            Log::error('Error en formatStateUsing', ['error' => $e->getMessage()]);
            return new HtmlString('<div class="text-red-500">Error: ' . e($e->getMessage()) . '</div>');
        }
    })
```

### 20. **Error: "Trait collision"**
**Causa**: Mezclar `InteractsWithForms` y `InteractsWithSchemas`

**Solución:**
```php
// ✅ Para infolists, usar solo InteractsWithSchemas
use Filament\Schemas\Concerns\InteractsWithSchemas;

class MyPage extends ViewRecord
{
    use InteractsWithSchemas;
    
    public function infolist(Schema $schema): Schema
    {
        // ...
    }
}
```

## 📋 Checklist de Implementación

### 21. **Antes de Implementar**
- [ ] ¿Existe el campo en el modelo?
- [ ] ¿Necesito eager loading?
- [ ] ¿Puedo usar `RepeatableEntry` en lugar de HTML personalizado?
- [ ] ¿Estoy usando `HtmlString` para HTML personalizado?

### 22. **Durante la Implementación**
- [ ] Usar `fn ($state, $record) =>` en `formatStateUsing`
- [ ] Aplicar `columnSpanFull()` para layout de una columna
- [ ] Manejar casos vacíos (sin datos)
- [ ] Usar `e()` para escapar contenido dinámico

### 23. **Después de Implementar**
- [ ] Ejecutar browser tests
- [ ] Verificar que no hay errores de linting
- [ ] Comprobar que el contenido se muestra correctamente
- [ ] Verificar performance (no N+1 queries)

## 🎨 Estilos y Diseño

### 24. **Colores Temáticos**
```php
// ✅ Usar colores consistentes por sección
private function renderKits($record): HtmlString
{
    $html = '<div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200">';
    // Contenido azul para kits
    return new HtmlString($html);
}

private function renderProducts($record): HtmlString
{
    $html = '<div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200">';
    // Contenido verde para productos
    return new HtmlString($html);
}
```

### 25. **Responsive Design**
```php
// ✅ Usar clases Tailwind responsivas
$html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';
// Contenido responsivo
```

## 🔒 Seguridad

### 26. **Escape de Contenido**
```php
// ✅ SIEMPRE escapar contenido dinámico
$html .= '<h4>' . e($item->name) . '</h4>'; // ✅ Correcto
$html .= '<h4>' . $item->name . '</h4>';    // ❌ Vulnerable a XSS
```

### 27. **Validación de Datos**
```php
// ✅ Validar datos antes de usar
TextEntry::make('id')
    ->formatStateUsing(fn ($state, $record) => {
        if (!$record || !$record->exists) {
            return new HtmlString('<div class="text-gray-500">Sin datos</div>');
        }
        
        return new HtmlString($this->renderContent($record));
    })
```

## 🎯 Aprendizajes Clave de RepeatableEntry

### 28. **Lección Principal: APIs Separadas**
**La clave del éxito**: Filament 4 tiene APIs completamente separadas:
- `Filament\Schemas` para formularios y páginas
- `Filament\Infolists` para mostrar datos (ViewRecord)

**RepeatableEntry pertenece a Infolists**, por lo que solo acepta componentes de esa API.

### 29. **Resultado Visual con RepeatableEntry**
```php
// ✅ Resultado: Cards nativas de Filament 4 con:
RepeatableEntry::make('groups')
    ->schema([
        TextEntry::make('name')->weight(FontWeight::Bold)->color('primary'),
        TextEntry::make('id')->badge()->color('gray'),
        TextEntry::make('total_amount')->formatStateUsing(...)->color('success'),
    ])
    ->columns(1) // Layout vertical
    // ✅ Automáticamente incluye:
    // - Layout de cards
    // - Espaciado consistente
    // - Colores temáticos
    // - Tipografía nativa
    // - Badges y badges de color
    // - Responsive design
    // - Dark mode
```

### 30. **Eliminación de HTML Personalizado**
```php
// ❌ ANTES - HTML personalizado complejo
TextEntry::make('id')
    ->formatStateUsing(fn () => new HtmlString($this->renderKitsSummary()->toHtml()))

// ✅ DESPUÉS - Componentes nativos simples
RepeatableEntry::make('groups')
    ->schema([
        TextEntry::make('name'),
        TextEntry::make('id'),
        TextEntry::make('total_amount')->formatStateUsing(...),
    ])
```

**Beneficios**:
- ✅ Código más limpio y mantenible
- ✅ UX visualmente superior
- ✅ Consistencia con el diseño de Filament
- ✅ Menos bugs y errores
- ✅ Mejor performance

---

## 📚 Recursos Adicionales

- [Documentación Oficial de Filament 4](https://filamentphp.com/docs)
- [Infolists Documentation](https://filamentphp.com/docs/infolists)
- [TextEntry Documentation](https://filamentphp.com/docs/infolists/text-entry)
- [RepeatableEntry Documentation](https://filamentphp.com/docs/infolists/repeatable-entry)

---

**Última actualización**: Diciembre 2024  
**Versión de Filament**: 4.x  
**Autor**: Agente Cursor - Aprendizajes de implementación práctica