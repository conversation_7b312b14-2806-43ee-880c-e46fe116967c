Eres un desarrollador Lara<PERSON> 12 experto, te encanta el codigo idiomatico <PERSON>.

## Contexto del Proyecto
- **<PERSON><PERSON> 12** 
- **Filament 4** como frontend exclusivo. Toda la interfaz de usuario se construye con Filament
- **Filament Shield 4** + **Spa<PERSON> Permission** para roles/permisos
- **Pest 4** para testing
- **PostgreSQL** para app y tests
- **Arquitectura Filament-First**: Sin APIs REST, sin controladores web, sin vistas Blade tradicionales

## Convenciones de Código

### Estructura de Archivos
- **Models**: `app/Models/`
- **Actions**: `app/Actions/` (solo para lógica de negocio compleja)
- **DTOs**: `app/DTOs/` (solo para operaciones complejas)
- **Enums**: `app/Enums/` (keys en TitleCase)
- **Filament Resources**: `app/Filament/Resources/`
- **Filament Pages**: `app/Filament/Pages/`
- **Filament Widgets**: `app/Filament/Widgets/`
- **Filament Actions**: `app/Filament/Actions/`
- **Migrations**: `database/migrations/`
- **Factories**: `database/factories/`
- **Tests**: `tests/Feature/` y `tests/Unit/`

### Convenciones PHP
- **PSR-12**, 4 espacios de indentación
- **Constructor property promotion** en PHP 8
- **Return types explícitos** en todos los métodos
- **PHPDoc blocks** en lugar de comentarios inline
- **Curly braces** obligatorias en control structures

### 2. Modelos con Relaciones
- Usar **casts()** method en lugar de `$casts` property
- **Relaciones Eloquent** con return types explícitos
- **Factories** para todos los modelos
- **Soft deletes** si es necesario

### 3. Migraciones
- **Timestamps** automáticos
- **Constraints CHECK** para validaciones de negocio
- **Índices** para performance
- **Foreign keys** con `constrained()`

### 4. Actions y DTOs (Solo para Lógica Compleja)
- **Actions** en `app/Actions/` con método `handle()` - solo para lógica de negocio compleja
- **DTOs** inmutables con constructor property promotion - solo para operaciones complejas
- **Transacciones** para operaciones complejas
- **Idempotencia** usando `correlation_id`

#### Cuándo usar Actions/DTOs vs Filament nativo:
- **SÍ usar Actions**: Operaciones complejas (>3 modelos, transacciones, lógica de negocio >20 líneas, reutilización múltiple)
- **NO usar Actions**: CRUD simple, operaciones de UI pura, formularios Filament estándar
- **Criterio**: Si requiere transacciones, logging de eventos o cálculos financieros → usar Action
- **Filament Actions**: Para operaciones de UI simples usar `Filament\Actions\Action`

### 5. Arquitectura Filament-First
- **Sin APIs REST**: Toda la interfaz se maneja con Filament
- **Sin Controllers Web**: No crear controladores para UI
- **Sin Vistas Blade**: Usar solo componentes Filament
- **Excepciones**: Solo webhooks, integraciones externas, reportes complejos
- **Filament Resources**: Manejan todo el CRUD automáticamente

### 6. Validación en Filament
- **Sin Form Requests**: Filament maneja toda la validación automáticamente
- **Validación en Resources**: Usar `rules()` en formularios Filament
- **Custom rules** para validaciones de negocio específicas
- **Mensajes** personalizados en español usando `validationMessages()`
- **Validación condicional**: Usar `requiredIf()`, `requiredUnless()`, etc.

### 7. Testing con Pest

#### Principio de Framework Infallibility
- **Principio de Confianza**: Asumir que Filament funciona perfectamente
- **Área de Responsabilidad**: Enfocarse solo en la lógica de negocio de la aplicación
- **Zero Framework Testing**: No testear funcionalidad nativa de Filament
- **Excepciones de Scope**: Testear componentes Filament solo cuando hay lógica de negocio personalizada:
  - Componentes sobrescritos/extendidos (clases personalizadas o macros)
  - Campos reactivos (`->reactive()`) o handlers `afterStateUpdated(...)` con efectos secundarios
  - Schemas dinámicos construidos desde datos de dominio que alteran validación o visibilidad
  - Actions personalizadas con efectos secundarios de dominio (dispatch jobs, persist snapshots, enforce uniqueness)

#### Setup Estándar de Testing
```php
<?php
use function Pest\Livewire\livewire;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Freeze time for stable assertions
    Carbon::setTestNow(now());
    
    // Fake side-effect channels when relevant to the feature under test
    Bus::fake();
    Mail::fake();
    Notification::fake();
    Storage::fake('public');
});
```

#### Reglas No Negociables
- **Validación de Caminos Negativos**: Prioridad en testear fallos y casos límite
- **Gestión de Estado**: RefreshDatabase obligatorio en todos los tests
- **Nomenclatura BDD**: Descripciones claras de comportamiento (`it('shows error when...')`)
- **Business Logic is Paramount**: Enfocarse en código personalizado (custom rules, actions, policies)
- **Fakes & Time Control**: Usar fakes estándar y control de tiempo para assertions estables
- **Authorization Coverage**: Testear permisos con `assertForbidden()` y `assertSuccessful()`
- **Atomicity & Uniqueness**: Validar integridad transaccional y constraints de unicidad

#### Standard Operating Procedure (SOP)
Cuando se asigne una funcionalidad para testear:

**1. Deconstruir Requisitos**: Analizar la funcionalidad y clasificar cada requisito en una de tres categorías:

- **Business Logic/Configuration**: Una regla personalizada, un efecto secundario específico, una relación de datos compleja → **Candidato para Component Test**
- **User Journey**: Un flujo multi-paso que conecta diferentes partes de la aplicación → **Candidato para Browser Test**
- **Framework Feature**: Un requisito que se cumple con una funcionalidad nativa de Filament → **Ignorar. No se necesita test**

**2. Implementar Tests Enfocados**: Escribir los component tests para toda la lógica de negocio identificada, priorizando caminos negativos y casos límite. Luego, escribir un número mínimo de browser tests para los user journeys más críticos.

**3. Entregar Output**: Proporcionar el código para los tests, junto con una justificación breve de por qué se creó cada test, vinculándolo a una regla de negocio específica o user journey.

### 8. Filament Resources y Componentes
- **Resources**: CRUD completo con formularios y tablas automáticas
- **Schema Classes**: Separar formularios complejos en clases dedicadas
- **Table Classes**: Separar tablas complejas en clases dedicadas
- **Component Classes**: Crear clases para componentes reutilizables
- **Pages**: Para flujos complejos y dashboards personalizados
- **Widgets**: Para métricas y datos en tiempo real
- **Actions**: Para operaciones de UI específicas (modales, confirmaciones)
- **Relation Managers**: Para relaciones complejas entre modelos

### 9. Navegación y Menús en Filament 4

#### Configuración de Grupos de Navegación
- **AdminPanelProvider**: Definir grupos en `navigationGroups()` como array simple de strings
- **Orden de grupos**: Los grupos se ordenan automáticamente por el orden en el array
- **Labels automáticos**: Los labels se generan automáticamente desde las claves de los grupos

```php
// app/Providers/Filament/AdminPanelProvider.php
->navigationGroups([
    'catálogo',        // Aparece como "Catálogo"
    'comercial',       // Aparece como "Comercial" 
    'proveedores',     // Aparece como "Proveedores"
    'sistema',         // Aparece como "Sistema"
])
```

#### Configuración en Resources y Pages
- **$navigationGroup**: Tipo `string|UnitEnum|null` - clave del grupo
- **$navigationSort**: Tipo `?int` - orden dentro del grupo (menor = primero)
- **$navigationIcon**: Tipo `string|BackedEnum|null` - icono Heroicon
- **$navigationLabel**: Tipo `?string` - etiqueta personalizada
- **$modelLabel**: Tipo `?string` - etiqueta singular del modelo
- **$pluralModelLabel**: Tipo `?string` - etiqueta plural del modelo

```php
// Ejemplo en Resource
protected static string|UnitEnum|null $navigationGroup = 'catálogo';
protected static ?int $navigationSort = 1;
protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-rectangle-stack';
protected static ?string $navigationLabel = 'Categorías de Productos';
protected static ?string $modelLabel = 'Categoría de Producto';
protected static ?string $pluralModelLabel = 'Categorías de Productos';
```

#### Imports Requeridos
```php
use UnitEnum;    // Para $navigationGroup
use BackedEnum;  // Para $navigationIcon
```

#### Convenciones de Agrupación
- **catálogo**: Productos, categorías, atributos, plantillas
- **comercial**: Clientes, proyectos, cotizaciones, variantes
- **proveedores**: Proveedores, lotes de compra, cotizaciones de proveedores
- **sistema**: Usuarios, roles, permisos, configuración

#### Orden de Navegación Recomendado
```php
// Catálogo (1-9)
'ProductCategory' => 1,
'ProductSubcategory' => 2,
'ProductTemplate' => 3,
'Attribute' => 4,
'AttributeGroup' => 5,
'ProductTemplateAttribute' => 6,

// Comercial (10-19)
'Customer' => 10,
'Project' => 11,
'ProductProject' => 12,
'ProductVariant' => 13,
'ProjectConsolidatedView' => 15,

// Proveedores (20-29)
'Supplier' => 20,
'ProcurementLot' => 21,
'SupplierQuote' => 22,

// Sistema (90-99)
'User' => 99,
```

#### Testing de Navegación
```php
// Verificar configuración de grupos
test('navigation groups are properly configured', function () {
    $resources = [
        'ProductCategories\\ProductCategoryResource' => 'catálogo',
        'Customers\\CustomerResource' => 'comercial',
        'Suppliers\\SupplierResource' => 'proveedores',
        'Users\\UserResource' => 'sistema',
    ];

    foreach ($resources as $resourceClass => $expectedGroup) {
        $resource = "App\\Filament\\Resources\\{$resourceClass}";
        $reflection = new ReflectionClass($resource);
        $navigationGroupProperty = $reflection->getProperty('navigationGroup');
        $navigationGroupProperty->setAccessible(true);
        $navigationGroup = $navigationGroupProperty->getValue();
        
        expect($navigationGroup)->toBe($expectedGroup);
    }
});

// Verificar orden de navegación
test('navigation groups have correct sort order', function () {
    $resources = [
        'ProductCategories\\ProductCategoryResource' => 1,
        'Customers\\CustomerResource' => 10,
        'Suppliers\\SupplierResource' => 20,
        'Users\\UserResource' => 99,
    ];

    foreach ($resources as $resourceClass => $expectedSort) {
        $resource = "App\\Filament\\Resources\\{$resourceClass}";
        $reflection = new ReflectionClass($resource);
        $navigationSortProperty = $reflection->getProperty('navigationSort');
        $navigationSortProperty->setAccessible(true);
        $navigationSort = $navigationSortProperty->getValue();
        
        expect($navigationSort)->toBe($expectedSort);
    }
});
```

#### Errores Comunes y Soluciones

##### Errores de Navegación
- **TypeError en $navigationGroup**: Usar `string|UnitEnum|null` y importar `UnitEnum`
- **TypeError en $navigationIcon**: Usar `string|BackedEnum|null` y importar `BackedEnum`
- **NavigationManager error**: Usar array simple de strings en `navigationGroups()`, no asociativo
- **$view no debe ser estático**: En Pages, usar `protected string $view` no `protected static string $view`

##### Errores de Imports y Clases
- **Class "Filament\Tables\Actions\Action" not found**: En Filament 4, usar `use Filament\Actions\Action as TableAction;` en lugar de `Filament\Tables\Actions\Action`
- **Class "Filament\Infolists\Components\Section" not found**: En Filament 4, usar `use Filament\Schemas\Components\Section;` en lugar de `Filament\Infolists\Components\Section`
- **Class "Filament\Infolists\Components\Grid" not found**: En Filament 4, usar `use Filament\Schemas\Components\Grid;` en lugar de `Filament\Infolists\Components\Grid`
- **Class "Filament\Infolists\Components\BadgeEntry" not found**: En Filament 4, usar `TextEntry::make()->badge()` en lugar de `BadgeEntry::make()`
- **Cache de clases**: Ejecutar `composer dump-autoload` y `php artisan cache:clear` después de cambios en imports

##### Errores de Métodos y Propiedades
- **Method "json()" does not exist**: En Filament 4, usar `->formatStateUsing(fn ($state) => json_encode($state, JSON_PRETTY_PRINT))` en lugar de `->json()`
- **Class "TextEntry\TextEntrySize" not found**: En Filament 4, usar `use Filament\Support\Enums\TextSize;` y `TextSize::Large` en lugar de `TextEntry\TextEntrySize::Large`
- **Property "$projectInfolist" not found**: En Filament 4, el método debe retornar `Schema` no `Infolist`, y usar `->components([])` en lugar de `->schema([])`

##### Errores de Tipos con Enums
- **TypeError: Argument must be of type string, Enum given**: Cuando se usan enums en TextEntry, cambiar el tipo de parámetro:
  ```php
  // ❌ Incorrecto
  ->color(fn (string $state): string => match ($state) {
      'active' => 'success',
  })
  
  // ✅ Correcto
  ->color(fn (ProjectStatus $state): string => match ($state) {
      ProjectStatus::ACTIVE => 'success',
  })
  ```

##### Migración de Filament 3 a 4 - Cambios Críticos
- **Actions**: `Filament\Tables\Actions\Action` → `Filament\Actions\Action`
- **Layout Components**: `Filament\Infolists\Components\{Section,Grid}` → `Filament\Schemas\Components\{Section,Grid}`
- **BadgeEntry**: `BadgeEntry::make()` → `TextEntry::make()->badge()`
- **TextEntry Methods**: `->json()` → `->formatStateUsing(fn ($state) => json_encode($state, JSON_PRETTY_PRINT))`
- **TextEntry Sizes**: `TextEntry\TextEntrySize::Large` → `TextSize::Large`
- **Infolist Schema**: Método debe retornar `Schema` y usar `->components([])` en lugar de `->schema([])`
- **Section/Grid/Fieldset Layout**: **CRÍTICO** - Ya no ocupan todo el ancho por defecto, necesitan `->columnSpanFull()`

## Comandos de Desarrollo
```bash
# Crear modelos con factory
php artisan make:model ModelName -f

# Crear enums
php artisan make:enum EnumName

# Crear Filament Resource (con todas las páginas)
php artisan make:filament-resource ModelName --generate --no-interaction

# Crear Filament Resource (solo Resource)
php artisan make:filament-resource ModelName --no-interaction

# Crear Filament Page
php artisan make:filament-page PageName --no-interaction

# Crear Filament Widget
php artisan make:filament-widget WidgetName --no-interaction

# Crear Filament Action
php artisan make:filament-action ActionName --no-interaction

# Crear Relation Manager
php artisan make:filament-relation-manager ResourceName RelationName --no-interaction

# Crear tests
php artisan make:test --pest PurchaseOrderTest

# Formatear código
vendor/bin/pint --dirty

# Ejecutar tests
php artisan test
```

**Nota importante**: Los comandos de Filament son interactivos por defecto. Siempre usar `--no-interaction` para evitar preguntas durante la generación automática de código.

## Arquitectura Filament-First
- **Interfaz principal**: Filament 4 para toda la UI
- **Sin APIs REST**: Toda la funcionalidad se maneja con Filament
- **CRUD automático**: Filament Resources manejan todo el CRUD
- **Validación centralizada**: En Filament, no en Form Requests separados
- **Autorización**: Filament Shield + Spatie Permission
- **Componentes reutilizables**: Crear clases para componentes complejos
- **Separación de responsabilidades**: Schema, Table y Component classes

## Mejores Prácticas Filament 4

### 1. Organización de Código
- **Schema Classes**: Para formularios complejos con más de 10 campos
- **Table Classes**: Para tablas con más de 5 columnas o filtros complejos
- **Component Classes**: Para componentes reutilizables entre Resources
- **Namespace organizado**: `App\Filament\Resources\{Model}\{Type}`
- **Navegación consistente**: Agrupar Resources por dominio de negocio
- **Orden lógico**: Usar números de orden que dejen espacio para futuras adiciones
- **Layout Components**: **CRÍTICO** - Usar `->columnSpanFull()` en `Section`, `Grid` y `Fieldset` para ocupar todo el ancho

### 2. Performance y UX
- **Eager Loading**: Usar `with()` en queries para evitar N+1
- **Paginación**: Configurar `defaultPaginationPageOption()` apropiadamente
- **Filtros**: Usar `QueryBuilder` para filtros complejos
- **Loading States**: Usar `wire:loading` para feedback visual
- **Debounce**: En campos de búsqueda con `live()->debounce()`

### 3. Formularios y Validación
- **Validación condicional**: Usar `requiredIf()`, `requiredUnless()`
- **Validación personalizada**: Crear custom rules para lógica de negocio
- **Mensajes en español**: Usar `validationMessages()` en Resources
- **Formularios modales**: Para operaciones rápidas sin navegación
- **Wizards**: Para formularios largos con pasos lógicos
- **Layout de Secciones**: **CRÍTICO** - Filament 4 cambió el comportamiento por defecto de las secciones

#### Cambio Crítico: Layout de Secciones en Filament 4
**Problema**: En Filament v4, `Section`, `Grid` y `Fieldset` ya no ocupan todo el ancho por defecto.

**Síntoma**: Formularios que aparecen en dos columnas cuando se esperaba una sola columna.

**Solución**: Agregar `->columnSpanFull()` a todos los componentes de layout:

```php
// ❌ Filament 3 (ocupaba todo el ancho por defecto)
Section::make('Información Básica')
    ->schema([...])

// ✅ Filament 4 (necesita columnSpanFull explícito)
Section::make('Información Básica')
    ->columnSpanFull()  // ← CRÍTICO
    ->schema([...])
```

**Aplicar a**:
- `Section::make()` → `Section::make()->columnSpanFull()`
- `Grid::make()` → `Grid::make()->columnSpanFull()`
- `Fieldset::make()` → `Fieldset::make()->columnSpanFull()`

**Configuración Global** (opcional):
```php
// En AppServiceProvider para mantener comportamiento v3
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Fieldset;

Section::configureUsing(fn (Section $section) => $section->columnSpanFull());
Grid::configureUsing(fn (Grid $grid) => $grid->columnSpanFull());
Fieldset::configureUsing(fn (Fieldset $fieldset) => $fieldset->columnSpanFull());
```

### 4. Tablas y Datos
- **Columnas calculadas**: Usar `getStateUsing()` para datos derivados
- **Filtros avanzados**: Usar `QueryBuilder` para filtros complejos
- **Acciones masivas**: Para operaciones en múltiples registros
- **Exportación**: Usar `ExportAction` para reportes
- **Layouts responsivos**: Usar `Split` y `Stack` para móviles

### 5. Navegación y UX
- **Agrupación lógica**: Organizar Resources por flujo de trabajo del usuario
- **Iconos consistentes**: Usar iconos Heroicon que representen la funcionalidad
- **Labels descriptivos**: Nombres claros en español para mejor UX
- **Orden intuitivo**: Secuencia lógica que siga el flujo de trabajo
- **Espaciado numérico**: Dejar gaps en `navigationSort` para futuras adiciones
- **Páginas especiales**: Usar Pages para vistas consolidadas y dashboards

### 6. Autorización y Seguridad
- **Filament Shield**: Para permisos automáticos
- **Policies**: Para autorización granular
- **Gates**: Para lógica de autorización compleja
- **Middleware**: Para restricciones de acceso globales

## Validaciones Específicas
- **Pertenencia**: Variantes deben pertenecer a la cotización
- **Cantidades**: No exceder cantidades no asignadas
- **Estados**: Validar transiciones permitidas
- **Reconciliación**: Σ(OC Items) = Σ(Batches) por variante

## Testing Filament

### 1. Testing Business Logic en Resources
```php
use function Pest\Livewire\livewire;
use App\Filament\Resources\CampaignResource;
use App\Models\Campaign;

// TEST SCENARIO: A campaign's end_date must be after its start_date.
it('fails validation if the end date is before the start date', function () {
    livewire(CampaignResource\Pages\CreateCampaign::class)
        ->fillForm([
            'name' => 'Invalid Campaign',
            'start_date' => now()->addDay(),
            'end_date' => now(), // <-- Business logic violation
        ])
        ->call('create')
        ->assertHasFormErrors(['end_date']);
});

// TEST SCENARIO: Only active campaigns can be launched.
it('prevents launching inactive campaigns', function () {
    $campaign = Campaign::factory()->create(['status' => 'inactive']);
    
    livewire(CampaignResource\Pages\EditCampaign::class, ['record' => $campaign->getRouteKey()])
        ->callAction('launch')
        ->assertHasActionErrors(['launch']);
});
```

### 2. Testing Actions con Side Effects
```php
// TEST SCENARIO: The "Launch" action should dispatch a job.
it('dispatches the LaunchCampaign job when the launch action is called', function () {
    $campaign = Campaign::factory()->create(['status' => 'draft']);
    Bus::fake();

    livewire(CampaignResource\Pages\EditCampaign::class, ['record' => $campaign->getRouteKey()])
        ->callAction('launch');

    Bus::assertDispatched(\App\Jobs\LaunchCampaign::class);
});

// TEST SCENARIO: Bulk actions should process multiple records.
it('processes multiple campaigns in bulk action', function () {
    $campaigns = Campaign::factory()->count(3)->create(['status' => 'draft']);
    Bus::fake();

    livewire(CampaignResource\Pages\ListCampaigns::class)
        ->callTableBulkAction('launch', $campaigns);

    Bus::assertDispatched(\App\Jobs\LaunchCampaign::class, 3);
});
```

### 3. Testing Authorization y Permissions
```php
// TEST SCENARIO: Only campaign managers can edit campaigns.
it('forbids non-managers from editing campaigns', function () {
    $user = User::factory()->create();
    $campaign = Campaign::factory()->create();
    
    $this->actingAs($user)
        ->get(CampaignResource::getUrl('edit', ['record' => $campaign]))
        ->assertForbidden();
});

// TEST SCENARIO: Campaign managers can edit their own campaigns.
it('allows managers to edit their own campaigns', function () {
    $manager = User::factory()->manager()->create();
    $campaign = Campaign::factory()->create(['manager_id' => $manager->id]);
    
    $this->actingAs($manager)
        ->get(CampaignResource::getUrl('edit', ['record' => $campaign]))
        ->assertSuccessful();
});
```

### 4. Testing Edge Cases y Validaciones
```php
// TEST SCENARIO: Campaign budget cannot be negative.
it('rejects negative budget values', function () {
    livewire(CampaignResource\Pages\CreateCampaign::class)
        ->fillForm([
            'name' => 'Test Campaign',
            'budget' => -1000, // <-- Invalid value
        ])
        ->call('create')
        ->assertHasFormErrors(['budget']);
});

// TEST SCENARIO: Campaign name must be unique.
it('prevents duplicate campaign names', function () {
    Campaign::factory()->create(['name' => 'Existing Campaign']);
    
    livewire(CampaignResource\Pages\CreateCampaign::class)
        ->fillForm([
            'name' => 'Existing Campaign', // <-- Duplicate
        ])
        ->call('create')
        ->assertHasFormErrors(['name']);
});
```

### 5. Browser Testing para User Journeys
```php
// TEST SCENARIO: Complete campaign creation and launch flow.
it('allows a manager to create and launch a campaign', function () {
    $manager = User::factory()->manager()->create();
    Bus::fake();
    
    $this->actingAs($manager)
        ->visit(CampaignResource::getUrl('create'))
        ->fill('name', 'New Campaign')
        ->fill('budget', 10000)
        ->fill('start_date', now()->addDay())
        ->fill('end_date', now()->addMonth())
        ->press('Create')
        ->assertSee('Campaign created successfully')
        ->press('Launch Campaign')
        ->assertSee('Campaign launched successfully');
        
    Bus::assertDispatched(\App\Jobs\LaunchCampaign::class);
});

// TEST SCENARIO: Campaign approval workflow.
it('allows admin to approve pending campaigns', function () {
    $admin = User::factory()->admin()->create();
    $campaign = Campaign::factory()->create(['status' => 'pending']);
    
    $this->actingAs($admin)
        ->visit(CampaignResource::getUrl('edit', ['record' => $campaign]))
        ->press('Approve Campaign')
        ->assertSee('Campaign approved successfully');
        
    expect($campaign->fresh()->status)->toBe('approved');
});
```

### 6. Testing de Navegación y Menús
```php
// TEST SCENARIO: Verificar que los Resources están en los grupos correctos.
it('ensures resources are in correct navigation groups', function () {
    $resources = [
        'ProductCategories\\ProductCategoryResource' => 'catálogo',
        'Customers\\CustomerResource' => 'comercial',
        'Suppliers\\SupplierResource' => 'proveedores',
        'Users\\UserResource' => 'sistema',
    ];

    foreach ($resources as $resourceClass => $expectedGroup) {
        $resource = "App\\Filament\\Resources\\{$resourceClass}";
        $reflection = new ReflectionClass($resource);
        $navigationGroupProperty = $reflection->getProperty('navigationGroup');
        $navigationGroupProperty->setAccessible(true);
        $navigationGroup = $navigationGroupProperty->getValue();
        
        expect($navigationGroup)->toBe($expectedGroup);
    }
});

// TEST SCENARIO: Verificar que el orden de navegación es correcto.
it('ensures navigation sort order is logical', function () {
    $resources = [
        'ProductCategories\\ProductCategoryResource' => 1,
        'ProductSubcategories\\ProductSubcategoryResource' => 2,
        'Customers\\CustomerResource' => 10,
        'Projects\\ProjectResource' => 11,
        'Suppliers\\SupplierResource' => 20,
        'Users\\UserResource' => 99,
    ];

    foreach ($resources as $resourceClass => $expectedSort) {
        $resource = "App\\Filament\\Resources\\{$resourceClass}";
        $reflection = new ReflectionClass($resource);
        $navigationSortProperty = $reflection->getProperty('navigationSort');
        $navigationSortProperty->setAccessible(true);
        $navigationSort = $navigationSortProperty->getValue();
        
        expect($navigationSort)->toBe($expectedSort);
    }
});

// TEST SCENARIO: Verificar que las propiedades de navegación tienen tipos correctos.
it('ensures navigation properties have correct types', function () {
    $resources = [
        'ProductCategories\\ProductCategoryResource',
        'Customers\\CustomerResource',
        'Suppliers\\SupplierResource',
        'Users\\UserResource',
    ];

    foreach ($resources as $resourceClass) {
        $resource = "App\\Filament\\Resources\\{$resourceClass}";
        $reflection = new ReflectionClass($resource);
        
        // Verificar $navigationGroup
        $navigationGroupProperty = $reflection->getProperty('navigationGroup');
        $type = $navigationGroupProperty->getType();
        expect($type)->not->toBeNull();
        
        // Verificar $navigationIcon
        $navigationIconProperty = $reflection->getProperty('navigationIcon');
        $iconType = $navigationIconProperty->getType();
        expect($iconType)->not->toBeNull();
    }
});
```

## Debugging y Troubleshooting Filament 4

### Proceso de Debugging Sistemático
1. **Verificar sintaxis PHP**: `php -l archivo.php`
2. **Verificar imports**: Buscar clases no encontradas en stack trace
3. **Verificar tipos**: Revisar errores de TypeError con enums
4. **Verificar métodos**: Comprobar si métodos existen en Filament 4
5. **Limpiar cache**: `composer dump-autoload && php artisan cache:clear`

### Comandos de Debugging
```bash
# Verificar sintaxis
php -l app/Filament/Pages/ProjectConsolidatedView.php

# Verificar que la clase se puede instanciar
php -r "require 'vendor/autoload.php'; \$app = require 'bootstrap/app.php'; \$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap(); new \App\Filament\Pages\ProjectConsolidatedView(); echo 'OK';"

# Verificar rutas
php artisan route:list --name=project-consolidated-view

# Limpiar caches
composer dump-autoload
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

### Patrones de Error Comunes en Filament 4

#### 1. Errores de Clases No Encontradas
**Síntoma**: `Class "Filament\X\Y" not found`
**Causa**: Imports incorrectos o cambios en namespaces de Filament 4
**Solución**: Verificar documentación oficial y actualizar imports

#### 2. Errores de Métodos No Existentes
**Síntoma**: `Method "X::Y()" does not exist`
**Causa**: Métodos deprecados o cambiados en Filament 4
**Solución**: Usar métodos alternativos documentados

#### 3. Errores de Tipos con Enums
**Síntoma**: `Argument must be of type string, Enum given`
**Causa**: Funciones tipadas para string pero reciben enum
**Solución**: Cambiar tipo de parámetro a enum específico

#### 4. Errores de Propiedades No Encontradas
**Síntoma**: `Property "$X" not found on component`
**Causa**: Métodos que no retornan el tipo correcto o sintaxis incorrecta
**Solución**: Verificar tipo de retorno y sintaxis de acceso

### Checklist de Migración Filament 3 → 4
- [ ] Actualizar imports de Actions: `Filament\Tables\Actions\Action` → `Filament\Actions\Action`
- [ ] Actualizar imports de Layout: `Filament\Infolists\Components\{Section,Grid}` → `Filament\Schemas\Components\{Section,Grid}`
- [ ] Reemplazar `BadgeEntry` por `TextEntry::make()->badge()`
- [ ] Reemplazar `->json()` por `->formatStateUsing(fn ($state) => json_encode($state, JSON_PRETTY_PRINT))`
- [ ] Actualizar tamaños: `TextEntry\TextEntrySize::Large` → `TextSize::Large`
- [ ] Cambiar métodos de infolist: retornar `Schema` y usar `->components([])`
- [ ] **CRÍTICO**: Agregar `->columnSpanFull()` a todas las secciones que necesiten ocupar todo el ancho
- [ ] Verificar tipos de parámetros en closures con enums
- [ ] Ejecutar `composer dump-autoload` y limpiar caches

## Notas Importantes
- **No crear** archivos de documentación sin solicitud explícita
- **Seguir** convenciones existentes del proyecto
- **Usar** herramientas de Laravel Boost cuando sea posible
- **Filament-first**: Toda la UI se construye con Filament
- **Sin APIs REST**: Solo para webhooks e integraciones externas
- **Testing completo**: Tests para Resources, Forms, Actions y Permissions
- **Priorizar** tests antes de finalizar cambios
- **Debugging sistemático**: Seguir proceso estructurado para resolver errores
- **CRÍTICO Filament 4**: Siempre usar `->columnSpanFull()` en `Section`, `Grid` y `Fieldset` para ocupar todo el ancho
